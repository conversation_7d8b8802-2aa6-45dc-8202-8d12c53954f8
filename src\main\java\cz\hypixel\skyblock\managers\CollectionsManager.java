package cz.hypixel.skyblock.managers;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.database.PlayerData;
import cz.hypixel.skyblock.data.CollectionData;
import cz.hypixel.skyblock.data.CollectionTier;
import org.bukkit.Material;
import org.bukkit.entity.Player;

import java.util.*;
import java.util.logging.Logger;

/**
 * Manager pro správu systému kolekcí podle Hypixel SkyBlock
 */
public class CollectionsManager {

    private final HypixelSkyBlockCZ plugin;
    private final Logger logger;
    private final Map<String, CollectionData> collections;
    private final Map<String, String> itemToCollection;

    public CollectionsManager(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.collections = new HashMap<>();
        this.itemToCollection = new HashMap<>();
        
        initializeCollections();
        logger.info("CollectionsManager inicializován s " + collections.size() + " kolekcemi");
    }

    /**
     * Inicializuje všechny kolekce podle Hypixel SkyBlock Wiki
     */
    private void initializeCollections() {
        // Farming Collections
        initializeFarmingCollections();
        
        // Mining Collections
        initializeMiningCollections();
        
        // Combat Collections
        initializeCombatCollections();
        
        // Foraging Collections
        initializeForagingCollections();
        
        // Fishing Collections
        initializeFishingCollections();
        
        // Boss Collections
        initializeBossCollections();
        
        // Rift Collections
        initializeRiftCollections();
    }

    /**
     * Inicializuje Farming kolekce
     */
    private void initializeFarmingCollections() {
        // Cactus Collection
        CollectionData cactus = new CollectionData("CACTUS", "Farming", Material.CACTUS);
        cactus.addTier(new CollectionTier(1, 100, Arrays.asList("Cactus Minion I Recipe", "+4 SkyBlock XP")));
        cactus.addTier(new CollectionTier(2, 250, Arrays.asList("Cactus Armor Recipes", "+4 SkyBlock XP")));
        cactus.addTier(new CollectionTier(3, 500, Arrays.asList("Resistance Potion Recipe", "+4 SkyBlock XP")));
        cactus.addTier(new CollectionTier(4, 1000, Arrays.asList("Enchanted Cactus Green Recipe", "+4 SkyBlock XP")));
        cactus.addTier(new CollectionTier(5, 2500, Arrays.asList("Desert Island Recipe", "+4 SkyBlock XP")));
        cactus.addTier(new CollectionTier(6, 5000, Arrays.asList("Piercing Exp Discount (-25%)", "+4 SkyBlock XP")));
        cactus.addTier(new CollectionTier(7, 10000, Arrays.asList("Thorns Exp Discount (-25%)", "+4 SkyBlock XP")));
        cactus.addTier(new CollectionTier(8, 25000, Arrays.asList("Enchanted Cactus Recipe", "+4 SkyBlock XP")));
        cactus.addTier(new CollectionTier(9, 50000, Arrays.asList("+5,000 Farming XP", "+4 SkyBlock XP")));
        collections.put("CACTUS", cactus);
        itemToCollection.put("CACTUS", "CACTUS");

        // Carrot Collection
        CollectionData carrot = new CollectionData("CARROT", "Farming", Material.CARROT);
        carrot.addTier(new CollectionTier(1, 100, Arrays.asList("Carrot Minion I Recipe", "+4 SkyBlock XP")));
        carrot.addTier(new CollectionTier(2, 250, Arrays.asList("Simple Carrot Candy Recipe", "+4 SkyBlock XP")));
        carrot.addTier(new CollectionTier(3, 500, Arrays.asList("Carrot Bait Recipe", "+4 SkyBlock XP")));
        carrot.addTier(new CollectionTier(4, 1750, Arrays.asList("Enchanted Carrot Recipe", "+4 SkyBlock XP")));
        carrot.addTier(new CollectionTier(5, 5000, Arrays.asList("Enchanted Carrot On A Stick Recipe", "+4 SkyBlock XP")));
        carrot.addTier(new CollectionTier(6, 10000, Arrays.asList("Great Carrot Candy Recipe", "+4 SkyBlock XP")));
        carrot.addTier(new CollectionTier(7, 25000, Arrays.asList("Enchanted Golden Carrot Recipe", "+4 SkyBlock XP")));
        carrot.addTier(new CollectionTier(8, 50000, Arrays.asList("Superb Carrot Candy Recipe", "+4 SkyBlock XP")));
        carrot.addTier(new CollectionTier(9, 100000, Arrays.asList("+10,000 Farming XP", "+4 SkyBlock XP")));
        collections.put("CARROT", carrot);
        itemToCollection.put("CARROT", "CARROT");

        // Wheat Collection
        CollectionData wheat = new CollectionData("WHEAT", "Farming", Material.WHEAT);
        wheat.addTier(new CollectionTier(1, 50, Arrays.asList("Wheat Minion I Recipe", "+4 SkyBlock XP")));
        wheat.addTier(new CollectionTier(2, 100, Arrays.asList("Enchanted Bread Recipe", "+4 SkyBlock XP")));
        wheat.addTier(new CollectionTier(3, 250, Arrays.asList("Speed Potion Recipe", "+4 SkyBlock XP")));
        wheat.addTier(new CollectionTier(4, 500, Arrays.asList("Enchanted Wheat Recipe", "+4 SkyBlock XP")));
        wheat.addTier(new CollectionTier(5, 1000, Arrays.asList("Farming Island Recipe", "+4 SkyBlock XP")));
        wheat.addTier(new CollectionTier(6, 2500, Arrays.asList("Enchanted Hay Bale Recipe", "+4 SkyBlock XP")));
        wheat.addTier(new CollectionTier(7, 10000, Arrays.asList("+5,000 Farming XP", "+4 SkyBlock XP")));
        wheat.addTier(new CollectionTier(8, 25000, Arrays.asList("Farmer Boots Recipe", "+4 SkyBlock XP")));
        wheat.addTier(new CollectionTier(9, 100000, Arrays.asList("Farmer Orb Recipe", "+4 SkyBlock XP")));
        collections.put("WHEAT", wheat);
        itemToCollection.put("WHEAT", "WHEAT");

        // Potato Collection
        CollectionData potato = new CollectionData("POTATO", "Farming", Material.POTATO);
        potato.addTier(new CollectionTier(1, 100, Arrays.asList("Potato Minion I Recipe", "+4 SkyBlock XP")));
        potato.addTier(new CollectionTier(2, 250, Arrays.asList("Enchanted Potato Recipe", "+4 SkyBlock XP")));
        potato.addTier(new CollectionTier(3, 500, Arrays.asList("Enchanted Baked Potato Recipe", "+4 SkyBlock XP")));
        potato.addTier(new CollectionTier(4, 1750, Arrays.asList("Hot Potato Book Recipe", "+4 SkyBlock XP")));
        potato.addTier(new CollectionTier(5, 5000, Arrays.asList("Potato Spreading Recipe", "+4 SkyBlock XP")));
        potato.addTier(new CollectionTier(6, 10000, Arrays.asList("Enchanted Poisonous Potato Recipe", "+4 SkyBlock XP")));
        potato.addTier(new CollectionTier(7, 25000, Arrays.asList("Potato Talisman Recipe", "+4 SkyBlock XP")));
        potato.addTier(new CollectionTier(8, 50000, Arrays.asList("Potato Ring Recipe", "+4 SkyBlock XP")));
        potato.addTier(new CollectionTier(9, 100000, Arrays.asList("Potato Artifact Recipe", "+4 SkyBlock XP")));
        collections.put("POTATO", potato);
        itemToCollection.put("POTATO", "POTATO");
    }

    /**
     * Inicializuje Mining kolekce
     */
    private void initializeMiningCollections() {
        // Cobblestone Collection
        CollectionData cobblestone = new CollectionData("COBBLESTONE", "Mining", Material.COBBLESTONE);
        cobblestone.addTier(new CollectionTier(1, 50, Arrays.asList("Cobblestone Minion I Recipe", "+4 SkyBlock XP")));
        cobblestone.addTier(new CollectionTier(2, 100, Arrays.asList("Auto Smelter Recipe", "+4 SkyBlock XP")));
        cobblestone.addTier(new CollectionTier(3, 250, Arrays.asList("Compactor Recipe", "+4 SkyBlock XP")));
        cobblestone.addTier(new CollectionTier(4, 1000, Arrays.asList("Enchanted Cobblestone Recipe", "+4 SkyBlock XP")));
        cobblestone.addTier(new CollectionTier(5, 2500, Arrays.asList("Budget Hopper Recipe", "+4 SkyBlock XP")));
        cobblestone.addTier(new CollectionTier(6, 5000, Arrays.asList("Enchanted Hopper Recipe", "+4 SkyBlock XP")));
        cobblestone.addTier(new CollectionTier(7, 10000, Arrays.asList("Super Compactor 3000 Recipe", "+4 SkyBlock XP")));
        cobblestone.addTier(new CollectionTier(8, 25000, Arrays.asList("+5,000 Mining XP", "+4 SkyBlock XP")));
        cobblestone.addTier(new CollectionTier(9, 50000, Arrays.asList("Cobblestone Talisman Recipe", "+4 SkyBlock XP")));
        collections.put("COBBLESTONE", cobblestone);
        itemToCollection.put("COBBLESTONE", "COBBLESTONE");

        // Coal Collection
        CollectionData coal = new CollectionData("COAL", "Mining", Material.COAL);
        coal.addTier(new CollectionTier(1, 50, Arrays.asList("Coal Minion I Recipe", "+4 SkyBlock XP")));
        coal.addTier(new CollectionTier(2, 100, Arrays.asList("Enchanted Coal Recipe", "+4 SkyBlock XP")));
        coal.addTier(new CollectionTier(3, 250, Arrays.asList("Enchanted Charcoal Recipe", "+4 SkyBlock XP")));
        coal.addTier(new CollectionTier(4, 1000, Arrays.asList("Coal Talisman Recipe", "+4 SkyBlock XP")));
        coal.addTier(new CollectionTier(5, 2500, Arrays.asList("Enchanted Block of Coal Recipe", "+4 SkyBlock XP")));
        coal.addTier(new CollectionTier(6, 5000, Arrays.asList("Coal Ring Recipe", "+4 SkyBlock XP")));
        coal.addTier(new CollectionTier(7, 10000, Arrays.asList("Coal Artifact Recipe", "+4 SkyBlock XP")));
        coal.addTier(new CollectionTier(8, 25000, Arrays.asList("+5,000 Mining XP", "+4 SkyBlock XP")));
        coal.addTier(new CollectionTier(9, 50000, Arrays.asList("Coal Relic Recipe", "+4 SkyBlock XP")));
        collections.put("COAL", coal);
        itemToCollection.put("COAL", "COAL");
    }

    /**
     * Inicializuje Combat kolekce
     */
    private void initializeCombatCollections() {
        // Rotten Flesh Collection
        CollectionData rottenFlesh = new CollectionData("ROTTEN_FLESH", "Combat", Material.ROTTEN_FLESH);
        rottenFlesh.addTier(new CollectionTier(1, 50, Arrays.asList("Zombie Minion I Recipe", "+4 SkyBlock XP")));
        rottenFlesh.addTier(new CollectionTier(2, 100, Arrays.asList("Zombie Talisman Recipe", "+4 SkyBlock XP")));
        rottenFlesh.addTier(new CollectionTier(3, 250, Arrays.asList("Enchanted Rotten Flesh Recipe", "+4 SkyBlock XP")));
        rottenFlesh.addTier(new CollectionTier(4, 1000, Arrays.asList("Zombie Ring Recipe", "+4 SkyBlock XP")));
        rottenFlesh.addTier(new CollectionTier(5, 2500, Arrays.asList("Zombie Artifact Recipe", "+4 SkyBlock XP")));
        rottenFlesh.addTier(new CollectionTier(6, 5000, Arrays.asList("Revenant Catalyst Recipe", "+4 SkyBlock XP")));
        rottenFlesh.addTier(new CollectionTier(7, 10000, Arrays.asList("Undead Catalyst Recipe", "+4 SkyBlock XP")));
        rottenFlesh.addTier(new CollectionTier(8, 25000, Arrays.asList("+5,000 Combat XP", "+4 SkyBlock XP")));
        collections.put("ROTTEN_FLESH", rottenFlesh);
        itemToCollection.put("ROTTEN_FLESH", "ROTTEN_FLESH");

        // Bone Collection
        CollectionData bone = new CollectionData("BONE", "Combat", Material.BONE);
        bone.addTier(new CollectionTier(1, 50, Arrays.asList("Skeleton Minion I Recipe", "+4 SkyBlock XP")));
        bone.addTier(new CollectionTier(2, 100, Arrays.asList("Bone Talisman Recipe", "+4 SkyBlock XP")));
        bone.addTier(new CollectionTier(3, 250, Arrays.asList("Enchanted Bone Recipe", "+4 SkyBlock XP")));
        bone.addTier(new CollectionTier(4, 1000, Arrays.asList("Bone Ring Recipe", "+4 SkyBlock XP")));
        bone.addTier(new CollectionTier(5, 2500, Arrays.asList("Bone Artifact Recipe", "+4 SkyBlock XP")));
        bone.addTier(new CollectionTier(6, 5000, Arrays.asList("Wolf Catalyst Recipe", "+4 SkyBlock XP")));
        bone.addTier(new CollectionTier(7, 10000, Arrays.asList("+5,000 Combat XP", "+4 SkyBlock XP")));
        collections.put("BONE", bone);
        itemToCollection.put("BONE", "BONE");
    }

    /**
     * Inicializuje Foraging kolekce
     */
    private void initializeForagingCollections() {
        // Oak Wood Collection
        CollectionData oakWood = new CollectionData("OAK_WOOD", "Foraging", Material.OAK_LOG);
        oakWood.addTier(new CollectionTier(1, 50, Arrays.asList("Oak Minion I Recipe", "+4 SkyBlock XP")));
        oakWood.addTier(new CollectionTier(2, 100, Arrays.asList("Enchanted Oak Wood Recipe", "+4 SkyBlock XP")));
        oakWood.addTier(new CollectionTier(3, 250, Arrays.asList("Wood Talisman Recipe", "+4 SkyBlock XP")));
        oakWood.addTier(new CollectionTier(4, 1000, Arrays.asList("Wood Ring Recipe", "+4 SkyBlock XP")));
        oakWood.addTier(new CollectionTier(5, 2500, Arrays.asList("Wood Artifact Recipe", "+4 SkyBlock XP")));
        oakWood.addTier(new CollectionTier(6, 5000, Arrays.asList("+5,000 Foraging XP", "+4 SkyBlock XP")));
        collections.put("OAK_WOOD", oakWood);
        itemToCollection.put("OAK_LOG", "OAK_WOOD");
    }

    /**
     * Inicializuje Fishing kolekce
     */
    private void initializeFishingCollections() {
        // Raw Fish Collection
        CollectionData rawFish = new CollectionData("RAW_FISH", "Fishing", Material.COD);
        rawFish.addTier(new CollectionTier(1, 50, Arrays.asList("Fishing Minion I Recipe", "+4 SkyBlock XP")));
        rawFish.addTier(new CollectionTier(2, 100, Arrays.asList("Enchanted Raw Fish Recipe", "+4 SkyBlock XP")));
        rawFish.addTier(new CollectionTier(3, 250, Arrays.asList("Fish Talisman Recipe", "+4 SkyBlock XP")));
        rawFish.addTier(new CollectionTier(4, 1000, Arrays.asList("Fish Ring Recipe", "+4 SkyBlock XP")));
        rawFish.addTier(new CollectionTier(5, 2500, Arrays.asList("Fish Artifact Recipe", "+4 SkyBlock XP")));
        rawFish.addTier(new CollectionTier(6, 5000, Arrays.asList("+5,000 Fishing XP", "+4 SkyBlock XP")));
        collections.put("RAW_FISH", rawFish);
        itemToCollection.put("COD", "RAW_FISH");
    }

    /**
     * Inicializuje Boss kolekce
     */
    private void initializeBossCollections() {
        // Bonzo Collection
        CollectionData bonzo = new CollectionData("BONZO", "Boss", Material.PLAYER_HEAD);
        bonzo.addTier(new CollectionTier(1, 25, Arrays.asList("Red Nose", "+15 SkyBlock XP")));
        bonzo.addTier(new CollectionTier(2, 50, Arrays.asList("Golden Bonzo Head", "+15 SkyBlock XP")));
        bonzo.addTier(new CollectionTier(3, 100, Arrays.asList("Bonzo Mask", "+15 SkyBlock XP")));
        bonzo.addTier(new CollectionTier(4, 150, Arrays.asList("Recombobulator 3000", "+25 SkyBlock XP")));
        bonzo.addTier(new CollectionTier(5, 250, Arrays.asList("Bonzo Staff", "+25 SkyBlock XP")));
        bonzo.addTier(new CollectionTier(6, 1000, Arrays.asList("Diamond Bonzo Head", "+25 SkyBlock XP")));
        collections.put("BONZO", bonzo);
    }

    /**
     * Inicializuje Rift kolekce
     */
    private void initializeRiftCollections() {
        // Hemovibe Collection
        CollectionData hemovibe = new CollectionData("HEMOVIBE", "Rift", Material.REDSTONE_ORE);
        hemovibe.addTier(new CollectionTier(1, 50, Arrays.asList("Vampire Minion I Recipe", "+4 SkyBlock XP")));
        hemovibe.addTier(new CollectionTier(2, 250, Arrays.asList("Blood Donor Talisman Recipe", "+4 SkyBlock XP")));
        hemovibe.addTier(new CollectionTier(3, 1000, Arrays.asList("Hemoglass Recipe", "+4 SkyBlock XP")));
        hemovibe.addTier(new CollectionTier(4, 5000, Arrays.asList("Transylvanian IV Enchantment", "+4 SkyBlock XP")));
        hemovibe.addTier(new CollectionTier(5, 15000, Arrays.asList("Displaced Leech Recipe", "+4 SkyBlock XP")));
        hemovibe.addTier(new CollectionTier(6, 30000, Arrays.asList("Blood Donor Ring Recipe", "+4 SkyBlock XP")));
        hemovibe.addTier(new CollectionTier(7, 75000, Arrays.asList("Full-Jaw Fanging Kit Recipe", "+4 SkyBlock XP")));
        hemovibe.addTier(new CollectionTier(8, 150000, Arrays.asList("Presumed Gallon Of Red Paint Recipe", "+4 SkyBlock XP")));
        hemovibe.addTier(new CollectionTier(9, 250000, Arrays.asList("Hemobomb Recipe", "Blood Donor Artifact Recipe", "+4 SkyBlock XP")));
        collections.put("HEMOVIBE", hemovibe);
        itemToCollection.put("HEMOVIBE", "HEMOVIBE");
    }

    /**
     * Přidá položku do kolekce hráče
     */
    public boolean addToCollection(Player player, String itemName, long amount) {
        String collectionName = itemToCollection.get(itemName.toUpperCase());
        if (collectionName == null) {
            return false;
        }

        PlayerData playerData = plugin.getPlayerDataManager().loadPlayerData(player.getUniqueId()).join();
        if (playerData == null) {
            return false;
        }

        long currentAmount = playerData.getCollectionAmount(collectionName);
        playerData.addCollection(collectionName, amount);
        
        // Zkontroluj tier unlock
        CollectionData collection = collections.get(collectionName);
        if (collection != null) {
            checkTierUnlock(player, collection, currentAmount, currentAmount + amount);
        }

        // Uložit data
        plugin.getPlayerDataManager().savePlayerData(playerData);
        return true;
    }

    /**
     * Zkontroluje a odemkne nové tier
     */
    private void checkTierUnlock(Player player, CollectionData collection, long oldAmount, long newAmount) {
        for (CollectionTier tier : collection.getTiers()) {
            if (oldAmount < tier.getRequiredAmount() && newAmount >= tier.getRequiredAmount()) {
                // Nový tier odemčen!
                player.sendMessage("§6§l[KOLEKCE] §r§eOdemčen nový tier pro " + collection.getName() + " (Tier " + tier.getTier() + ")!");
                
                // Přidej odměny
                for (String reward : tier.getRewards()) {
                    player.sendMessage("§a+ " + reward);
                }
                
                // Přidej SkyBlock XP
                PlayerData playerData = plugin.getPlayerDataManager().loadPlayerData(player.getUniqueId()).join();
                if (playerData != null) {
                    playerData.addExperience(4); // +4 SkyBlock XP za každý tier
                }
            }
        }
    }

    /**
     * Získá kolekci podle názvu
     */
    public CollectionData getCollection(String name) {
        return collections.get(name.toUpperCase());
    }

    /**
     * Získá všechny kolekce
     */
    public Map<String, CollectionData> getAllCollections() {
        return new HashMap<>(collections);
    }

    /**
     * Získá kolekce podle kategorie
     */
    public Map<String, CollectionData> getCollectionsByCategory(String category) {
        Map<String, CollectionData> result = new HashMap<>();
        for (Map.Entry<String, CollectionData> entry : collections.entrySet()) {
            if (entry.getValue().getCategory().equalsIgnoreCase(category)) {
                result.put(entry.getKey(), entry.getValue());
            }
        }
        return result;
    }
}
