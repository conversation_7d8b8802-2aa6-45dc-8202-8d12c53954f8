package cz.hypixel.skyblock.commands;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.gui.AuctionHouseGUI;
import cz.hypixel.skyblock.managers.BankManager;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

/**
 * Příkaz pro Auction House systém
 */
public class AuctionCommand implements CommandExecutor {

    private final HypixelSkyBlockCZ plugin;
    private final AuctionHouseGUI auctionGUI;

    public AuctionCommand(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.auctionGUI = new AuctionHouseGUI(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cTento příkaz může pou<PERSON>t pouze hráč!");
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            // Otevři Auction House GUI
            auctionGUI.openAuctionHouse(player);
            return true;
        }

        String action = args[0].toLowerCase();

        switch (action) {
            case "create":
            case "sell":
                handleCreateAuction(player, args);
                break;
                
            case "bid":
                handleBid(player, args);
                break;
                
            case "buy":
                handleBuy(player, args);
                break;
                
            case "list":
            case "my":
                handleListMyAuctions(player);
                break;
                
            case "help":
                showHelp(player);
                break;
                
            default:
                player.sendMessage("§cNeznámý příkaz! Použij §e/ah help §cpro nápovědu.");
                break;
        }

        return true;
    }

    /**
     * Zpracuje vytvoření aukce
     */
    private void handleCreateAuction(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cPoužití: /ah create <cena> [bin] [doba_hodin]");
            player.sendMessage("§7Příklad: /ah create 1000");
            player.sendMessage("§7Příklad: /ah create 5000 bin");
            player.sendMessage("§7Příklad: /ah create 1000 auction 48");
            return;
        }

        ItemStack item = player.getInventory().getItemInMainHand();
        if (item.getType().isAir()) {
            player.sendMessage("§cMusíš držet předmět v ruce!");
            return;
        }

        double price = BankManager.parseCoins(args[1]);
        if (price <= 0) {
            player.sendMessage("§cNeplatná cena!");
            return;
        }

        boolean buyItNow = false;
        int durationHours = 24; // Výchozí doba

        // Zpracuj další argumenty
        for (int i = 2; i < args.length; i++) {
            String arg = args[i].toLowerCase();
            if (arg.equals("bin") || arg.equals("buy")) {
                buyItNow = true;
            } else if (arg.equals("auction") || arg.equals("bid")) {
                buyItNow = false;
            } else {
                try {
                    int hours = Integer.parseInt(arg);
                    if (hours >= 1 && hours <= 168) { // 1 hodina až 7 dní
                        durationHours = hours;
                    } else {
                        player.sendMessage("§cDoba aukce musí být mezi 1-168 hodinami!");
                        return;
                    }
                } catch (NumberFormatException e) {
                    player.sendMessage("§cNeplatný argument: " + arg);
                    return;
                }
            }
        }

        // Vytvoř aukci
        plugin.getAuctionManager().createAuction(player, item, price, buyItNow, price, durationHours)
            .thenAccept(success -> {
                if (!success) {
                    player.sendMessage("§cNepodařilo se vytvořit aukci!");
                }
            });
    }

    /**
     * Zpracuje bid na aukci
     */
    private void handleBid(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage("§cPoužití: /ah bid <id_aukce> <částka>");
            player.sendMessage("§7Příklad: /ah bid 123 1500");
            return;
        }

        int auctionId;
        try {
            auctionId = Integer.parseInt(args[1]);
        } catch (NumberFormatException e) {
            player.sendMessage("§cNeplatné ID aukce!");
            return;
        }

        double bidAmount = BankManager.parseCoins(args[2]);
        if (bidAmount <= 0) {
            player.sendMessage("§cNeplatná částka!");
            return;
        }

        plugin.getAuctionManager().placeBid(player, auctionId, bidAmount)
            .thenAccept(success -> {
                if (!success) {
                    player.sendMessage("§cNepodařilo se podat bid!");
                }
            });
    }

    /**
     * Zpracuje koupi přes BIN
     */
    private void handleBuy(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cPoužití: /ah buy <id_aukce>");
            player.sendMessage("§7Příklad: /ah buy 123");
            return;
        }

        int auctionId;
        try {
            auctionId = Integer.parseInt(args[1]);
        } catch (NumberFormatException e) {
            player.sendMessage("§cNeplatné ID aukce!");
            return;
        }

        plugin.getAuctionManager().buyInstantly(player, auctionId)
            .thenAccept(success -> {
                if (!success) {
                    player.sendMessage("§cNepodařilo se koupit předmět!");
                }
            });
    }

    /**
     * Zobrazí aukce hráče
     */
    private void handleListMyAuctions(Player player) {
        plugin.getAuctionManager().getPlayerAuctions(player.getUniqueId())
            .thenAccept(auctions -> {
                player.sendMessage("§6§l=== MOJE AUKCE ===");
                
                if (auctions.isEmpty()) {
                    player.sendMessage("§7Nemáš žádné aukce.");
                    return;
                }

                for (int i = 0; i < Math.min(auctions.size(), 10); i++) {
                    var auction = auctions.get(i);
                    String status = auction.getStatus().getColoredName();
                    String type = auction.isBuyItNow() ? "§6BIN" : "§eAukce";
                    
                    player.sendMessage("§7[§e" + auction.getId() + "§7] " + 
                                     "§e" + auction.getItemName() + " §7- " + 
                                     type + " §7- " + status);
                    
                    if (auction.isActive()) {
                        if (auction.isBuyItNow()) {
                            player.sendMessage("  §7Cena: §e" + BankManager.formatCoins(auction.getBinPrice()));
                        } else {
                            if (auction.getCurrentBid() > 0) {
                                player.sendMessage("  §7Současný bid: §e" + BankManager.formatCoins(auction.getCurrentBid()));
                            } else {
                                player.sendMessage("  §7Počáteční cena: §e" + BankManager.formatCoins(auction.getStartingBid()));
                            }
                        }
                        player.sendMessage("  §7Zbývá: §e" + auction.getTimeLeftFormatted());
                    }
                }
                
                if (auctions.size() > 10) {
                    player.sendMessage("§7... a " + (auctions.size() - 10) + " dalších");
                }
            });
    }

    /**
     * Zobrazí nápovědu
     */
    private void showHelp(Player player) {
        player.sendMessage("§6§l=== AUCTION HOUSE - NÁPOVĚDA ===");
        player.sendMessage("§e/ah §7- Otevře Auction House GUI");
        player.sendMessage("§e/ah create <cena> [bin/auction] [hodiny] §7- Vytvoří aukci");
        player.sendMessage("§e/ah bid <id> <částka> §7- Podá bid na aukci");
        player.sendMessage("§e/ah buy <id> §7- Koupí BIN aukci");
        player.sendMessage("§e/ah list §7- Zobrazí tvoje aukce");
        player.sendMessage("");
        player.sendMessage("§7Příklady:");
        player.sendMessage("§7- §e/ah create 1000 bin §7- BIN aukce za 1000 mincí");
        player.sendMessage("§7- §e/ah create 500 auction 48 §7- Aukce na 48 hodin");
        player.sendMessage("§7- §e/ah bid 123 1500 §7- Bid 1500 mincí na aukci #123");
        player.sendMessage("§7- §e/ah buy 456 §7- Koupí BIN aukci #456");
        player.sendMessage("");
        player.sendMessage("§7Poplatky: §e1% z ceny §7(min. 100, max. 1M mincí)");
    }
}
