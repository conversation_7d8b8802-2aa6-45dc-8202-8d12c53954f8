<!DOCTYPE html>
<html lang="cs">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Minecraft Items</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=VT323&display=swap');

        body {
            background-color: #1E1E1E;
            color: #FFFFFF;
            font-family: 'VT323', monospace;
            display: flex;
            flex-direction: column;
            align-items: center;
            margin: 0;
            padding: 2em;
            background-image: url('https://www.transparenttextures.com/patterns/dark-stone-wall.png');
        }

        h1 {
            font-size: 3em;
            color: #FFAA00;
            text-shadow: 2px 2px #3B3B3B;
            margin-bottom: 1em;
        }

        .container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
            gap: 25px;
            width: 90%;
            max-width: 1400px;
        }

        .item-card {
            background-color: #3C3C3C;
            border: 3px solid #5A5A5A;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            height: 250px;
        }

        .item-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 16px rgba(0, 0, 0, 0.5);
        }

        .item-card h2 {
            font-size: 1.4em;
            margin: 0.5em 0;
            color: #FFFFFF;
            word-wrap: break-word;
        }

        .item-card p {
            font-size: 1em;
            color: #AAAAAA;
            margin: 0.2em 0;
        }
        
        .item-skin {
            width: 64px;
            height: 64px;
            margin: 10px auto;
            image-rendering: pixelated;
        }

        .no-items {
            color: #AAAAAA;
            font-size: 1.5em;
            text-align: center;
            width: 100%;
        }
        
        /* Tier colors */
        .tier-COMMON { border-color: #AAAAAA; }
        .tier-UNCOMMON { border-color: #FFFFFF; }
        .tier-RARE { border-color: #5555FF; }
        .tier-EPIC { border-color: #AA00AA; }
        .tier-LEGENDARY { border-color: #FFAA00; }
        .tier-MYTHIC { border-color: #FF55FF; }
        .tier-SPECIAL { border-color: #AA0000; }
        .tier-DIVINE { border-color: #55FFFF; }

    </style>
</head>
<body>

    <h1>Minecraft Předměty</h1>

    <div class="container">
        <?php
        $json_file = 'items.json';
        if (file_exists($json_file)) {
            $json_data = file_get_contents($json_file);
            $data = json_decode($json_data);

            if (json_last_error() === JSON_ERROR_NONE && isset($data->success) && $data->success && !empty($data->items)) {
                foreach ($data->items as $item) {
                    $tier = isset($item->tier) ? $item->tier : 'COMMON';
                    echo '<div class="item-card tier-' . htmlspecialchars($tier) . '">';
                    
                    // Skin
                    if (isset($item->skin) && isset($item->skin->value)) {
                        $skin_data_json = base64_decode($item->skin->value);
                        $skin_data = json_decode($skin_data_json);
                        if (isset($skin_data->textures->SKIN->url)) {
                            $skin_url = $skin_data->textures->SKIN->url;
                            echo '<img src="' . htmlspecialchars($skin_url) . '" alt="' . htmlspecialchars($item->name) . ' skin" class="item-skin">';
                        }
                    }

                    echo '<div>';
                    echo '<h2>' . htmlspecialchars($item->name) . '</h2>';
                    if (isset($item->category)) {
                        echo '<p>Category: ' . htmlspecialchars($item->category) . '</p>';
                    }
                    echo '<p>Tier: ' . htmlspecialchars($tier) . '</p>';
                    if (isset($item->description)) {
                        echo '<p>' . htmlspecialchars(preg_replace('/%%[^%]+%%/', '', $item->description)) . '</p>';
                    }
                    echo '</div>';

                    echo '</div>';
                }
            } else {
                echo '<p class="no-items">V souboru items.json nebyla nalezena žádná data nebo je soubor poškozen.</p>';
            }
        } else {
            echo '<p class="no-items">Soubor items.json nebyl nalezen.</p>';
        }
        ?>
    </div>

</body>
</html>