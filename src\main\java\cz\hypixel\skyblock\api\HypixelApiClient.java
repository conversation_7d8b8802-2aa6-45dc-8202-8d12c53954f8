package cz.hypixel.skyblock.api;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.api.models.SkyBlockProfile;
import cz.hypixel.skyblock.api.models.AuctionData;
import cz.hypixel.skyblock.api.models.BazaarData;
import cz.hypixel.skyblock.cache.CacheManager;
import okhttp3.*;

import java.io.IOException;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;

/**
 * HTTP klient pro komunikaci s Hypixel API
 */
public class HypixelApiClient {
    
    private final HypixelSkyBlockCZ plugin;
    private final Logger logger;
    private final OkHttpClient httpClient;
    private final Gson gson;
    private final String apiKey;
    private final String baseUrl;
    private final CacheManager cacheManager;
    
    // Rate limiting
    private long lastRequestTime = 0;
    private final long rateLimitDelay; // milliseconds between requests
    
    public HypixelApiClient(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.gson = new Gson();
        this.cacheManager = new CacheManager(plugin);
        
        // Načtení konfigurace
        this.apiKey = plugin.getConfig().getString("hypixel_api.api_key", "");
        this.baseUrl = plugin.getConfig().getString("hypixel_api.base_url", "https://api.hypixel.net");
        int timeout = plugin.getConfig().getInt("hypixel_api.timeout", 10);
        int rateLimit = plugin.getConfig().getInt("hypixel_api.rate_limit", 120);
        
        // Rate limit delay (60000ms / requests per minute)
        this.rateLimitDelay = 60000L / rateLimit;
        
        // Konfigurace HTTP klienta
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(timeout, TimeUnit.SECONDS)
                .readTimeout(timeout, TimeUnit.SECONDS)
                .writeTimeout(timeout, TimeUnit.SECONDS)
                .build();
        
        if (apiKey.isEmpty() || apiKey.equals("your-hypixel-api-key-here")) {
            logger.warning("Hypixel API klíč není nastaven! Získej ho na https://developer.hypixel.net/");
        } else {
            logger.info("Hypixel API klient inicializován s rate limitem " + rateLimit + " req/min");
        }
    }
    
    /**
     * Získá SkyBlock profily hráče
     */
    public CompletableFuture<SkyBlockProfile[]> getSkyBlockProfiles(String playerUuid) {
        String cacheKey = "profiles_" + playerUuid;
        
        // Zkontroluj cache
        SkyBlockProfile[] cached = cacheManager.get(cacheKey, SkyBlockProfile[].class);
        if (cached != null) {
            return CompletableFuture.completedFuture(cached);
        }
        
        return makeRequest("/skyblock/profiles?uuid=" + playerUuid)
                .thenApply(response -> {
                    if (response != null && response.has("profiles")) {
                        SkyBlockProfile[] profiles = gson.fromJson(response.get("profiles"), SkyBlockProfile[].class);
                        cacheManager.put(cacheKey, profiles);
                        return profiles;
                    }
                    return new SkyBlockProfile[0];
                })
                .exceptionally(throwable -> {
                    logger.warning("Chyba při získávání SkyBlock profilů: " + throwable.getMessage());
                    return new SkyBlockProfile[0];
                });
    }
    
    /**
     * Získá data z auction house
     */
    public CompletableFuture<AuctionData> getAuctions(int page) {
        String cacheKey = "auctions_" + page;
        
        // Zkontroluj cache
        AuctionData cached = cacheManager.get(cacheKey, AuctionData.class);
        if (cached != null) {
            return CompletableFuture.completedFuture(cached);
        }
        
        return makeRequest("/skyblock/auctions?page=" + page)
                .thenApply(response -> {
                    if (response != null) {
                        AuctionData auctionData = gson.fromJson(response, AuctionData.class);
                        cacheManager.put(cacheKey, auctionData);
                        return auctionData;
                    }
                    return new AuctionData();
                })
                .exceptionally(throwable -> {
                    logger.warning("Chyba při získávání auction dat: " + throwable.getMessage());
                    return new AuctionData();
                });
    }
    
    /**
     * Získá data z bazaaru
     */
    public CompletableFuture<BazaarData> getBazaarData() {
        String cacheKey = "bazaar_data";
        
        // Zkontroluj cache
        BazaarData cached = cacheManager.get(cacheKey, BazaarData.class);
        if (cached != null) {
            return CompletableFuture.completedFuture(cached);
        }
        
        return makeRequest("/skyblock/bazaar")
                .thenApply(response -> {
                    if (response != null) {
                        BazaarData bazaarData = gson.fromJson(response, BazaarData.class);
                        cacheManager.put(cacheKey, bazaarData);
                        return bazaarData;
                    }
                    return new BazaarData();
                })
                .exceptionally(throwable -> {
                    logger.warning("Chyba při získávání bazaar dat: " + throwable.getMessage());
                    return new BazaarData();
                });
    }
    
    /**
     * Provede HTTP požadavek na Hypixel API
     */
    private CompletableFuture<JsonObject> makeRequest(String endpoint) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                // Rate limiting
                enforceRateLimit();
                
                String url = baseUrl + endpoint;
                Request request = new Request.Builder()
                        .url(url)
                        .addHeader("API-Key", apiKey)
                        .addHeader("User-Agent", "HypixelSkyBlockCZ/1.0")
                        .build();
                
                logger.info("API požadavek: " + endpoint);
                
                try (Response response = httpClient.newCall(request).execute()) {
                    if (!response.isSuccessful()) {
                        logger.warning("API požadavek neúspěšný: " + response.code() + " " + response.message());
                        return null;
                    }
                    
                    ResponseBody body = response.body();
                    if (body == null) {
                        logger.warning("Prázdná odpověď z API");
                        return null;
                    }
                    
                    String jsonString = body.string();
                    JsonObject jsonObject = gson.fromJson(jsonString, JsonObject.class);
                    
                    // Zkontroluj úspěch
                    if (jsonObject.has("success") && !jsonObject.get("success").getAsBoolean()) {
                        String cause = jsonObject.has("cause") ? jsonObject.get("cause").getAsString() : "Neznámá chyba";
                        logger.warning("API chyba: " + cause);
                        return null;
                    }
                    
                    return jsonObject;
                }
                
            } catch (IOException e) {
                logger.severe("Chyba při HTTP požadavku: " + e.getMessage());
                return null;
            }
        });
    }
    
    /**
     * Vynucuje rate limiting
     */
    private void enforceRateLimit() {
        long currentTime = System.currentTimeMillis();
        long timeSinceLastRequest = currentTime - lastRequestTime;
        
        if (timeSinceLastRequest < rateLimitDelay) {
            try {
                Thread.sleep(rateLimitDelay - timeSinceLastRequest);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
        
        lastRequestTime = System.currentTimeMillis();
    }
    
    /**
     * Ukončí HTTP klienta
     */
    public void shutdown() {
        httpClient.dispatcher().executorService().shutdown();
        httpClient.connectionPool().evictAll();
        logger.info("Hypixel API klient ukončen");
    }
    
    /**
     * Zkontroluje, jestli je API klíč nastaven
     */
    public boolean isApiKeyConfigured() {
        return !apiKey.isEmpty() && !apiKey.equals("your-hypixel-api-key-here");
    }
}
