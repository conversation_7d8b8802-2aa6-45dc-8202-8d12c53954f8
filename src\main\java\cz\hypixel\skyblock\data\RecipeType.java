package cz.hypixel.skyblock.data;

/**
 * Enum pro typy receptů podle Hypixel SkyBlock
 */
public enum RecipeType {
    
    SHAPED("Shaped", "§e", "Shaped crafting recipe"),
    SHAPELESS("Shapeless", "§a", "Shapeless crafting recipe"),
    FURNACE("Furnace", "§c", "Furnace smelting recipe"),
    BREWING("Brewing", "§d", "Brewing stand recipe"),
    ANVIL("Anvil", "§7", "Anvil combining recipe"),
    ENCHANTING("Enchanting", "§5", "Enchanting table recipe"),
    MINION("Minion", "§6", "Minion crafting recipe"),
    SPECIAL("Special", "§b", "Special crafting recipe");
    
    private final String displayName;
    private final String colorCode;
    private final String description;
    
    RecipeType(String displayName, String colorCode, String description) {
        this.displayName = displayName;
        this.colorCode = colorCode;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getColorCode() {
        return colorCode;
    }
    
    public String getColoredName() {
        return colorCode + displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Získá typ podle názvu
     */
    public static RecipeType fromString(String name) {
        for (RecipeType type : values()) {
            if (type.name().equalsIgnoreCase(name) || 
                type.displayName.equalsIgnoreCase(name)) {
                return type;
            }
        }
        return SHAPED; // Výchozí typ
    }
}
