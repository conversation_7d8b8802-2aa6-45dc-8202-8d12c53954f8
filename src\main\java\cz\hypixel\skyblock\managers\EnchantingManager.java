package cz.hypixel.skyblock.managers;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.SkyBlockEnchantment;
import cz.hypixel.skyblock.database.PlayerData;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;

/**
 * Manager pro Enchanting systém podle Hypixel SkyBlock
 */
public class EnchantingManager {

    private final HypixelSkyBlockCZ plugin;
    private final Logger logger;
    
    // Enchanting materiály a jejich ceny
    private final Map<Material, Double> enchantingMaterials;

    public EnchantingManager(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.enchantingMaterials = new HashMap<>();
        
        initializeEnchantingMaterials();
        
        logger.info("EnchantingManager inicializován");
    }

    /**
     * Inicializuje enchanting materiály
     */
    private void initializeEnchantingMaterials() {
        // Základní materiály
        enchantingMaterials.put(Material.LAPIS_LAZULI, 50.0);
        enchantingMaterials.put(Material.EXPERIENCE_BOTTLE, 200.0);
        enchantingMaterials.put(Material.BOOK, 100.0);
        
        // Vzácné materiály
        enchantingMaterials.put(Material.DIAMOND, 1000.0);
        enchantingMaterials.put(Material.EMERALD, 2000.0);
        enchantingMaterials.put(Material.NETHER_STAR, 50000.0);
        
        // Speciální materiály
        enchantingMaterials.put(Material.ENCHANTED_BOOK, 5000.0);
        enchantingMaterials.put(Material.DRAGON_EGG, 1000000.0);
    }

    /**
     * Enchantuje předmět
     */
    public CompletableFuture<Boolean> enchantItem(Player player, ItemStack item, SkyBlockEnchantment enchantment, int level) {
        return CompletableFuture.supplyAsync(() -> {
            // Zkontroluj platnost
            if (item == null || item.getType().isAir()) {
                player.sendMessage("§cNeplatný předmět!");
                return false;
            }
            
            if (level < 1 || level > enchantment.getMaxLevel()) {
                player.sendMessage("§cNeplatný level enchantmentu!");
                return false;
            }
            
            if (!enchantment.canApplyTo(item.getType())) {
                player.sendMessage("§cTento enchantment nelze aplikovat na tento předmět!");
                return false;
            }
            
            // Zkontroluj, jestli už má enchantment
            if (hasEnchantment(item, enchantment)) {
                int currentLevel = getEnchantmentLevel(item, enchantment);
                if (currentLevel >= level) {
                    player.sendMessage("§cPředmět už má tento enchantment na stejném nebo vyšším levelu!");
                    return false;
                }
            }
            
            // Spočítej cenu
            double cost = calculateEnchantCost(enchantment, level);
            
            // Zkontroluj mince
            PlayerData playerData = plugin.getPlayerDataManager().loadPlayerData(player.getUniqueId()).join();
            if (playerData == null || playerData.getCoins() < cost) {
                player.sendMessage("§cNemáš dostatek mincí!");
                player.sendMessage("§7Potřebuješ: §e" + BankManager.formatCoins(cost));
                return false;
            }
            
            // Zkontroluj materiály
            if (!hasRequiredMaterials(player, enchantment, level)) {
                player.sendMessage("§cNemáš potřebné materiály!");
                showRequiredMaterials(player, enchantment, level);
                return false;
            }
            
            // Proveď enchantování
            playerData.removeCoins(cost);
            plugin.getPlayerDataManager().savePlayerData(playerData);
            
            removeRequiredMaterials(player, enchantment, level);
            applyEnchantment(item, enchantment, level);
            
            // Přidej Enchanting XP
            plugin.getSkillsManager().addExperience(player.getUniqueId(), "ENCHANTING", calculateEnchantingXP(level));
            
            player.sendMessage("§aÚspěšně jsi enchantoval předmět!");
            player.sendMessage("§7Enchantment: " + enchantment.getFormattedName(level));
            player.sendMessage("§7Cena: §c-" + BankManager.formatCoins(cost));
            
            return true;
        });
    }

    /**
     * Odebere enchantment z předmětu
     */
    public CompletableFuture<Boolean> removeEnchantment(Player player, ItemStack item, SkyBlockEnchantment enchantment) {
        return CompletableFuture.supplyAsync(() -> {
            if (!hasEnchantment(item, enchantment)) {
                player.sendMessage("§cPředmět nemá tento enchantment!");
                return false;
            }
            
            // Cena za odebrání (50% původní ceny)
            int level = getEnchantmentLevel(item, enchantment);
            double cost = calculateEnchantCost(enchantment, level) * 0.5;
            
            PlayerData playerData = plugin.getPlayerDataManager().loadPlayerData(player.getUniqueId()).join();
            if (playerData == null || playerData.getCoins() < cost) {
                player.sendMessage("§cNemáš dostatek mincí na odebrání enchantmentu!");
                player.sendMessage("§7Potřebuješ: §e" + BankManager.formatCoins(cost));
                return false;
            }
            
            // Proveď odebrání
            playerData.removeCoins(cost);
            plugin.getPlayerDataManager().savePlayerData(playerData);
            
            removeEnchantmentFromItem(item, enchantment);
            
            player.sendMessage("§aÚspěšně jsi odebral enchantment!");
            player.sendMessage("§7Enchantment: " + enchantment.getColoredName());
            player.sendMessage("§7Cena: §c-" + BankManager.formatCoins(cost));
            
            return true;
        });
    }

    /**
     * Aplikuje enchantment na předmět
     */
    private void applyEnchantment(ItemStack item, SkyBlockEnchantment enchantment, int level) {
        ItemMeta meta = item.getItemMeta();
        if (meta == null) return;
        
        List<String> lore = meta.getLore();
        if (lore == null) {
            lore = new ArrayList<>();
        }
        
        // Odeber starý enchantment pokud existuje
        removeEnchantmentFromLore(lore, enchantment);
        
        // Přidej nový enchantment
        String enchantLine = enchantment.getFormattedName(level) + " §7- " + enchantment.getDescription(level);
        
        // Najdi správné místo pro enchantment (na začátek lore)
        int insertIndex = 0;
        for (int i = 0; i < lore.size(); i++) {
            String line = lore.get(i);
            if (!line.startsWith("§") || line.startsWith("§7")) {
                insertIndex = i;
                break;
            }
            insertIndex = i + 1;
        }
        
        lore.add(insertIndex, enchantLine);
        meta.setLore(lore);
        item.setItemMeta(meta);
    }

    /**
     * Odebere enchantment z předmětu
     */
    private void removeEnchantmentFromItem(ItemStack item, SkyBlockEnchantment enchantment) {
        ItemMeta meta = item.getItemMeta();
        if (meta == null) return;
        
        List<String> lore = meta.getLore();
        if (lore == null) return;
        
        removeEnchantmentFromLore(lore, enchantment);
        meta.setLore(lore);
        item.setItemMeta(meta);
    }

    /**
     * Odebere enchantment z lore
     */
    private void removeEnchantmentFromLore(List<String> lore, SkyBlockEnchantment enchantment) {
        lore.removeIf(line -> line.contains(enchantment.getDisplayName()));
    }

    /**
     * Zkontroluje, jestli má předmět enchantment
     */
    public boolean hasEnchantment(ItemStack item, SkyBlockEnchantment enchantment) {
        ItemMeta meta = item.getItemMeta();
        if (meta == null || meta.getLore() == null) return false;
        
        return meta.getLore().stream()
            .anyMatch(line -> line.contains(enchantment.getDisplayName()));
    }

    /**
     * Získá level enchantmentu na předmětu
     */
    public int getEnchantmentLevel(ItemStack item, SkyBlockEnchantment enchantment) {
        ItemMeta meta = item.getItemMeta();
        if (meta == null || meta.getLore() == null) return 0;
        
        for (String line : meta.getLore()) {
            if (line.contains(enchantment.getDisplayName())) {
                // Pokus se extrahovat level z římských číslic
                String[] parts = line.split(" ");
                for (String part : parts) {
                    if (part.matches("[IVX]+")) {
                        return parseRomanNumeral(part);
                    }
                }
                return 1; // Pokud není level, je to level 1
            }
        }
        
        return 0;
    }

    /**
     * Spočítá cenu enchantování
     */
    private double calculateEnchantCost(SkyBlockEnchantment enchantment, int level) {
        return enchantment.getEnchantCost(level);
    }

    /**
     * Spočítá Enchanting XP
     */
    private double calculateEnchantingXP(int level) {
        return level * 50.0; // 50 XP za level
    }

    /**
     * Zkontroluje potřebné materiály
     */
    private boolean hasRequiredMaterials(Player player, SkyBlockEnchantment enchantment, int level) {
        Map<Material, Integer> required = getRequiredMaterials(enchantment, level);
        
        for (Map.Entry<Material, Integer> entry : required.entrySet()) {
            if (countItems(player, entry.getKey()) < entry.getValue()) {
                return false;
            }
        }
        
        return true;
    }

    /**
     * Odebere potřebné materiály
     */
    private void removeRequiredMaterials(Player player, SkyBlockEnchantment enchantment, int level) {
        Map<Material, Integer> required = getRequiredMaterials(enchantment, level);
        
        for (Map.Entry<Material, Integer> entry : required.entrySet()) {
            removeItems(player, entry.getKey(), entry.getValue());
        }
    }

    /**
     * Získá potřebné materiály pro enchantment
     */
    private Map<Material, Integer> getRequiredMaterials(SkyBlockEnchantment enchantment, int level) {
        Map<Material, Integer> materials = new HashMap<>();
        
        // Základní materiály pro všechny enchantmenty
        materials.put(Material.LAPIS_LAZULI, level * 3);
        materials.put(Material.EXPERIENCE_BOTTLE, level);
        
        // Speciální materiály podle typu enchantmentu
        if (enchantment.name().contains("ULTIMATE")) {
            materials.put(Material.NETHER_STAR, 1);
        } else if (level >= 5) {
            materials.put(Material.DIAMOND, level - 4);
        }
        
        return materials;
    }

    /**
     * Zobrazí potřebné materiály
     */
    private void showRequiredMaterials(Player player, SkyBlockEnchantment enchantment, int level) {
        Map<Material, Integer> required = getRequiredMaterials(enchantment, level);
        
        player.sendMessage("§7Potřebné materiály:");
        for (Map.Entry<Material, Integer> entry : required.entrySet()) {
            int has = countItems(player, entry.getKey());
            int needed = entry.getValue();
            String color = has >= needed ? "§a" : "§c";
            
            player.sendMessage("§7- " + entry.getKey().name() + ": " + color + has + "§7/" + needed);
        }
    }

    /**
     * Spočítá předměty v inventáři
     */
    private int countItems(Player player, Material material) {
        int count = 0;
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.getType() == material) {
                count += item.getAmount();
            }
        }
        return count;
    }

    /**
     * Odebere předměty z inventáře
     */
    private void removeItems(Player player, Material material, int amount) {
        ItemStack itemToRemove = new ItemStack(material, amount);
        player.getInventory().removeItem(itemToRemove);
    }

    /**
     * Převede římské číslice na číslo
     */
    private int parseRomanNumeral(String roman) {
        Map<String, Integer> romanMap = Map.of(
            "I", 1, "II", 2, "III", 3, "IV", 4, "V", 5,
            "VI", 6, "VII", 7, "VIII", 8, "IX", 9, "X", 10
        );
        
        return romanMap.getOrDefault(roman, 1);
    }

    /**
     * Získá všechny enchantmenty na předmětu
     */
    public Map<SkyBlockEnchantment, Integer> getEnchantments(ItemStack item) {
        Map<SkyBlockEnchantment, Integer> enchantments = new HashMap<>();
        
        for (SkyBlockEnchantment enchant : SkyBlockEnchantment.values()) {
            if (hasEnchantment(item, enchant)) {
                enchantments.put(enchant, getEnchantmentLevel(item, enchant));
            }
        }
        
        return enchantments;
    }
}
