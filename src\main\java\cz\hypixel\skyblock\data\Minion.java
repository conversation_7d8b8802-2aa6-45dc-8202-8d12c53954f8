package cz.hypixel.skyblock.data;

import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

import java.util.*;

/**
 * Data třída pro miniony podle Hypixel SkyBlock
 */
public class Minion {
    
    private int id;
    private UUID ownerUuid;
    private MinionType minionType;
    private int tier;
    private Location location;
    private boolean active;
    private long lastAction;
    private long placedAt;
    private List<ItemStack> storage;
    private Map<String, Object> upgrades;
    private double fuelRemaining;
    private String skinType;
    
    public Minion() {
        this.tier = 1;
        this.active = true;
        this.lastAction = System.currentTimeMillis();
        this.placedAt = System.currentTimeMillis();
        this.storage = new ArrayList<>();
        this.upgrades = new HashMap<>();
        this.fuelRemaining = 0;
        this.skinType = "default";
    }
    
    public Minion(UUID ownerUuid, MinionType minionType, int tier, Location location) {
        this();
        this.ownerUuid = ownerUuid;
        this.minionType = minionType;
        this.tier = tier;
        this.location = location;
    }
    
    // Gettery a settery
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public UUID getOwnerUuid() {
        return ownerUuid;
    }
    
    public void setOwnerUuid(UUID ownerUuid) {
        this.ownerUuid = ownerUuid;
    }
    
    public MinionType getMinionType() {
        return minionType;
    }
    
    public void setMinionType(MinionType minionType) {
        this.minionType = minionType;
    }
    
    public int getTier() {
        return tier;
    }
    
    public void setTier(int tier) {
        this.tier = tier;
    }
    
    public Location getLocation() {
        return location;
    }
    
    public void setLocation(Location location) {
        this.location = location;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }
    
    public long getLastAction() {
        return lastAction;
    }
    
    public void setLastAction(long lastAction) {
        this.lastAction = lastAction;
    }
    
    public long getPlacedAt() {
        return placedAt;
    }
    
    public void setPlacedAt(long placedAt) {
        this.placedAt = placedAt;
    }
    
    public List<ItemStack> getStorage() {
        return storage;
    }
    
    public void setStorage(List<ItemStack> storage) {
        this.storage = storage;
    }
    
    public Map<String, Object> getUpgrades() {
        return upgrades;
    }
    
    public void setUpgrades(Map<String, Object> upgrades) {
        this.upgrades = upgrades;
    }
    
    public double getFuelRemaining() {
        return fuelRemaining;
    }
    
    public void setFuelRemaining(double fuelRemaining) {
        this.fuelRemaining = fuelRemaining;
    }
    
    public String getSkinType() {
        return skinType;
    }
    
    public void setSkinType(String skinType) {
        this.skinType = skinType;
    }
    
    // Utility metody
    public double getActionSpeed() {
        // Základní rychlost podle tieru
        double baseSpeed = minionType.getBaseActionTime() / Math.pow(1.1, tier - 1);
        
        // Aplikuj upgrady
        if (upgrades.containsKey("AUTO_SMELTER")) {
            baseSpeed *= 0.95; // 5% rychlejší
        }
        if (upgrades.containsKey("COMPACTOR")) {
            baseSpeed *= 0.9; // 10% rychlejší
        }
        if (upgrades.containsKey("SUPER_COMPACTOR")) {
            baseSpeed *= 0.85; // 15% rychlejší
        }
        
        // Aplikuj fuel bonus
        if (fuelRemaining > 0) {
            baseSpeed *= 0.75; // 25% rychlejší s fuel
        }
        
        return baseSpeed;
    }
    
    public boolean canPerformAction() {
        if (!active) return false;
        
        long currentTime = System.currentTimeMillis();
        double actionSpeed = getActionSpeed();
        
        return (currentTime - lastAction) >= (actionSpeed * 1000);
    }
    
    public void performAction() {
        if (!canPerformAction()) return;
        
        // Generuj předměty podle typu miniona
        ItemStack[] generatedItems = minionType.generateItems(tier);
        
        // Zpracuj upgrady
        generatedItems = processUpgrades(generatedItems);
        
        // Přidej do storage
        for (ItemStack item : generatedItems) {
            if (item != null) {
                addToStorage(item);
            }
        }
        
        // Spotřebuj fuel
        if (fuelRemaining > 0) {
            fuelRemaining -= 0.1; // Spotřeba fuel za akci
            if (fuelRemaining < 0) fuelRemaining = 0;
        }
        
        lastAction = System.currentTimeMillis();
    }
    
    private ItemStack[] processUpgrades(ItemStack[] items) {
        List<ItemStack> processedItems = new ArrayList<>();
        
        for (ItemStack item : items) {
            if (item == null) continue;
            
            // Auto Smelter upgrade
            if (upgrades.containsKey("AUTO_SMELTER")) {
                item = smeltItem(item);
            }
            
            // Compactor upgrade
            if (upgrades.containsKey("COMPACTOR")) {
                item = compactItem(item, false);
            }
            
            // Super Compactor upgrade
            if (upgrades.containsKey("SUPER_COMPACTOR")) {
                item = compactItem(item, true);
            }
            
            processedItems.add(item);
        }
        
        return processedItems.toArray(new ItemStack[0]);
    }
    
    private ItemStack smeltItem(ItemStack item) {
        // Zjednodušená smelting logika
        Material smelted = switch (item.getType()) {
            case COBBLESTONE -> Material.STONE;
            case RAW_IRON -> Material.IRON_INGOT;
            case RAW_GOLD -> Material.GOLD_INGOT;
            case RAW_COPPER -> Material.COPPER_INGOT;
            case SAND -> Material.GLASS;
            default -> item.getType();
        };
        
        return new ItemStack(smelted, item.getAmount());
    }
    
    private ItemStack compactItem(ItemStack item, boolean superCompact) {
        // Zjednodušená compacting logika
        if (item.getAmount() < 9) return item;
        
        Material compacted = switch (item.getType()) {
            case COBBLESTONE -> superCompact ? Material.STONE_BRICKS : Material.STONE;
            case WHEAT -> Material.HAY_BLOCK;
            case COAL -> Material.COAL_BLOCK;
            case IRON_INGOT -> Material.IRON_BLOCK;
            case GOLD_INGOT -> Material.GOLD_BLOCK;
            case DIAMOND -> Material.DIAMOND_BLOCK;
            case EMERALD -> Material.EMERALD_BLOCK;
            default -> item.getType();
        };
        
        if (compacted != item.getType()) {
            int newAmount = item.getAmount() / 9;
            return new ItemStack(compacted, newAmount);
        }
        
        return item;
    }
    
    private void addToStorage(ItemStack item) {
        // Zkus sloučit s existujícími itemy
        for (ItemStack stored : storage) {
            if (stored.isSimilar(item)) {
                int maxStack = stored.getMaxStackSize();
                int canAdd = maxStack - stored.getAmount();
                int toAdd = Math.min(canAdd, item.getAmount());
                
                stored.setAmount(stored.getAmount() + toAdd);
                item.setAmount(item.getAmount() - toAdd);
                
                if (item.getAmount() <= 0) return;
            }
        }
        
        // Pokud se nevešlo, přidej jako nový stack
        if (item.getAmount() > 0 && storage.size() < getMaxStorageSlots()) {
            storage.add(item.clone());
        }
    }
    
    public int getMaxStorageSlots() {
        // Základní storage podle tieru
        int baseSlots = 15 + (tier * 3);
        
        // Upgrady mohou zvýšit storage
        if (upgrades.containsKey("LARGE_STORAGE")) {
            baseSlots += 9;
        }
        if (upgrades.containsKey("MEDIUM_STORAGE")) {
            baseSlots += 6;
        }
        if (upgrades.containsKey("SMALL_STORAGE")) {
            baseSlots += 3;
        }
        
        return Math.min(baseSlots, 54); // Max 54 slotů (double chest)
    }
    
    public boolean isStorageFull() {
        return storage.size() >= getMaxStorageSlots();
    }
    
    public void clearStorage() {
        storage.clear();
    }
    
    public boolean canUpgrade() {
        return tier < minionType.getMaxTier();
    }
    
    public double getUpgradeCost() {
        if (!canUpgrade()) return 0;
        
        return minionType.getUpgradeCost(tier + 1);
    }
    
    public String getDisplayName() {
        return minionType.getColoredName() + " Minion " + getRomanNumeral(tier);
    }
    
    public long getUptime() {
        return System.currentTimeMillis() - placedAt;
    }
    
    public String getUptimeFormatted() {
        long uptime = getUptime();
        long days = uptime / (24 * 60 * 60 * 1000);
        long hours = (uptime % (24 * 60 * 60 * 1000)) / (60 * 60 * 1000);
        long minutes = (uptime % (60 * 60 * 1000)) / (60 * 1000);
        
        if (days > 0) {
            return days + "d " + hours + "h " + minutes + "m";
        } else if (hours > 0) {
            return hours + "h " + minutes + "m";
        } else {
            return minutes + "m";
        }
    }
    
    private String getRomanNumeral(int number) {
        String[] romanNumerals = {"", "I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX", "X", "XI", "XII"};
        if (number >= 1 && number <= 12) {
            return romanNumerals[number];
        }
        return String.valueOf(number);
    }
    
    @Override
    public String toString() {
        return "Minion{" +
                "id=" + id +
                ", ownerUuid=" + ownerUuid +
                ", minionType=" + minionType +
                ", tier=" + tier +
                ", location=" + location +
                ", active=" + active +
                '}';
    }
}
