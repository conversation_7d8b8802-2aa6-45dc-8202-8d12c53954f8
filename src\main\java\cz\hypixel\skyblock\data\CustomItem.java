package cz.hypixel.skyblock.data;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.*;

/**
 * T<PERSON>ída pro custom předměty podle Hypixel SkyBlock
 */
public class CustomItem {
    
    private String id;
    private String displayName;
    private Material material;
    private ItemType itemType;
    private ItemRarity rarity;
    private ItemStats stats;
    private List<String> description;
    private List<String> abilities;
    private Map<String, Object> metadata;
    private List<String> requirements;
    private boolean stackable;
    private int maxStackSize;
    private String texture; // Pro custom textures
    
    public CustomItem() {
        this.stats = new ItemStats();
        this.description = new ArrayList<>();
        this.abilities = new ArrayList<>();
        this.metadata = new HashMap<>();
        this.requirements = new ArrayList<>();
        this.stackable = true;
        this.maxStackSize = 64;
    }
    
    public CustomItem(String id, String displayName, Material material, ItemType itemType, ItemRarity rarity) {
        this();
        this.id = id;
        this.displayName = displayName;
        this.material = material;
        this.itemType = itemType;
        this.rarity = rarity;
    }
    
    // Gettery a settery
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public Material getMaterial() {
        return material;
    }
    
    public void setMaterial(Material material) {
        this.material = material;
    }
    
    public ItemType getItemType() {
        return itemType;
    }
    
    public void setItemType(ItemType itemType) {
        this.itemType = itemType;
    }
    
    public ItemRarity getRarity() {
        return rarity;
    }
    
    public void setRarity(ItemRarity rarity) {
        this.rarity = rarity;
    }
    
    public ItemStats getStats() {
        return stats;
    }
    
    public void setStats(ItemStats stats) {
        this.stats = stats;
    }
    
    public List<String> getDescription() {
        return description;
    }
    
    public void setDescription(List<String> description) {
        this.description = description;
    }
    
    public List<String> getAbilities() {
        return abilities;
    }
    
    public void setAbilities(List<String> abilities) {
        this.abilities = abilities;
    }
    
    public Map<String, Object> getMetadata() {
        return metadata;
    }
    
    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }
    
    public List<String> getRequirements() {
        return requirements;
    }
    
    public void setRequirements(List<String> requirements) {
        this.requirements = requirements;
    }
    
    public boolean isStackable() {
        return stackable;
    }
    
    public void setStackable(boolean stackable) {
        this.stackable = stackable;
    }
    
    public int getMaxStackSize() {
        return maxStackSize;
    }
    
    public void setMaxStackSize(int maxStackSize) {
        this.maxStackSize = maxStackSize;
    }
    
    public String getTexture() {
        return texture;
    }
    
    public void setTexture(String texture) {
        this.texture = texture;
    }
    
    /**
     * Vytvoří ItemStack z custom předmětu
     */
    public ItemStack createItemStack() {
        return createItemStack(1);
    }
    
    public ItemStack createItemStack(int amount) {
        ItemStack item = new ItemStack(material, amount);
        ItemMeta meta = item.getItemMeta();
        
        if (meta != null) {
            // Nastavení display name s rarity barvou
            meta.setDisplayName(rarity.getColorCode() + displayName);
            
            // Vytvoření lore
            List<String> lore = createLore();
            meta.setLore(lore);
            
            // Přidat custom NBT data
            // TODO: Implementovat NBT systém pro SkyBlock ID a statistiky
            
            item.setItemMeta(meta);
        }
        
        return item;
    }
    
    /**
     * Vytvoří lore pro předmět
     */
    private List<String> createLore() {
        List<String> lore = new ArrayList<>();
        
        // Přidej popis
        if (!description.isEmpty()) {
            for (String line : description) {
                lore.add("§7" + line);
            }
            lore.add("");
        }
        
        // Přidej statistiky
        if (!stats.isEmpty()) {
            addStatsToLore(lore);
            lore.add("");
        }
        
        // Přidej abilities
        if (!abilities.isEmpty()) {
            for (String ability : abilities) {
                lore.add("§6" + ability);
            }
            lore.add("");
        }
        
        // Přidej requirements
        if (!requirements.isEmpty()) {
            lore.add("§7Requirements:");
            for (String requirement : requirements) {
                lore.add("§c" + requirement);
            }
            lore.add("");
        }
        
        // Přidej typ a rarity
        lore.add(itemType.getFormattedCategory());
        lore.add(rarity.getFormattedName());
        
        return lore;
    }
    
    /**
     * Přidá statistiky do lore
     */
    private void addStatsToLore(List<String> lore) {
        Map<String, Double> allStats = stats.getAllStats();
        
        // Combat stats
        addStatToLore(lore, "Damage", allStats.get("damage"), "§c", "+");
        addStatToLore(lore, "Strength", allStats.get("strength"), "§c", "+");
        addStatToLore(lore, "Crit Chance", allStats.get("crit_chance"), "§9", "+", "%");
        addStatToLore(lore, "Crit Damage", allStats.get("crit_damage"), "§9", "+", "%");
        addStatToLore(lore, "Attack Speed", allStats.get("attack_speed"), "§e", "+", "%");
        
        // Defensive stats
        addStatToLore(lore, "Defense", allStats.get("defense"), "§a", "+");
        addStatToLore(lore, "Health", allStats.get("health"), "§c", "+");
        addStatToLore(lore, "Speed", allStats.get("speed"), "§f", "+");
        
        // Magic stats
        addStatToLore(lore, "Intelligence", allStats.get("intelligence"), "§b", "+");
        addStatToLore(lore, "Mana", allStats.get("mana"), "§b", "+");
        
        // Skill stats
        addStatToLore(lore, "Mining Speed", allStats.get("mining_speed"), "§e", "+");
        addStatToLore(lore, "Mining Fortune", allStats.get("mining_fortune"), "§6", "+");
        addStatToLore(lore, "Farming Fortune", allStats.get("farming_fortune"), "§6", "+");
        addStatToLore(lore, "Foraging Fortune", allStats.get("foraging_fortune"), "§6", "+");
        addStatToLore(lore, "Fishing Speed", allStats.get("fishing_speed"), "§b", "+");
        addStatToLore(lore, "Sea Creature Chance", allStats.get("sea_creature_chance"), "§3", "+", "%");
    }
    
    /**
     * Přidá jednotlivou statistiku do lore
     */
    private void addStatToLore(List<String> lore, String statName, Double value, String color, String prefix) {
        addStatToLore(lore, statName, value, color, prefix, "");
    }
    
    private void addStatToLore(List<String> lore, String statName, Double value, String color, String prefix, String suffix) {
        if (value != null && value > 0) {
            String formattedValue = value % 1 == 0 ? String.valueOf(value.intValue()) : String.format("%.1f", value);
            lore.add("§7" + statName + ": " + color + prefix + formattedValue + suffix);
        }
    }
    
    /**
     * Zkontroluje, jestli hráč splňuje requirements
     */
    public boolean meetsRequirements(UUID playerUuid) {
        // TODO: Implementovat kontrolu requirements
        return true;
    }
    
    /**
     * Získá hodnotu předmětu
     */
    public double getValue() {
        double baseValue = rarity.getBaseValue() * 1000; // Base value podle rarity
        
        // Přidej hodnotu podle statistik
        double statsValue = stats.getAllStats().values().stream()
            .mapToDouble(Double::doubleValue)
            .sum() * 100;
        
        return baseValue + statsValue;
    }
    
    /**
     * Zkopíruje custom předmět
     */
    public CustomItem copy() {
        CustomItem copy = new CustomItem();
        copy.id = this.id;
        copy.displayName = this.displayName;
        copy.material = this.material;
        copy.itemType = this.itemType;
        copy.rarity = this.rarity;
        copy.stats = this.stats.copy();
        copy.description = new ArrayList<>(this.description);
        copy.abilities = new ArrayList<>(this.abilities);
        copy.metadata = new HashMap<>(this.metadata);
        copy.requirements = new ArrayList<>(this.requirements);
        copy.stackable = this.stackable;
        copy.maxStackSize = this.maxStackSize;
        copy.texture = this.texture;
        
        return copy;
    }
    
    @Override
    public String toString() {
        return "CustomItem{" +
                "id='" + id + '\'' +
                ", displayName='" + displayName + '\'' +
                ", rarity=" + rarity +
                ", itemType=" + itemType +
                '}';
    }
}
