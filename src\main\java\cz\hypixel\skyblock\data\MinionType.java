package cz.hypixel.skyblock.data;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

import java.util.HashMap;
import java.util.Map;

/**
 * Enum pro typy minionů podle Hypixel SkyBlock
 */
public enum MinionType {
    
    // Farming minioni
    WHEAT("Wheat Minion", "§e", Material.WHEAT, "farming", 26.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 3); // Více wheat na vyšších tierech
            return new ItemStack[]{new ItemStack(Material.WHEAT, amount)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.WHEAT, 64 * tier);
            if (tier > 1) recipe.put(Material.WHEAT_SEEDS, 32 * tier);
            if (tier > 5) recipe.put(Material.HAY_BLOCK, 16 * tier);
            return recipe;
        }
    },
    
    CARROT("Carrot Minion", "§6", Material.CARROT, "farming", 24.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 3);
            return new ItemStack[]{new ItemStack(Material.CARROT, amount)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.CARROT, 64 * tier);
            if (tier > 1) recipe.put(Material.GOLDEN_CARROT, 8 * tier);
            return recipe;
        }
    },
    
    POTATO("Potato Minion", "§e", Material.POTATO, "farming", 24.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 3);
            return new ItemStack[]{new ItemStack(Material.POTATO, amount)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.POTATO, 64 * tier);
            if (tier > 1) recipe.put(Material.BAKED_POTATO, 16 * tier);
            return recipe;
        }
    },
    
    PUMPKIN("Pumpkin Minion", "§6", Material.PUMPKIN, "farming", 28.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 4);
            return new ItemStack[]{new ItemStack(Material.PUMPKIN, amount)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.PUMPKIN, 32 * tier);
            if (tier > 1) recipe.put(Material.PUMPKIN_SEEDS, 16 * tier);
            return recipe;
        }
    },
    
    MELON("Melon Minion", "§a", Material.MELON, "farming", 28.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 4);
            return new ItemStack[]{new ItemStack(Material.MELON_SLICE, amount * 3)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.MELON_SLICE, 64 * tier);
            if (tier > 1) recipe.put(Material.MELON_SEEDS, 16 * tier);
            return recipe;
        }
    },
    
    // Mining minioni
    COBBLESTONE("Cobblestone Minion", "§7", Material.COBBLESTONE, "mining", 14.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 2);
            return new ItemStack[]{new ItemStack(Material.COBBLESTONE, amount)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.COBBLESTONE, 64 * tier);
            if (tier > 1) recipe.put(Material.STONE, 32 * tier);
            return recipe;
        }
    },
    
    COAL("Coal Minion", "§8", Material.COAL, "mining", 15.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 3);
            return new ItemStack[]{new ItemStack(Material.COAL, amount)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.COAL, 64 * tier);
            if (tier > 1) recipe.put(Material.COAL_BLOCK, 8 * tier);
            return recipe;
        }
    },
    
    IRON("Iron Minion", "§f", Material.IRON_INGOT, "mining", 18.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 4);
            return new ItemStack[]{new ItemStack(Material.RAW_IRON, amount)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.IRON_INGOT, 32 * tier);
            if (tier > 1) recipe.put(Material.IRON_BLOCK, 4 * tier);
            return recipe;
        }
    },
    
    GOLD("Gold Minion", "§6", Material.GOLD_INGOT, "mining", 22.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 5);
            return new ItemStack[]{new ItemStack(Material.RAW_GOLD, amount)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.GOLD_INGOT, 32 * tier);
            if (tier > 1) recipe.put(Material.GOLD_BLOCK, 4 * tier);
            return recipe;
        }
    },
    
    DIAMOND("Diamond Minion", "§b", Material.DIAMOND, "mining", 29.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 6);
            return new ItemStack[]{new ItemStack(Material.DIAMOND, amount)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.DIAMOND, 16 * tier);
            if (tier > 1) recipe.put(Material.DIAMOND_BLOCK, 2 * tier);
            return recipe;
        }
    },
    
    // Combat minioni
    ZOMBIE("Zombie Minion", "§2", Material.ROTTEN_FLESH, "combat", 26.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 3);
            return new ItemStack[]{new ItemStack(Material.ROTTEN_FLESH, amount)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.ROTTEN_FLESH, 64 * tier);
            if (tier > 1) recipe.put(Material.ZOMBIE_HEAD, tier);
            return recipe;
        }
    },
    
    SKELETON("Skeleton Minion", "§f", Material.BONE, "combat", 26.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 3);
            return new ItemStack[]{new ItemStack(Material.BONE, amount)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.BONE, 64 * tier);
            if (tier > 1) recipe.put(Material.SKELETON_SKULL, tier);
            return recipe;
        }
    },
    
    SPIDER("Spider Minion", "§8", Material.STRING, "combat", 26.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 3);
            return new ItemStack[]{new ItemStack(Material.STRING, amount), new ItemStack(Material.SPIDER_EYE, amount / 2)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.STRING, 64 * tier);
            recipe.put(Material.SPIDER_EYE, 32 * tier);
            return recipe;
        }
    },
    
    // Foraging minioni
    OAK("Oak Minion", "§6", Material.OAK_LOG, "foraging", 46.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 4);
            return new ItemStack[]{new ItemStack(Material.OAK_LOG, amount)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.OAK_LOG, 64 * tier);
            if (tier > 1) recipe.put(Material.OAK_SAPLING, 16 * tier);
            return recipe;
        }
    },
    
    BIRCH("Birch Minion", "§f", Material.BIRCH_LOG, "foraging", 46.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 4);
            return new ItemStack[]{new ItemStack(Material.BIRCH_LOG, amount)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.BIRCH_LOG, 64 * tier);
            if (tier > 1) recipe.put(Material.BIRCH_SAPLING, 16 * tier);
            return recipe;
        }
    },
    
    // Fishing minioni
    FISHING("Fishing Minion", "§9", Material.RAW_COD, "fishing", 26.0, 11) {
        @Override
        public ItemStack[] generateItems(int tier) {
            int amount = 1 + (tier / 3);
            // Různé typy ryb
            Material[] fishTypes = {Material.RAW_COD, Material.RAW_SALMON, Material.TROPICAL_FISH, Material.PUFFERFISH};
            Material fishType = fishTypes[(int) (Math.random() * fishTypes.length)];
            return new ItemStack[]{new ItemStack(fishType, amount)};
        }
        
        @Override
        public Map<Material, Integer> getCraftingRecipe(int tier) {
            Map<Material, Integer> recipe = new HashMap<>();
            recipe.put(Material.RAW_COD, 32 * tier);
            recipe.put(Material.FISHING_ROD, tier);
            return recipe;
        }
    };
    
    private final String displayName;
    private final String colorCode;
    private final Material displayMaterial;
    private final String category;
    private final double baseActionTime; // v sekundách
    private final int maxTier;
    
    MinionType(String displayName, String colorCode, Material displayMaterial, String category, 
               double baseActionTime, int maxTier) {
        this.displayName = displayName;
        this.colorCode = colorCode;
        this.displayMaterial = displayMaterial;
        this.category = category;
        this.baseActionTime = baseActionTime;
        this.maxTier = maxTier;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getColorCode() {
        return colorCode;
    }
    
    public String getColoredName() {
        return colorCode + displayName;
    }
    
    public Material getDisplayMaterial() {
        return displayMaterial;
    }
    
    public String getCategory() {
        return category;
    }
    
    public double getBaseActionTime() {
        return baseActionTime;
    }
    
    public int getMaxTier() {
        return maxTier;
    }
    
    /**
     * Generuje předměty pro daný tier (implementováno v každém typu)
     */
    public abstract ItemStack[] generateItems(int tier);
    
    /**
     * Získá crafting recept pro daný tier (implementováno v každém typu)
     */
    public abstract Map<Material, Integer> getCraftingRecipe(int tier);
    
    /**
     * Spočítá cenu upgradu na další tier
     */
    public double getUpgradeCost(int targetTier) {
        if (targetTier <= 1 || targetTier > maxTier) return 0;
        
        // Exponenciální růst ceny
        return Math.pow(2, targetTier - 1) * 10000; // Začíná na 20k pro tier 2
    }
    
    /**
     * Získá typ podle názvu
     */
    public static MinionType fromString(String name) {
        for (MinionType type : values()) {
            if (type.name().equalsIgnoreCase(name) || 
                type.displayName.equalsIgnoreCase(name)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * Získá všechny typy podle kategorie
     */
    public static MinionType[] getByCategory(String category) {
        return java.util.Arrays.stream(values())
            .filter(type -> type.category.equalsIgnoreCase(category))
            .toArray(MinionType[]::new);
    }
}
