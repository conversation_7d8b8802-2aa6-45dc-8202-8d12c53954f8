package cz.hypixel.skyblock.data;

/**
 * Enum pro typy Bazaar orderů
 */
public enum BazaarOrderType {
    
    BUY("Nákup", "§a", "<PERSON><PERSON><PERSON> koupit"),
    SELL("Prodej", "§c", "<PERSON><PERSON><PERSON> prodat");
    
    private final String displayName;
    private final String colorCode;
    private final String description;
    
    BazaarOrderType(String displayName, String colorCode, String description) {
        this.displayName = displayName;
        this.colorCode = colorCode;
        this.description = description;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getColorCode() {
        return colorCode;
    }
    
    public String getColoredName() {
        return colorCode + displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    /**
     * Získá opačný typ orderu
     */
    public BazaarOrderType getOpposite() {
        return this == BUY ? SELL : BUY;
    }
    
    /**
     * Získá typ podle názvu
     */
    public static BazaarOrderType fromString(String name) {
        for (BazaarOrderType type : values()) {
            if (type.name().equalsIgnoreCase(name) || 
                type.displayName.equalsIgnoreCase(name)) {
                return type;
            }
        }
        return null;
    }
}
