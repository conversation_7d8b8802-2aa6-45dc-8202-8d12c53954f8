package cz.hypixel.skyblock.managers;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.Minion;
import cz.hypixel.skyblock.data.MinionType;
import cz.hypixel.skyblock.database.PlayerData;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;

/**
 * Manager pro Minion systém podle Hypixel SkyBlock
 */
public class MinionManager {

    private final HypixelSkyBlockCZ plugin;
    private final Logger logger;
    private final DataSource dataSource;
    private final Map<Integer, Minion> activeMinions;
    private final Map<UUID, List<Minion>> playerMinions;
    
    // Limity
    private static final int MAX_MINIONS_PER_PLAYER = 25; // Základní limit
    private static final int MINION_TICK_INTERVAL = 20; // 1 sekunda

    public MinionManager(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.dataSource = plugin.getPlayerDataManager().getDataSource();
        this.activeMinions = new HashMap<>();
        this.playerMinions = new HashMap<>();
        
        // Spusť minion tick systém
        startMinionTickSystem();
        
        // Načti všechny aktivní miniony při startu
        loadAllMinions();
        
        logger.info("MinionManager inicializován s " + activeMinions.size() + " aktivními miniony");
    }

    /**
     * Umístí nového miniona
     */
    public CompletableFuture<Boolean> placeMinion(Player player, MinionType minionType, int tier, Location location) {
        return CompletableFuture.supplyAsync(() -> {
            UUID playerUuid = player.getUniqueId();
            
            // Zkontroluj limit minionů
            if (getPlayerMinionCount(playerUuid) >= getMaxMinionsForPlayer(playerUuid)) {
                player.sendMessage("§cDosáhl jsi limitu minionů! (Max: " + getMaxMinionsForPlayer(playerUuid) + ")");
                return false;
            }
            
            // Zkontroluj, jestli není na pozici už jiný minion
            if (getMinionAtLocation(location) != null) {
                player.sendMessage("§cNa této pozici už je minion!");
                return false;
            }
            
            // Zkontroluj materiály pro crafting
            if (!hasRequiredMaterials(player, minionType, tier)) {
                player.sendMessage("§cNemáš potřebné materiály pro vytvoření miniona!");
                showRequiredMaterials(player, minionType, tier);
                return false;
            }
            
            // Odeber materiály
            removeRequiredMaterials(player, minionType, tier);
            
            // Vytvoř miniona
            Minion minion = new Minion(playerUuid, minionType, tier, location);
            
            // Ulož do databáze
            String sql = """
                INSERT INTO minions (owner_uuid, minion_type, tier, world, x, y, z, yaw, pitch, 
                                   active, last_action, placed_at, storage, upgrades, fuel_remaining, skin_type)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;
            
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
                
                stmt.setString(1, playerUuid.toString());
                stmt.setString(2, minionType.name());
                stmt.setInt(3, tier);
                stmt.setString(4, location.getWorld().getName());
                stmt.setDouble(5, location.getX());
                stmt.setDouble(6, location.getY());
                stmt.setDouble(7, location.getZ());
                stmt.setFloat(8, location.getYaw());
                stmt.setFloat(9, location.getPitch());
                stmt.setBoolean(10, minion.isActive());
                stmt.setLong(11, minion.getLastAction());
                stmt.setLong(12, minion.getPlacedAt());
                stmt.setString(13, serializeStorage(minion.getStorage()));
                stmt.setString(14, serializeUpgrades(minion.getUpgrades()));
                stmt.setDouble(15, minion.getFuelRemaining());
                stmt.setString(16, minion.getSkinType());
                
                int result = stmt.executeUpdate();
                
                if (result > 0) {
                    // Získej vygenerované ID
                    try (ResultSet rs = stmt.getGeneratedKeys()) {
                        if (rs.next()) {
                            minion.setId(rs.getInt(1));
                        }
                    }
                    
                    // Přidej do cache
                    activeMinions.put(minion.getId(), minion);
                    playerMinions.computeIfAbsent(playerUuid, k -> new ArrayList<>()).add(minion);
                    
                    // Umísti blok miniona ve světě
                    location.getBlock().setType(Material.PLAYER_HEAD);
                    
                    player.sendMessage("§aÚspěšně jsi umístil " + minion.getDisplayName() + "!");
                    player.sendMessage("§7Pozice: §e" + formatLocation(location));
                    
                    return true;
                }
                
            } catch (SQLException e) {
                logger.severe("Chyba při umísťování miniona: " + e.getMessage());
            }
            
            return false;
        });
    }

    /**
     * Odebere miniona
     */
    public CompletableFuture<Boolean> removeMinion(Player player, Minion minion) {
        return CompletableFuture.supplyAsync(() -> {
            if (!minion.getOwnerUuid().equals(player.getUniqueId())) {
                player.sendMessage("§cToto není tvůj minion!");
                return false;
            }
            
            // Vrať předměty ze storage
            for (ItemStack item : minion.getStorage()) {
                if (item != null) {
                    player.getInventory().addItem(item);
                }
            }
            
            // Vrať minion item
            ItemStack minionItem = createMinionItem(minion.getMinionType(), minion.getTier());
            player.getInventory().addItem(minionItem);
            
            // Odeber z databáze
            String sql = "DELETE FROM minions WHERE id = ?";
            
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setInt(1, minion.getId());
                int result = stmt.executeUpdate();
                
                if (result > 0) {
                    // Odeber z cache
                    activeMinions.remove(minion.getId());
                    List<Minion> playerMinionList = playerMinions.get(minion.getOwnerUuid());
                    if (playerMinionList != null) {
                        playerMinionList.remove(minion);
                    }
                    
                    // Odeber blok ze světa
                    minion.getLocation().getBlock().setType(Material.AIR);
                    
                    player.sendMessage("§aÚspěšně jsi odebral " + minion.getDisplayName() + "!");
                    
                    return true;
                }
                
            } catch (SQLException e) {
                logger.severe("Chyba při odebírání miniona: " + e.getMessage());
            }
            
            return false;
        });
    }

    /**
     * Upgraduje miniona na vyšší tier
     */
    public CompletableFuture<Boolean> upgradeMinion(Player player, Minion minion) {
        return CompletableFuture.supplyAsync(() -> {
            if (!minion.getOwnerUuid().equals(player.getUniqueId())) {
                player.sendMessage("§cToto není tvůj minion!");
                return false;
            }
            
            if (!minion.canUpgrade()) {
                player.sendMessage("§cTento minion je už na maximálním tieru!");
                return false;
            }
            
            int newTier = minion.getTier() + 1;
            double upgradeCost = minion.getUpgradeCost();
            
            // Zkontroluj mince
            PlayerData playerData = plugin.getPlayerDataManager().loadPlayerData(player.getUniqueId()).join();
            if (playerData == null || playerData.getCoins() < upgradeCost) {
                player.sendMessage("§cNemáš dostatek mincí na upgrade!");
                player.sendMessage("§7Potřebuješ: §e" + BankManager.formatCoins(upgradeCost));
                return false;
            }
            
            // Zkontroluj materiály
            if (!hasRequiredMaterials(player, minion.getMinionType(), newTier)) {
                player.sendMessage("§cNemáš potřebné materiály pro upgrade!");
                showRequiredMaterials(player, minion.getMinionType(), newTier);
                return false;
            }
            
            // Proveď upgrade
            playerData.removeCoins(upgradeCost);
            plugin.getPlayerDataManager().savePlayerData(playerData);
            
            removeRequiredMaterials(player, minion.getMinionType(), newTier);
            
            minion.setTier(newTier);
            updateMinionInDatabase(minion);
            
            player.sendMessage("§aÚspěšně jsi upgradoval miniona na tier " + newTier + "!");
            player.sendMessage("§7Cena: §c-" + BankManager.formatCoins(upgradeCost));
            player.sendMessage("§7Nová rychlost: §e" + String.format("%.1f", minion.getActionSpeed()) + "s");
            
            return true;
        });
    }

    /**
     * Přidá fuel do miniona
     */
    public CompletableFuture<Boolean> addFuel(Player player, Minion minion, Material fuelType, int amount) {
        return CompletableFuture.supplyAsync(() -> {
            if (!minion.getOwnerUuid().equals(player.getUniqueId())) {
                player.sendMessage("§cToto není tvůj minion!");
                return false;
            }
            
            // Zkontroluj, jestli má hráč fuel
            if (countItems(player, fuelType) < amount) {
                player.sendMessage("§cNemáš dostatek fuel!");
                return false;
            }
            
            // Spočítej fuel hodnotu
            double fuelValue = getFuelValue(fuelType) * amount;
            
            // Přidej fuel
            minion.setFuelRemaining(minion.getFuelRemaining() + fuelValue);
            
            // Odeber fuel z inventáře
            removeItems(player, fuelType, amount);
            
            // Aktualizuj v databázi
            updateMinionInDatabase(minion);
            
            player.sendMessage("§aPřidal jsi fuel do miniona!");
            player.sendMessage("§7Fuel: §e" + String.format("%.1f", minion.getFuelRemaining()) + " hodin");
            
            return true;
        });
    }

    /**
     * Spustí tick systém pro miniony
     */
    private void startMinionTickSystem() {
        Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            for (Minion minion : activeMinions.values()) {
                if (minion.isActive() && minion.canPerformAction()) {
                    processMinionAction(minion);
                }
            }
        }, MINION_TICK_INTERVAL, MINION_TICK_INTERVAL);
    }

    /**
     * Zpracuje akci miniona
     */
    private void processMinionAction(Minion minion) {
        try {
            // Proveď akci miniona
            minion.performAction();
            
            // Aktualizuj v databázi (pouze storage a fuel)
            updateMinionStorageAndFuel(minion);
            
            // Přidej do kolekcí a skills
            addToCollectionsAndSkills(minion);
            
        } catch (Exception e) {
            logger.warning("Chyba při zpracování akce miniona " + minion.getId() + ": " + e.getMessage());
        }
    }

    /**
     * Přidá produkci miniona do kolekcí a skills
     */
    private void addToCollectionsAndSkills(Minion minion) {
        UUID ownerUuid = minion.getOwnerUuid();
        String category = minion.getMinionType().getCategory();
        
        // Přidej XP do příslušného skillu
        double xpAmount = 1.0 * minion.getTier(); // Více XP na vyšších tierech
        
        switch (category) {
            case "farming" -> plugin.getSkillsManager().addExperience(ownerUuid, "FARMING", xpAmount);
            case "mining" -> plugin.getSkillsManager().addExperience(ownerUuid, "MINING", xpAmount);
            case "combat" -> plugin.getSkillsManager().addExperience(ownerUuid, "COMBAT", xpAmount);
            case "foraging" -> plugin.getSkillsManager().addExperience(ownerUuid, "FORAGING", xpAmount);
            case "fishing" -> plugin.getSkillsManager().addExperience(ownerUuid, "FISHING", xpAmount);
        }
        
        // Přidej do kolekcí
        ItemStack[] generatedItems = minion.getMinionType().generateItems(minion.getTier());
        for (ItemStack item : generatedItems) {
            if (item != null) {
                plugin.getCollectionsManager().addToCollection(ownerUuid, item.getType().name(), item.getAmount());
            }
        }
    }

    /**
     * Načte všechny miniony z databáze
     */
    private void loadAllMinions() {
        String sql = """
            SELECT id, owner_uuid, minion_type, tier, world, x, y, z, yaw, pitch, 
                   active, last_action, placed_at, storage, upgrades, fuel_remaining, skin_type
            FROM minions WHERE active = true
        """;
        
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql);
             ResultSet rs = stmt.executeQuery()) {
            
            while (rs.next()) {
                Minion minion = createMinionFromResultSet(rs);
                if (minion != null) {
                    activeMinions.put(minion.getId(), minion);
                    playerMinions.computeIfAbsent(minion.getOwnerUuid(), k -> new ArrayList<>()).add(minion);
                }
            }
            
        } catch (SQLException e) {
            logger.severe("Chyba při načítání minionů: " + e.getMessage());
        }
    }

    /**
     * Vytvoří Minion z ResultSet
     */
    private Minion createMinionFromResultSet(ResultSet rs) throws SQLException {
        Minion minion = new Minion();
        minion.setId(rs.getInt("id"));
        minion.setOwnerUuid(UUID.fromString(rs.getString("owner_uuid")));
        minion.setMinionType(MinionType.fromString(rs.getString("minion_type")));
        minion.setTier(rs.getInt("tier"));
        
        // Vytvoř Location
        String worldName = rs.getString("world");
        if (Bukkit.getWorld(worldName) != null) {
            Location location = new Location(
                Bukkit.getWorld(worldName),
                rs.getDouble("x"),
                rs.getDouble("y"),
                rs.getDouble("z"),
                rs.getFloat("yaw"),
                rs.getFloat("pitch")
            );
            minion.setLocation(location);
        } else {
            return null; // Svět neexistuje
        }
        
        minion.setActive(rs.getBoolean("active"));
        minion.setLastAction(rs.getLong("last_action"));
        minion.setPlacedAt(rs.getLong("placed_at"));
        minion.setStorage(deserializeStorage(rs.getString("storage")));
        minion.setUpgrades(deserializeUpgrades(rs.getString("upgrades")));
        minion.setFuelRemaining(rs.getDouble("fuel_remaining"));
        minion.setSkinType(rs.getString("skin_type"));
        
        return minion;
    }

    /**
     * Získá miniona na pozici
     */
    public Minion getMinionAtLocation(Location location) {
        return activeMinions.values().stream()
            .filter(minion -> minion.getLocation().getBlockX() == location.getBlockX() &&
                            minion.getLocation().getBlockY() == location.getBlockY() &&
                            minion.getLocation().getBlockZ() == location.getBlockZ() &&
                            minion.getLocation().getWorld().equals(location.getWorld()))
            .findFirst()
            .orElse(null);
    }

    /**
     * Získá počet minionů hráče
     */
    public int getPlayerMinionCount(UUID playerUuid) {
        List<Minion> minions = playerMinions.get(playerUuid);
        return minions != null ? minions.size() : 0;
    }

    /**
     * Získá maximální počet minionů pro hráče
     */
    public int getMaxMinionsForPlayer(UUID playerUuid) {
        // TODO: Implementovat podle crafted minion slots
        return MAX_MINIONS_PER_PLAYER;
    }

    /**
     * Získá všechny miniony hráče
     */
    public List<Minion> getPlayerMinions(UUID playerUuid) {
        return playerMinions.getOrDefault(playerUuid, new ArrayList<>());
    }

    /**
     * Zkontroluje potřebné materiály
     */
    private boolean hasRequiredMaterials(Player player, MinionType minionType, int tier) {
        Map<Material, Integer> required = minionType.getCraftingRecipe(tier);

        for (Map.Entry<Material, Integer> entry : required.entrySet()) {
            if (countItems(player, entry.getKey()) < entry.getValue()) {
                return false;
            }
        }

        return true;
    }

    /**
     * Odebere potřebné materiály
     */
    private void removeRequiredMaterials(Player player, MinionType minionType, int tier) {
        Map<Material, Integer> required = minionType.getCraftingRecipe(tier);

        for (Map.Entry<Material, Integer> entry : required.entrySet()) {
            removeItems(player, entry.getKey(), entry.getValue());
        }
    }

    /**
     * Zobrazí potřebné materiály
     */
    private void showRequiredMaterials(Player player, MinionType minionType, int tier) {
        Map<Material, Integer> required = minionType.getCraftingRecipe(tier);

        player.sendMessage("§7Potřebné materiály pro " + minionType.getColoredName() + " Tier " + tier + ":");
        for (Map.Entry<Material, Integer> entry : required.entrySet()) {
            int has = countItems(player, entry.getKey());
            int needed = entry.getValue();
            String color = has >= needed ? "§a" : "§c";

            player.sendMessage("§7- " + entry.getKey().name() + ": " + color + has + "§7/" + needed);
        }
    }

    /**
     * Spočítá předměty v inventáři
     */
    private int countItems(Player player, Material material) {
        int count = 0;
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.getType() == material) {
                count += item.getAmount();
            }
        }
        return count;
    }

    /**
     * Odebere předměty z inventáře
     */
    private void removeItems(Player player, Material material, int amount) {
        ItemStack itemToRemove = new ItemStack(material, amount);
        player.getInventory().removeItem(itemToRemove);
    }

    /**
     * Vytvoří minion item
     */
    private ItemStack createMinionItem(MinionType minionType, int tier) {
        ItemStack item = new ItemStack(Material.PLAYER_HEAD);
        // TODO: Nastavit custom texture a lore
        return item;
    }

    /**
     * Získá fuel hodnotu materiálu
     */
    private double getFuelValue(Material fuelType) {
        return switch (fuelType) {
            case COAL -> 0.5; // 30 minut
            case COAL_BLOCK -> 4.5; // 4.5 hodiny
            case LAVA_BUCKET -> 12.0; // 12 hodin
            case BLAZE_ROD -> 24.0; // 24 hodin
            default -> 0.0;
        };
    }

    /**
     * Aktualizuje miniona v databázi
     */
    private void updateMinionInDatabase(Minion minion) {
        String sql = """
            UPDATE minions
            SET tier = ?, active = ?, last_action = ?, storage = ?, upgrades = ?, fuel_remaining = ?
            WHERE id = ?
        """;

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setInt(1, minion.getTier());
            stmt.setBoolean(2, minion.isActive());
            stmt.setLong(3, minion.getLastAction());
            stmt.setString(4, serializeStorage(minion.getStorage()));
            stmt.setString(5, serializeUpgrades(minion.getUpgrades()));
            stmt.setDouble(6, minion.getFuelRemaining());
            stmt.setInt(7, minion.getId());

            stmt.executeUpdate();

        } catch (SQLException e) {
            logger.warning("Chyba při aktualizaci miniona: " + e.getMessage());
        }
    }

    /**
     * Aktualizuje pouze storage a fuel miniona
     */
    private void updateMinionStorageAndFuel(Minion minion) {
        String sql = "UPDATE minions SET last_action = ?, storage = ?, fuel_remaining = ? WHERE id = ?";

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, minion.getLastAction());
            stmt.setString(2, serializeStorage(minion.getStorage()));
            stmt.setDouble(3, minion.getFuelRemaining());
            stmt.setInt(4, minion.getId());

            stmt.executeUpdate();

        } catch (SQLException e) {
            logger.warning("Chyba při aktualizaci storage miniona: " + e.getMessage());
        }
    }

    /**
     * Serializuje storage do stringu
     */
    private String serializeStorage(List<ItemStack> storage) {
        if (storage == null || storage.isEmpty()) {
            return "[]";
        }

        StringBuilder sb = new StringBuilder("[");
        boolean first = true;
        for (ItemStack item : storage) {
            if (!first) sb.append(",");
            if (item != null) {
                sb.append(item.getType().name()).append(":").append(item.getAmount());
            } else {
                sb.append("null");
            }
            first = false;
        }
        sb.append("]");

        return sb.toString();
    }

    /**
     * Deserializuje storage ze stringu
     */
    private List<ItemStack> deserializeStorage(String data) {
        List<ItemStack> storage = new ArrayList<>();

        if (data == null || data.equals("[]")) {
            return storage;
        }

        try {
            data = data.substring(1, data.length() - 1); // Odeber []
            if (data.isEmpty()) return storage;

            String[] items = data.split(",");
            for (String itemStr : items) {
                if (itemStr.equals("null")) {
                    storage.add(null);
                } else {
                    String[] parts = itemStr.split(":");
                    if (parts.length == 2) {
                        Material material = Material.valueOf(parts[0]);
                        int amount = Integer.parseInt(parts[1]);
                        storage.add(new ItemStack(material, amount));
                    }
                }
            }
        } catch (Exception e) {
            logger.warning("Chyba při deserializaci storage: " + e.getMessage());
        }

        return storage;
    }

    /**
     * Serializuje upgrady do stringu
     */
    private String serializeUpgrades(Map<String, Object> upgrades) {
        if (upgrades == null || upgrades.isEmpty()) {
            return "{}";
        }

        StringBuilder sb = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<String, Object> entry : upgrades.entrySet()) {
            if (!first) sb.append(",");
            sb.append("\"").append(entry.getKey()).append("\":\"").append(entry.getValue()).append("\"");
            first = false;
        }
        sb.append("}");

        return sb.toString();
    }

    /**
     * Deserializuje upgrady ze stringu
     */
    private Map<String, Object> deserializeUpgrades(String data) {
        Map<String, Object> upgrades = new HashMap<>();

        if (data == null || data.equals("{}")) {
            return upgrades;
        }

        try {
            data = data.substring(1, data.length() - 1); // Odeber {}
            if (data.isEmpty()) return upgrades;

            String[] pairs = data.split(",");
            for (String pair : pairs) {
                String[] keyValue = pair.split(":");
                if (keyValue.length == 2) {
                    String key = keyValue[0].replace("\"", "").trim();
                    String value = keyValue[1].replace("\"", "").trim();
                    upgrades.put(key, value);
                }
            }
        } catch (Exception e) {
            logger.warning("Chyba při deserializaci upgradů: " + e.getMessage());
        }

        return upgrades;
    }

    /**
     * Formátuje lokaci pro zobrazení
     */
    private String formatLocation(Location location) {
        return String.format("%.0f, %.0f, %.0f", location.getX(), location.getY(), location.getZ());
    }
}
