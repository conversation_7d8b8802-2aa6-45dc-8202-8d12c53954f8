package cz.hypixel.skyblock.data;

import org.bukkit.Material;

import java.util.Arrays;
import java.util.List;

/**
 * Enum pro SkyBlock enchantmenty podle Hypixel SkyBlock
 */
public enum SkyBlockEnchantment {
    
    // Combat enchantmenty
    SHARPNESS("Sharpness", "§c", 5, Arrays.asList(Material.DIAMOND_SWORD, Material.IRON_SWORD, Material.GOLDEN_SWORD, Material.STONE_SWORD, Material.WOODEN_SWORD)) {
        @Override
        public String getDescription(int level) {
            return "§7Zvyšuje damage o §c+" + (level * 5) + "%";
        }
        
        @Override
        public double getStatBonus(int level) {
            return level * 5.0; // +5% damage za level
        }
    },
    
    CRITICAL("Critical", "§9", 7, Arrays.asList(Material.DIAMOND_SWORD, Material.BOW)) {
        @Override
        public String getDescription(int level) {
            return "§7Zvyšuje crit chance o §9+" + (level * 2) + "%";
        }
        
        @Override
        public double getStatBonus(int level) {
            return level * 2.0; // +2% crit chance za level
        }
    },
    
    LOOTING("Looting", "§b", 5, Arrays.asList(Material.DIAMOND_SWORD, Material.IRON_SWORD)) {
        @Override
        public String getDescription(int level) {
            return "§7Zvyšuje mob drops o §b+" + (level * 15) + "%";
        }
        
        @Override
        public double getStatBonus(int level) {
            return level * 15.0; // +15% drops za level
        }
    },
    
    // Mining enchantmenty
    EFFICIENCY("Efficiency", "§e", 10, Arrays.asList(Material.DIAMOND_PICKAXE, Material.IRON_PICKAXE, Material.GOLDEN_PICKAXE)) {
        @Override
        public String getDescription(int level) {
            return "§7Zvyšuje mining speed o §e+" + (level * 10) + "%";
        }
        
        @Override
        public double getStatBonus(int level) {
            return level * 10.0; // +10% mining speed za level
        }
    },
    
    FORTUNE("Fortune", "§6", 5, Arrays.asList(Material.DIAMOND_PICKAXE, Material.IRON_PICKAXE)) {
        @Override
        public String getDescription(int level) {
            return "§7Zvyšuje mining fortune o §6+" + (level * 20);
        }
        
        @Override
        public double getStatBonus(int level) {
            return level * 20.0; // +20 fortune za level
        }
    },
    
    SMELTING_TOUCH("Smelting Touch", "§c", 1, Arrays.asList(Material.DIAMOND_PICKAXE)) {
        @Override
        public String getDescription(int level) {
            return "§7Automaticky smelts bloky";
        }
        
        @Override
        public double getStatBonus(int level) {
            return 0; // Speciální efekt
        }
    },
    
    // Farming enchantmenty
    HARVESTING("Harvesting", "§a", 6, Arrays.asList(Material.DIAMOND_HOE, Material.IRON_HOE)) {
        @Override
        public String getDescription(int level) {
            return "§7Zvyšuje farming fortune o §a+" + (level * 12.5);
        }
        
        @Override
        public double getStatBonus(int level) {
            return level * 12.5; // +12.5 fortune za level
        }
    },
    
    TURBO_CROP("Turbo-Crop", "§2", 5, Arrays.asList(Material.DIAMOND_HOE)) {
        @Override
        public String getDescription(int level) {
            return "§7Šance na instant regrow §2+" + (level * 5) + "%";
        }
        
        @Override
        public double getStatBonus(int level) {
            return level * 5.0; // +5% šance za level
        }
    },
    
    // Foraging enchantmenty
    TELEKINESIS("Telekinesis", "§d", 1, Arrays.asList(Material.DIAMOND_AXE, Material.DIAMOND_PICKAXE, Material.DIAMOND_HOE)) {
        @Override
        public String getDescription(int level) {
            return "§7Předměty jdou přímo do inventáře";
        }
        
        @Override
        public double getStatBonus(int level) {
            return 0; // Speciální efekt
        }
    },
    
    // Fishing enchantmenty
    LUCK_OF_THE_SEA("Luck of the Sea", "§b", 6, Arrays.asList(Material.FISHING_ROD)) {
        @Override
        public String getDescription(int level) {
            return "§7Zvyšuje sea creature chance o §b+" + (level * 2) + "%";
        }
        
        @Override
        public double getStatBonus(int level) {
            return level * 2.0; // +2% sea creature chance za level
        }
    },
    
    LURE("Lure", "§e", 6, Arrays.asList(Material.FISHING_ROD)) {
        @Override
        public String getDescription(int level) {
            return "§7Zvyšuje fishing speed o §e+" + (level * 5) + "%";
        }
        
        @Override
        public double getStatBonus(int level) {
            return level * 5.0; // +5% fishing speed za level
        }
    },
    
    // Armor enchantmenty
    PROTECTION("Protection", "§7", 7, Arrays.asList(Material.DIAMOND_HELMET, Material.DIAMOND_CHESTPLATE, Material.DIAMOND_LEGGINGS, Material.DIAMOND_BOOTS)) {
        @Override
        public String getDescription(int level) {
            return "§7Zvyšuje defense o §7+" + (level * 3);
        }
        
        @Override
        public double getStatBonus(int level) {
            return level * 3.0; // +3 defense za level
        }
    },
    
    GROWTH("Growth", "§a", 7, Arrays.asList(Material.DIAMOND_HELMET, Material.DIAMOND_CHESTPLATE, Material.DIAMOND_LEGGINGS, Material.DIAMOND_BOOTS)) {
        @Override
        public String getDescription(int level) {
            return "§7Zvyšuje health o §a+" + (level * 15);
        }
        
        @Override
        public double getStatBonus(int level) {
            return level * 15.0; // +15 health za level
        }
    },
    
    // Speciální enchantmenty
    ULTIMATE_WISE("Ultimate Wise", "§b", 5, Arrays.asList(Material.DIAMOND_SWORD, Material.BOW)) {
        @Override
        public String getDescription(int level) {
            return "§7Snižuje mana cost o §b-" + (level * 10) + "%";
        }
        
        @Override
        public double getStatBonus(int level) {
            return level * 10.0; // -10% mana cost za level
        }
    };
    
    private final String displayName;
    private final String colorCode;
    private final int maxLevel;
    private final List<Material> applicableItems;
    
    SkyBlockEnchantment(String displayName, String colorCode, int maxLevel, List<Material> applicableItems) {
        this.displayName = displayName;
        this.colorCode = colorCode;
        this.maxLevel = maxLevel;
        this.applicableItems = applicableItems;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getColorCode() {
        return colorCode;
    }
    
    public String getColoredName() {
        return colorCode + displayName;
    }
    
    public int getMaxLevel() {
        return maxLevel;
    }
    
    public List<Material> getApplicableItems() {
        return applicableItems;
    }
    
    public String getFormattedName(int level) {
        if (maxLevel == 1) {
            return colorCode + displayName;
        } else {
            return colorCode + displayName + " " + getRomanNumeral(level);
        }
    }
    
    /**
     * Získá popis enchantmentu (implementováno v každém typu)
     */
    public abstract String getDescription(int level);
    
    /**
     * Získá stat bonus (implementováno v každém typu)
     */
    public abstract double getStatBonus(int level);
    
    /**
     * Zkontroluje, jestli lze aplikovat na předmět
     */
    public boolean canApplyTo(Material material) {
        return applicableItems.contains(material);
    }
    
    /**
     * Získá cenu enchantmentu
     */
    public double getEnchantCost(int level) {
        return Math.pow(2, level - 1) * 1000; // Exponenciální růst ceny
    }
    
    /**
     * Získá enchantment podle názvu
     */
    public static SkyBlockEnchantment fromString(String name) {
        for (SkyBlockEnchantment enchant : values()) {
            if (enchant.name().equalsIgnoreCase(name) || 
                enchant.displayName.equalsIgnoreCase(name)) {
                return enchant;
            }
        }
        return null;
    }
    
    /**
     * Získá všechny enchantmenty pro materiál
     */
    public static SkyBlockEnchantment[] getForMaterial(Material material) {
        return Arrays.stream(values())
            .filter(enchant -> enchant.canApplyTo(material))
            .toArray(SkyBlockEnchantment[]::new);
    }
    
    /**
     * Převede číslo na římské číslice
     */
    private String getRomanNumeral(int number) {
        String[] romanNumerals = {"", "I", "II", "III", "IV", "V", "VI", "VII", "VIII", "IX", "X"};
        if (number >= 1 && number <= 10) {
            return romanNumerals[number];
        }
        return String.valueOf(number);
    }
}
