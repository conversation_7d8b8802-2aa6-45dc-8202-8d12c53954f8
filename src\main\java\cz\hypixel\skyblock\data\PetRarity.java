package cz.hypixel.skyblock.data;

/**
 * Enum pro rarity mazlíčků podle Hypixel SkyBlock
 */
public enum PetRarity {
    
    COMMON("Common", "§f", 50, 1.0),
    UNCOMMON("Uncommon", "§a", 75, 1.2),
    RARE("Rare", "§9", 90, 1.4),
    EPIC("Epic", "§5", 95, 1.6),
    LEGENDARY("Legendary", "§6", 100, 2.0),
    MYTHIC("Mythic", "§d", 100, 2.5);
    
    private final String displayName;
    private final String colorCode;
    private final int maxLevel;
    private final double statMultiplier;
    
    PetRarity(String displayName, String colorCode, int maxLevel, double statMultiplier) {
        this.displayName = displayName;
        this.colorCode = colorCode;
        this.maxLevel = maxLevel;
        this.statMultiplier = statMultiplier;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getColorCode() {
        return colorCode;
    }
    
    public String getColoredName() {
        return colorCode + displayName;
    }
    
    public int getMaxLevel() {
        return maxLevel;
    }
    
    public double getStatMultiplier() {
        return statMultiplier;
    }
    
    /**
     * Získá rarity podle názvu
     */
    public static PetRarity fromString(String name) {
        for (PetRarity rarity : values()) {
            if (rarity.name().equalsIgnoreCase(name) || 
                rarity.displayName.equalsIgnoreCase(name)) {
                return rarity;
            }
        }
        return COMMON; // Výchozí rarity
    }
    
    /**
     * Získá šanci na drop podle rarity
     */
    public double getDropChance() {
        return switch (this) {
            case COMMON -> 0.5;      // 50%
            case UNCOMMON -> 0.25;   // 25%
            case RARE -> 0.15;       // 15%
            case EPIC -> 0.08;       // 8%
            case LEGENDARY -> 0.02;  // 2%
            case MYTHIC -> 0.001;    // 0.1%
        };
    }
    
    /**
     * Získá cenu mazlíčka podle rarity
     */
    public double getBasePrice() {
        return switch (this) {
            case COMMON -> 1000;
            case UNCOMMON -> 5000;
            case RARE -> 25000;
            case EPIC -> 100000;
            case LEGENDARY -> 500000;
            case MYTHIC -> 2000000;
        };
    }
    
    /**
     * Získá následující rarity (pro upgrade)
     */
    public PetRarity getNext() {
        return switch (this) {
            case COMMON -> UNCOMMON;
            case UNCOMMON -> RARE;
            case RARE -> EPIC;
            case EPIC -> LEGENDARY;
            case LEGENDARY -> MYTHIC;
            case MYTHIC -> null; // Mythic je nejvyšší
        };
    }
    
    /**
     * Zkontroluje, jestli lze upgradovat
     */
    public boolean canUpgrade() {
        return getNext() != null;
    }
    
    /**
     * Získá cenu upgradu na další rarity
     */
    public double getUpgradeCost() {
        PetRarity next = getNext();
        if (next == null) return 0;
        
        return switch (next) {
            case UNCOMMON -> 10000;
            case RARE -> 50000;
            case EPIC -> 200000;
            case LEGENDARY -> 1000000;
            case MYTHIC -> 5000000;
            default -> 0;
        };
    }
}
