package cz.hypixel.skyblock.data;

/**
 * Enum pro typy custom předmětů podle Hypixel SkyBlock
 */
public enum ItemType {
    
    // Weapons
    SWORD("Sword", "§c", "WEAPON"),
    BOW("Bow", "§c", "WEAPON"),
    FISHING_ROD("Fishing Rod", "§c", "FISHING WEAPON"),
    
    // Tools
    PICKAXE("Pickaxe", "§e", "PICKAXE"),
    AXE("Axe", "§e", "AXE"),
    SHOVEL("Shovel", "§e", "SHOVEL"),
    HOE("Hoe", "§e", "HOE"),
    
    // Armor
    HELMET("Helmet", "§a", "HELMET"),
    CHESTPLATE("Chestplate", "§a", "CHESTPLATE"),
    LEGGINGS("Leggings", "§a", "LEGGINGS"),
    BOOTS("Boots", "§a", "BOOTS"),
    
    // Accessories
    ACCESSORY("Accessory", "§d", "ACCESSORY"),
    RING("Ring", "§d", "ACCESSORY"),
    ARTIFACT("Artifact", "§d", "ACCESSORY"),
    ORBS("Orb", "§d", "ACCESSORY"),
    
    // Materials
    CRAFTING_MATERIAL("Material", "§f", "CRAFTING MATERIAL"),
    ENCHANTED_MATERIAL("Enchanted Material", "§f", "CRAFTING MATERIAL"),
    
    // Consumables
    POTION("Potion", "§9", "CONSUMABLE"),
    FOOD("Food", "§6", "CONSUMABLE"),
    
    // Special
    PET_ITEM("Pet Item", "§d", "PET ITEM"),
    MINION_UPGRADE("Minion Upgrade", "§e", "MINION UPGRADE"),
    REFORGE_STONE("Reforge Stone", "§5", "REFORGE STONE"),
    DUNGEON_ITEM("Dungeon Item", "§c", "DUNGEON ITEM"),
    
    // Misc
    MISC("Misc", "§7", "MISC"),
    COSMETIC("Cosmetic", "§d", "COSMETIC");
    
    private final String displayName;
    private final String colorCode;
    private final String category;
    
    ItemType(String displayName, String colorCode, String category) {
        this.displayName = displayName;
        this.colorCode = colorCode;
        this.category = category;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getColorCode() {
        return colorCode;
    }
    
    public String getCategory() {
        return category;
    }
    
    public String getColoredName() {
        return colorCode + displayName;
    }
    
    public String getFormattedCategory() {
        return "§8" + category;
    }
    
    /**
     * Získá typ podle názvu
     */
    public static ItemType fromString(String name) {
        for (ItemType type : values()) {
            if (type.name().equalsIgnoreCase(name) || 
                type.displayName.equalsIgnoreCase(name) ||
                type.category.equalsIgnoreCase(name)) {
                return type;
            }
        }
        return MISC; // Výchozí typ
    }
    
    /**
     * Zkontroluje, jestli je typ zbraň
     */
    public boolean isWeapon() {
        return this == SWORD || this == BOW || this == FISHING_ROD;
    }
    
    /**
     * Zkontroluje, jestli je typ tool
     */
    public boolean isTool() {
        return this == PICKAXE || this == AXE || this == SHOVEL || this == HOE;
    }
    
    /**
     * Zkontroluje, jestli je typ armor
     */
    public boolean isArmor() {
        return this == HELMET || this == CHESTPLATE || this == LEGGINGS || this == BOOTS;
    }
    
    /**
     * Zkontroluje, jestli je typ accessory
     */
    public boolean isAccessory() {
        return this == ACCESSORY || this == RING || this == ARTIFACT || this == ORBS;
    }
    
    /**
     * Zkontroluje, jestli je typ consumable
     */
    public boolean isConsumable() {
        return this == POTION || this == FOOD;
    }
    
    /**
     * Získá všechny typy podle kategorie
     */
    public static ItemType[] getByCategory(String category) {
        return java.util.Arrays.stream(values())
            .filter(type -> type.category.equalsIgnoreCase(category))
            .toArray(ItemType[]::new);
    }
}
