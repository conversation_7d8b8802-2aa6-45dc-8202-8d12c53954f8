package cz.hypixel.skyblock.gui;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.database.PlayerData;
import cz.hypixel.skyblock.data.SkillData;
import cz.hypixel.skyblock.data.SkillType;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * GUI pro zobrazení skillů podle Hypixel SkyBlock
 */
public class SkillsGUI implements Listener {

    private final HypixelSkyBlockCZ plugin;
    private static final String GUI_TITLE = "§a§lSkills";

    public SkillsGUI(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * Otevře Skills GUI
     */
    public void openSkillsMenu(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, GUI_TITLE);

        PlayerData playerData = plugin.getPlayerDataManager().loadPlayerData(player.getUniqueId()).join();
        if (playerData == null) {
            player.sendMessage("§cChyba při načítání dat hráče!");
            return;
        }

        Map<SkillType, SkillData> skills = plugin.getSkillsManager().getAllSkills();

        // Hlavní skills (první 3 řady)
        int[] mainSkillSlots = {10, 11, 12, 13, 14, 15, 16, 19, 20, 21, 22, 23, 24, 25};
        int slotIndex = 0;

        for (Map.Entry<SkillType, SkillData> entry : skills.entrySet()) {
            SkillType skillType = entry.getKey();
            SkillData skillData = entry.getValue();
            
            if (!skillData.isCosmetic() && slotIndex < mainSkillSlots.length) {
                PlayerData.SkillData playerSkill = playerData.getSkill(skillType.name());
                ItemStack item = createSkillItem(skillData, playerSkill);
                gui.setItem(mainSkillSlots[slotIndex], item);
                slotIndex++;
            }
        }

        // Kosmetické skills (spodní řada)
        int[] cosmeticSlots = {37, 38, 39, 40, 41, 42, 43};
        slotIndex = 0;

        for (Map.Entry<SkillType, SkillData> entry : skills.entrySet()) {
            SkillType skillType = entry.getKey();
            SkillData skillData = entry.getValue();
            
            if (skillData.isCosmetic() && slotIndex < cosmeticSlots.length) {
                PlayerData.SkillData playerSkill = playerData.getSkill(skillType.name());
                ItemStack item = createSkillItem(skillData, playerSkill);
                gui.setItem(cosmeticSlots[slotIndex], item);
                slotIndex++;
            }
        }

        // Celkové statistiky
        setItem(gui, 4, createStatsItem(player, playerData));

        // Dekorativní předměty
        fillBorders(gui);

        player.openInventory(gui);
    }

    /**
     * Vytvoří item pro skill
     */
    private ItemStack createSkillItem(SkillData skillData, PlayerData.SkillData playerSkill) {
        ItemStack item = new ItemStack(skillData.getDisplayMaterial());
        ItemMeta meta = item.getItemMeta();
        
        int level = playerSkill != null ? playerSkill.getLevel() : 0;
        double xp = playerSkill != null ? playerSkill.getExperience() : 0;
        
        meta.setDisplayName(skillData.getColoredName() + " " + level);
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7" + skillData.getDescription());
        lore.add("");
        
        if (!skillData.isCosmetic()) {
            // XP informace
            double currentLevelXp = plugin.getSkillsManager().getXpForLevel(skillData.getSkillType(), level);
            double nextLevelXp = plugin.getSkillsManager().getXpForLevel(skillData.getSkillType(), level + 1);
            double xpToNext = plugin.getSkillsManager().getXpToNextLevel(skillData.getSkillType(), xp);
            
            if (level < skillData.getMaxLevel()) {
                double progress = (xp - currentLevelXp) / (nextLevelXp - currentLevelXp);
                String progressBar = createProgressBar(progress);
                
                lore.add("§7Progress: " + progressBar);
                lore.add("§7XP: §e" + formatNumber((long)(xp - currentLevelXp)) + "§7/§e" + formatNumber((long)(nextLevelXp - currentLevelXp)));
                lore.add("§7Do dalšího levelu: §e" + formatNumber((long)xpToNext) + " XP");
            } else {
                lore.add("§a§lMAX LEVEL!");
            }
            
            lore.add("");
            lore.add("§7Stat Bonus: §e" + skillData.getStatBonus());
            lore.add("§7Passive: §e" + skillData.getPassiveAbility());
        } else {
            lore.add("§7Kosmetický skill");
            lore.add("§7Level: §e" + level + "§7/§e" + skillData.getMaxLevel());
        }
        
        lore.add("");
        lore.add("§eKlikni pro více informací!");
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }

    /**
     * Vytvoří item se statistikami hráče
     */
    private ItemStack createStatsItem(Player player, PlayerData playerData) {
        ItemStack item = new ItemStack(Material.PLAYER_HEAD);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName("§6§l" + player.getName());
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7SkyBlock Level: §e" + playerData.getLevel());
        lore.add("§7SkyBlock XP: §e" + formatNumber((long)playerData.getExperience()));
        lore.add("");
        
        // Spočítej průměrný skill level
        Map<SkillType, SkillData> skills = plugin.getSkillsManager().getAllSkills();
        int totalLevels = 0;
        int skillCount = 0;
        
        for (SkillType skillType : skills.keySet()) {
            if (!skills.get(skillType).isCosmetic()) {
                PlayerData.SkillData playerSkill = playerData.getSkill(skillType.name());
                totalLevels += playerSkill != null ? playerSkill.getLevel() : 0;
                skillCount++;
            }
        }
        
        double averageLevel = skillCount > 0 ? (double) totalLevels / skillCount : 0;
        lore.add("§7Průměrný Skill Level: §e" + String.format("%.1f", averageLevel));
        lore.add("§7Celkový Skill Level: §e" + totalLevels);
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }

    /**
     * Vyplní okraje GUI dekorativními předměty
     */
    private void fillBorders(Inventory gui) {
        ItemStack border = new ItemStack(Material.LIME_STAINED_GLASS_PANE);
        ItemMeta meta = border.getItemMeta();
        meta.setDisplayName(" ");
        border.setItemMeta(meta);

        // Horní a dolní řada
        for (int i = 0; i < 9; i++) {
            if (i != 4) { // Nechej místo pro stats
                gui.setItem(i, border);
            }
            gui.setItem(i + 45, border);
        }

        // Levý a pravý sloupec
        for (int i = 9; i < 45; i += 9) {
            gui.setItem(i, border);
            gui.setItem(i + 8, border);
        }

        // Oddělení mezi hlavními a kosmetickými skills
        ItemStack separator = new ItemStack(Material.YELLOW_STAINED_GLASS_PANE);
        ItemMeta sepMeta = separator.getItemMeta();
        sepMeta.setDisplayName("§e§lKosmetické Skills");
        sepMeta.setLore(Arrays.asList("", "§7Tyto skills neposkytují", "§7žádné gameplay výhody"));
        separator.setItemMeta(sepMeta);
        
        for (int i = 28; i <= 34; i++) {
            gui.setItem(i, separator);
        }
    }

    /**
     * Nastaví item na konkrétní slot
     */
    private void setItem(Inventory gui, int slot, ItemStack item) {
        gui.setItem(slot, item);
    }

    /**
     * Vytvoří progress bar
     */
    private String createProgressBar(double progress) {
        int bars = 20;
        int filled = (int) (progress * bars);
        
        StringBuilder bar = new StringBuilder("§7[");
        for (int i = 0; i < bars; i++) {
            if (i < filled) {
                bar.append("§a■");
            } else {
                bar.append("§7■");
            }
        }
        bar.append("§7] §e").append(String.format("%.1f", progress * 100)).append("%");
        
        return bar.toString();
    }

    /**
     * Formátuje číslo pro zobrazení
     */
    private String formatNumber(long number) {
        if (number >= 1_000_000_000) {
            return String.format("%.1fB", number / 1_000_000_000.0);
        } else if (number >= 1_000_000) {
            return String.format("%.1fM", number / 1_000_000.0);
        } else if (number >= 1_000) {
            return String.format("%.1fk", number / 1_000.0);
        } else {
            return String.valueOf(number);
        }
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        
        if (!event.getView().getTitle().equals(GUI_TITLE)) return;
        
        event.setCancelled(true);
        
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || clicked.getType() == Material.AIR) return;
        
        ItemMeta meta = clicked.getItemMeta();
        if (meta == null || meta.getDisplayName() == null) return;
        
        // TODO: Implementovat detailní view pro konkrétní skill
        String displayName = meta.getDisplayName();
        if (displayName.contains("Combat") || displayName.contains("Mining") || 
            displayName.contains("Farming") || displayName.contains("Foraging") ||
            displayName.contains("Fishing") || displayName.contains("Enchanting") ||
            displayName.contains("Alchemy") || displayName.contains("Taming") ||
            displayName.contains("Dungeoneering") || displayName.contains("Carpentry") ||
            displayName.contains("Social") || displayName.contains("Runecrafting")) {
            
            player.sendMessage("§eDetailní view pro skills bude implementován později!");
        }
    }
}
