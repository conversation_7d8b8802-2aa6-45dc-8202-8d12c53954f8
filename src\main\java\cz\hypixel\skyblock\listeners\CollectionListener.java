package cz.hypixel.skyblock.listeners;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.SkillType;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.entity.EntityDeathEvent;
import org.bukkit.event.player.PlayerFishEvent;
import org.bukkit.event.player.PlayerHarvestBlockEvent;
import org.bukkit.inventory.ItemStack;

import java.util.HashMap;
import java.util.Map;

/**
 * Listener pro automatické přidávání do kolekcí a skillů
 */
public class CollectionListener implements Listener {

    private final HypixelSkyBlockCZ plugin;
    
    // Mapování materiálů na kolekce
    private final Map<Material, String> materialToCollection;
    
    // XP hodnoty pro různé aktivity
    private final Map<Material, Double> farmingXp;
    private final Map<Material, Double> miningXp;
    private final Map<Material, Double> foragingXp;

    public CollectionListener(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.materialToCollection = new HashMap<>();
        this.farmingXp = new HashMap<>();
        this.miningXp = new HashMap<>();
        this.foragingXp = new HashMap<>();
        
        initializeMappings();
    }

    /**
     * Inicializuje mapování materiálů na kolekce a XP hodnoty
     */
    private void initializeMappings() {
        // Farming kolekce
        materialToCollection.put(Material.CACTUS, "CACTUS");
        materialToCollection.put(Material.CARROT, "CARROT");
        materialToCollection.put(Material.WHEAT, "WHEAT");
        materialToCollection.put(Material.POTATO, "POTATO");
        
        // Mining kolekce
        materialToCollection.put(Material.COBBLESTONE, "COBBLESTONE");
        materialToCollection.put(Material.COAL, "COAL");
        
        // Combat kolekce
        materialToCollection.put(Material.ROTTEN_FLESH, "ROTTEN_FLESH");
        materialToCollection.put(Material.BONE, "BONE");
        
        // Foraging kolekce
        materialToCollection.put(Material.OAK_LOG, "OAK_WOOD");
        materialToCollection.put(Material.BIRCH_LOG, "OAK_WOOD");
        materialToCollection.put(Material.SPRUCE_LOG, "OAK_WOOD");
        materialToCollection.put(Material.JUNGLE_LOG, "OAK_WOOD");
        materialToCollection.put(Material.ACACIA_LOG, "OAK_WOOD");
        materialToCollection.put(Material.DARK_OAK_LOG, "OAK_WOOD");
        
        // Fishing kolekce
        materialToCollection.put(Material.COD, "RAW_FISH");
        materialToCollection.put(Material.SALMON, "RAW_FISH");
        materialToCollection.put(Material.TROPICAL_FISH, "RAW_FISH");
        materialToCollection.put(Material.PUFFERFISH, "RAW_FISH");

        // Farming XP hodnoty
        farmingXp.put(Material.WHEAT, 4.0);
        farmingXp.put(Material.CARROT, 4.0);
        farmingXp.put(Material.POTATO, 4.0);
        farmingXp.put(Material.BEETROOT, 4.0);
        farmingXp.put(Material.PUMPKIN, 4.5);
        farmingXp.put(Material.MELON, 4.5);
        farmingXp.put(Material.SUGAR_CANE, 2.0);
        farmingXp.put(Material.CACTUS, 2.0);
        farmingXp.put(Material.COCOA_BEANS, 3.0);
        farmingXp.put(Material.NETHER_WART, 3.0);

        // Mining XP hodnoty
        miningXp.put(Material.COBBLESTONE, 1.0);
        miningXp.put(Material.COAL_ORE, 5.0);
        miningXp.put(Material.IRON_ORE, 12.0);
        miningXp.put(Material.GOLD_ORE, 25.0);
        miningXp.put(Material.DIAMOND_ORE, 50.0);
        miningXp.put(Material.EMERALD_ORE, 100.0);
        miningXp.put(Material.LAPIS_ORE, 20.0);
        miningXp.put(Material.REDSTONE_ORE, 15.0);
        miningXp.put(Material.NETHER_QUARTZ_ORE, 20.0);

        // Foraging XP hodnoty
        foragingXp.put(Material.OAK_LOG, 6.0);
        foragingXp.put(Material.BIRCH_LOG, 6.0);
        foragingXp.put(Material.SPRUCE_LOG, 6.0);
        foragingXp.put(Material.JUNGLE_LOG, 7.0);
        foragingXp.put(Material.ACACIA_LOG, 7.0);
        foragingXp.put(Material.DARK_OAK_LOG, 7.0);
    }

    @EventHandler
    public void onBlockBreak(BlockBreakEvent event) {
        if (event.isCancelled()) return;
        
        Player player = event.getPlayer();
        Material material = event.getBlock().getType();
        
        // Zkontroluj, jestli je to v kolekci
        String collectionName = materialToCollection.get(material);
        if (collectionName != null) {
            plugin.getCollectionsManager().addToCollection(player, collectionName, 1);
        }
        
        // Přidej Mining XP
        Double xp = miningXp.get(material);
        if (xp != null) {
            plugin.getSkillsManager().addSkillXp(player, SkillType.MINING, xp);
        }
        
        // Přidej Foraging XP
        xp = foragingXp.get(material);
        if (xp != null) {
            plugin.getSkillsManager().addSkillXp(player, SkillType.FORAGING, xp);
        }
    }

    @EventHandler
    public void onPlayerHarvest(PlayerHarvestBlockEvent event) {
        Player player = event.getPlayer();
        Material material = event.getHarvestedBlock().getType();
        
        // Spočítej množství sklizených předmětů
        int amount = event.getItemsHarvested().size();
        
        // Zkontroluj, jestli je to v kolekci
        String collectionName = materialToCollection.get(material);
        if (collectionName != null) {
            plugin.getCollectionsManager().addToCollection(player, collectionName, amount);
        }
        
        // Přidej Farming XP
        Double xp = farmingXp.get(material);
        if (xp != null) {
            plugin.getSkillsManager().addSkillXp(player, SkillType.FARMING, xp * amount);
        }
    }

    @EventHandler
    public void onEntityDeath(EntityDeathEvent event) {
        if (event.getEntity().getKiller() == null) return;
        
        Player player = event.getEntity().getKiller();
        
        // Přidej Combat XP na základě typu entity
        double combatXp = getCombatXp(event.getEntity().getType().name());
        if (combatXp > 0) {
            plugin.getSkillsManager().addSkillXp(player, SkillType.COMBAT, combatXp);
        }
        
        // Zpracuj dropy pro kolekce
        for (ItemStack drop : event.getDrops()) {
            String collectionName = materialToCollection.get(drop.getType());
            if (collectionName != null) {
                plugin.getCollectionsManager().addToCollection(player, collectionName, drop.getAmount());
            }
        }
    }

    @EventHandler
    public void onPlayerFish(PlayerFishEvent event) {
        if (event.getState() != PlayerFishEvent.State.CAUGHT_FISH) return;
        if (event.getCaught() == null) return;
        
        Player player = event.getPlayer();
        
        // Přidej Fishing XP
        plugin.getSkillsManager().addSkillXp(player, SkillType.FISHING, 10.0);
        
        // Zkontroluj, jestli je ulovená ryba v kolekci
        if (event.getCaught() instanceof org.bukkit.entity.Item) {
            org.bukkit.entity.Item item = (org.bukkit.entity.Item) event.getCaught();
            Material material = item.getItemStack().getType();
            
            String collectionName = materialToCollection.get(material);
            if (collectionName != null) {
                plugin.getCollectionsManager().addToCollection(player, collectionName, item.getItemStack().getAmount());
            }
        }
    }

    /**
     * Získá Combat XP na základě typu entity
     */
    private double getCombatXp(String entityType) {
        switch (entityType.toUpperCase()) {
            case "ZOMBIE":
                return 5.0;
            case "SKELETON":
                return 5.0;
            case "SPIDER":
                return 5.0;
            case "CREEPER":
                return 8.0;
            case "ENDERMAN":
                return 10.0;
            case "WITCH":
                return 10.0;
            case "BLAZE":
                return 15.0;
            case "WITHER_SKELETON":
                return 20.0;
            case "ENDER_DRAGON":
                return 500.0;
            case "WITHER":
                return 300.0;
            default:
                return 2.0; // Základní XP pro ostatní entity
        }
    }
}
