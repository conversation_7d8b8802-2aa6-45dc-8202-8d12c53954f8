package cz.hypixel.skyblock.gui;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.Minion;
import cz.hypixel.skyblock.managers.BankManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * GUI pro interakci s minionem podle Hypixel SkyBlock
 */
public class MinionGUI implements Listener {

    private final HypixelSkyBlockCZ plugin;
    private static final String GUI_TITLE = "§6§lMinion - ";

    public MinionGUI(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * Otevře GUI miniona
     */
    public void openMinionGUI(Player player, Minion minion) {
        if (!minion.getOwnerUuid().equals(player.getUniqueId())) {
            player.sendMessage("§cToto není tvůj minion!");
            return;
        }

        String title = GUI_TITLE + minion.getMinionType().getDisplayName();
        Inventory gui = Bukkit.createInventory(null, 54, title);

        // Informace o minionovi
        setItem(gui, 4, createMinionInfoDisplay(minion));

        // Storage (sloty 19-43)
        displayMinionStorage(gui, minion);

        // Akční tlačítka
        setItem(gui, 45, createUpgradeButton(minion));
        setItem(gui, 46, createFuelButton(minion));
        setItem(gui, 47, createUpgradesButton(minion));
        setItem(gui, 48, createCollectButton(minion));
        setItem(gui, 49, createToggleButton(minion));
        setItem(gui, 50, createPickupButton(minion));

        setItem(gui, 53, createCloseButton());

        // Dekorativní předměty
        fillBorders(gui);

        player.openInventory(gui);
    }

    /**
     * Zobrazí storage miniona
     */
    private void displayMinionStorage(Inventory gui, Minion minion) {
        List<ItemStack> storage = minion.getStorage();
        
        // Storage sloty (19-43, celkem 25 slotů)
        for (int i = 0; i < 25; i++) {
            int slot = 19 + i;
            if (slot == 26) slot = 28; // Přeskoč okraje
            if (slot == 35) slot = 37;
            if (slot >= 44) break;
            
            if (i < storage.size() && storage.get(i) != null) {
                gui.setItem(slot, storage.get(i));
            } else {
                gui.setItem(slot, createEmptyStorageSlot());
            }
        }
    }

    /**
     * Vytvoří informační display miniona
     */
    private ItemStack createMinionInfoDisplay(Minion minion) {
        ItemStack item = new ItemStack(minion.getMinionType().getDisplayMaterial());
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName(minion.getDisplayName());
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Typ: " + minion.getMinionType().getColoredName());
        lore.add("§7Tier: §e" + minion.getTier() + "§7/§e" + minion.getMinionType().getMaxTier());
        lore.add("§7Status: " + (minion.isActive() ? "§aAktivní" : "§cNeaktivní"));
        lore.add("");
        lore.add("§7Rychlost akce: §e" + String.format("%.1f", minion.getActionSpeed()) + "s");
        lore.add("§7Uptime: §e" + minion.getUptimeFormatted());
        lore.add("");
        lore.add("§7Storage: §e" + minion.getStorage().size() + "§7/§e" + minion.getMaxStorageSlots());
        
        if (minion.getFuelRemaining() > 0) {
            lore.add("§7Fuel: §e" + String.format("%.1f", minion.getFuelRemaining()) + " hodin");
        } else {
            lore.add("§7Fuel: §cŽádný");
        }
        
        lore.add("");
        lore.add("§7Upgrady: §e" + minion.getUpgrades().size());
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko pro upgrade
     */
    private ItemStack createUpgradeButton(Minion minion) {
        ItemStack item = new ItemStack(Material.ANVIL);
        ItemMeta meta = item.getItemMeta();
        
        if (minion.canUpgrade()) {
            meta.setDisplayName("§aUpgrade na Tier " + (minion.getTier() + 1));
            
            List<String> lore = Arrays.asList(
                "",
                "§7Současný tier: §e" + minion.getTier(),
                "§7Nový tier: §e" + (minion.getTier() + 1),
                "",
                "§7Cena: §6" + BankManager.formatCoins(minion.getUpgradeCost()),
                "",
                "§eKlikni pro upgrade!"
            );
            meta.setLore(lore);
        } else {
            meta.setDisplayName("§cMax Tier");
            meta.setLore(Arrays.asList("", "§7Tento minion je už na", "§7maximálním tieru!"));
        }
        
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko pro fuel
     */
    private ItemStack createFuelButton(Minion minion) {
        ItemStack item = new ItemStack(Material.COAL);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§6Přidat Fuel");
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Současný fuel: §e" + String.format("%.1f", minion.getFuelRemaining()) + " hodin");
        lore.add("");
        lore.add("§7Fuel typy:");
        lore.add("§7- Coal: §e30 minut");
        lore.add("§7- Coal Block: §e4.5 hodin");
        lore.add("§7- Lava Bucket: §e12 hodin");
        lore.add("§7- Blaze Rod: §e24 hodin");
        lore.add("");
        lore.add("§eKlikni pro přidání fuel!");
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko pro upgrady
     */
    private ItemStack createUpgradesButton(Minion minion) {
        ItemStack item = new ItemStack(Material.REDSTONE);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§cUpgrady");
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Nainstalované upgrady:");
        
        if (minion.getUpgrades().isEmpty()) {
            lore.add("§7Žádné upgrady");
        } else {
            for (String upgrade : minion.getUpgrades().keySet()) {
                lore.add("§7- §e" + upgrade);
            }
        }
        
        lore.add("");
        lore.add("§7Dostupné upgrady:");
        lore.add("§7- Auto Smelter");
        lore.add("§7- Compactor");
        lore.add("§7- Super Compactor");
        lore.add("§7- Storage Upgrade");
        lore.add("");
        lore.add("§eKlikni pro správu upgradů!");
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko pro collect
     */
    private ItemStack createCollectButton(Minion minion) {
        ItemStack item = new ItemStack(Material.HOPPER);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§aVybrat vše");
        
        List<String> lore = Arrays.asList(
            "",
            "§7Vybere všechny předměty",
            "§7ze storage miniona",
            "",
            "§7Předmětů ve storage: §e" + minion.getStorage().size(),
            "",
            "§eKlikni pro vybrání!"
        );
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko pro toggle
     */
    private ItemStack createToggleButton(Minion minion) {
        Material material = minion.isActive() ? Material.REDSTONE_TORCH : Material.TORCH;
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        if (minion.isActive()) {
            meta.setDisplayName("§cDeaktivovat");
            meta.setLore(Arrays.asList("", "§7Pozastaví práci miniona", "", "§eKlikni pro deaktivaci!"));
        } else {
            meta.setDisplayName("§aAktivovat");
            meta.setLore(Arrays.asList("", "§7Obnoví práci miniona", "", "§eKlikni pro aktivaci!"));
        }
        
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko pro pickup
     */
    private ItemStack createPickupButton(Minion minion) {
        ItemStack item = new ItemStack(Material.BARRIER);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§cOdebrat miniona");
        
        List<String> lore = Arrays.asList(
            "",
            "§7Odebere miniona a vrátí",
            "§7ho do inventáře",
            "",
            "§c§lVAROVÁNÍ:",
            "§7Všechny upgrady budou ztraceny!",
            "",
            "§eKlikni pro odebrání!"
        );
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří prázdný storage slot
     */
    private ItemStack createEmptyStorageSlot() {
        ItemStack item = new ItemStack(Material.LIGHT_GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§7Prázdný slot");
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Close"
     */
    private ItemStack createCloseButton() {
        ItemStack item = new ItemStack(Material.BARRIER);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§cZavřít");
        meta.setLore(Arrays.asList("", "§7Klikni pro zavření"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vyplní okraje GUI
     */
    private void fillBorders(Inventory gui) {
        ItemStack border = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = border.getItemMeta();
        meta.setDisplayName(" ");
        border.setItemMeta(meta);

        // Horní a dolní řada
        for (int i = 0; i < 9; i++) {
            if (gui.getItem(i) == null) gui.setItem(i, border);
            if (gui.getItem(i + 45) == null) gui.setItem(i + 45, border);
        }

        // Levý a pravý sloupec (kromě storage oblasti)
        int[] borderSlots = {9, 17, 18, 26, 27, 35, 36, 44};
        for (int slot : borderSlots) {
            if (gui.getItem(slot) == null) gui.setItem(slot, border);
        }
    }

    /**
     * Nastaví item na slot
     */
    private void setItem(Inventory gui, int slot, ItemStack item) {
        gui.setItem(slot, item);
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        
        String title = event.getView().getTitle();
        if (!title.startsWith("§6§lMinion - ")) return;
        
        event.setCancelled(true);
        
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || clicked.getType() == Material.AIR) return;
        
        ItemMeta meta = clicked.getItemMeta();
        if (meta == null || meta.getDisplayName() == null) return;
        
        String displayName = meta.getDisplayName();
        
        // TODO: Implementovat handling kliků
        handleMinionGUIClick(player, displayName, title);
    }

    /**
     * Zpracuje klik v minion GUI
     */
    private void handleMinionGUIClick(Player player, String displayName, String title) {
        if (displayName.equals("§cZavřít")) {
            player.closeInventory();
        } else if (displayName.startsWith("§aUpgrade na Tier")) {
            player.sendMessage("§eUpgrade miniona bude implementován později!");
        } else if (displayName.equals("§6Přidat Fuel")) {
            player.sendMessage("§ePřidávání fuel bude implementováno později!");
        } else if (displayName.equals("§cUpgrady")) {
            player.sendMessage("§eSpráva upgradů bude implementována později!");
        } else if (displayName.equals("§aVybrat vše")) {
            player.sendMessage("§eVybrání předmětů bude implementováno později!");
        } else if (displayName.equals("§aAktivovat") || displayName.equals("§cDeaktivovat")) {
            player.sendMessage("§eToggle miniona bude implementován později!");
        } else if (displayName.equals("§cOdebrat miniona")) {
            player.sendMessage("§eOdebrání miniona bude implementováno později!");
        }
    }
}
