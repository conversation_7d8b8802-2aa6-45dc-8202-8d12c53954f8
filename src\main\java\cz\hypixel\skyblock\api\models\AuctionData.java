package cz.hypixel.skyblock.api.models;

import com.google.gson.annotations.SerializedName;
import java.util.List;

/**
 * Model pro auction data z Hypixel API
 */
public class AuctionData {
    
    private boolean success;
    private int page;
    
    @SerializedName("totalPages")
    private int totalPages;
    
    @SerializedName("totalAuctions")
    private int totalAuctions;
    
    @SerializedName("lastUpdated")
    private long lastUpdated;
    
    private List<Auction> auctions;
    
    // Gettery
    public boolean isSuccess() {
        return success;
    }
    
    public int getPage() {
        return page;
    }
    
    public int getTotalPages() {
        return totalPages;
    }
    
    public int getTotalAuctions() {
        return totalAuctions;
    }
    
    public long getLastUpdated() {
        return lastUpdated;
    }
    
    public List<Auction> getAuctions() {
        return auctions;
    }
    
    /**
     * Model pro jednotlivou aukci
     */
    public static class Auction {
        
        private String uuid;
        private String auctioneer;
        
        @SerializedName("profile_id")
        private String profileId;
        
        private List<String> coop;
        
        private long start;
        private long end;
        
        @SerializedName("item_name")
        private String itemName;
        
        @SerializedName("item_lore")
        private String itemLore;
        
        private String extra;
        private String category;
        private String tier;
        
        @SerializedName("starting_bid")
        private long startingBid;
        
        @SerializedName("item_bytes")
        private String itemBytes;
        
        private boolean claimed;
        
        @SerializedName("claimed_bidders")
        private List<String> claimedBidders;
        
        @SerializedName("highest_bid_amount")
        private long highestBidAmount;
        
        private List<Bid> bids;
        
        // Gettery
        public String getUuid() {
            return uuid;
        }
        
        public String getAuctioneer() {
            return auctioneer;
        }
        
        public String getProfileId() {
            return profileId;
        }
        
        public List<String> getCoop() {
            return coop;
        }
        
        public long getStart() {
            return start;
        }
        
        public long getEnd() {
            return end;
        }
        
        public String getItemName() {
            return itemName;
        }
        
        public String getItemLore() {
            return itemLore;
        }
        
        public String getExtra() {
            return extra;
        }
        
        public String getCategory() {
            return category;
        }
        
        public String getTier() {
            return tier;
        }
        
        public long getStartingBid() {
            return startingBid;
        }
        
        public String getItemBytes() {
            return itemBytes;
        }
        
        public boolean isClaimed() {
            return claimed;
        }
        
        public List<String> getClaimedBidders() {
            return claimedBidders;
        }
        
        public long getHighestBidAmount() {
            return highestBidAmount;
        }
        
        public List<Bid> getBids() {
            return bids;
        }
        
        /**
         * Zkontroluje, jestli je aukce aktivní
         */
        public boolean isActive() {
            long currentTime = System.currentTimeMillis();
            return currentTime >= start && currentTime <= end && !claimed;
        }
        
        /**
         * Získá aktuální cenu (nejvyšší bid nebo starting bid)
         */
        public long getCurrentPrice() {
            return Math.max(highestBidAmount, startingBid);
        }
        
        /**
         * Získá zbývající čas v milisekundách
         */
        public long getTimeRemaining() {
            return Math.max(0, end - System.currentTimeMillis());
        }
    }
    
    /**
     * Model pro bid
     */
    public static class Bid {
        
        @SerializedName("auction_id")
        private String auctionId;
        
        private String bidder;
        
        @SerializedName("profile_id")
        private String profileId;
        
        private long amount;
        private long timestamp;
        
        // Gettery
        public String getAuctionId() {
            return auctionId;
        }
        
        public String getBidder() {
            return bidder;
        }
        
        public String getProfileId() {
            return profileId;
        }
        
        public long getAmount() {
            return amount;
        }
        
        public long getTimestamp() {
            return timestamp;
        }
    }
}
