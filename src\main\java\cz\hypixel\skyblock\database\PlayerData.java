package cz.hypixel.skyblock.database;

import java.util.HashMap;
import java.util.Map;

/**
 * Model pro data hr<PERSON><PERSON><PERSON> v databázi
 */
public class PlayerData {
    
    private String uuid;
    private String username;
    private String hypixelUuid;
    private double coins;
    private int level;
    private double experience;
    private boolean islandCreated;
    private long lastSeen;
    private long firstJoin;
    private long createdAt;
    private long updatedAt;
    
    // Skills a collections
    private Map<String, SkillData> skills;
    private Map<String, Long> collections;
    
    public PlayerData() {
        this.skills = new HashMap<>();
        this.collections = new HashMap<>();
    }
    
    public PlayerData(String uuid, String username) {
        this();
        this.uuid = uuid;
        this.username = username;
        this.coins = 100.0; // Počáteční mince
        this.level = 1;
        this.experience = 0.0;
        this.islandCreated = false;
        this.lastSeen = System.currentTimeMillis();
        this.firstJoin = System.currentTimeMillis();
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }
    
    // Gettery a settery
    public String getUuid() {
        return uuid;
    }
    
    public void setUuid(String uuid) {
        this.uuid = uuid;
    }
    
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
    
    public String getHypixelUuid() {
        return hypixelUuid;
    }
    
    public void setHypixelUuid(String hypixelUuid) {
        this.hypixelUuid = hypixelUuid;
    }
    
    public double getCoins() {
        return coins;
    }
    
    public void setCoins(double coins) {
        this.coins = coins;
    }
    
    public int getLevel() {
        return level;
    }
    
    public void setLevel(int level) {
        this.level = level;
    }
    
    public double getExperience() {
        return experience;
    }
    
    public void setExperience(double experience) {
        this.experience = experience;
    }
    
    public boolean isIslandCreated() {
        return islandCreated;
    }
    
    public void setIslandCreated(boolean islandCreated) {
        this.islandCreated = islandCreated;
    }
    
    public long getLastSeen() {
        return lastSeen;
    }
    
    public void setLastSeen(long lastSeen) {
        this.lastSeen = lastSeen;
    }
    
    public long getFirstJoin() {
        return firstJoin;
    }
    
    public void setFirstJoin(long firstJoin) {
        this.firstJoin = firstJoin;
    }
    
    public long getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }
    
    public long getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public Map<String, SkillData> getSkills() {
        return skills;
    }
    
    public void setSkills(Map<String, SkillData> skills) {
        this.skills = skills;
    }
    
    public Map<String, Long> getCollections() {
        return collections;
    }
    
    public void setCollections(Map<String, Long> collections) {
        this.collections = collections;
    }
    
    // Utility metody
    public void addCoins(double amount) {
        this.coins += amount;
        updateTimestamp();
    }
    
    public boolean removeCoins(double amount) {
        if (this.coins >= amount) {
            this.coins -= amount;
            updateTimestamp();
            return true;
        }
        return false;
    }
    
    public void addExperience(double exp) {
        this.experience += exp;
        updateTimestamp();
        
        // Zkontroluj level up
        checkLevelUp();
    }
    
    public SkillData getSkill(String skillName) {
        return skills.get(skillName.toUpperCase());
    }
    
    public void setSkill(String skillName, int level, double experience) {
        skills.put(skillName.toUpperCase(), new SkillData(skillName, level, experience));
        updateTimestamp();
    }
    
    public long getCollectionAmount(String itemName) {
        return collections.getOrDefault(itemName.toUpperCase(), 0L);
    }
    
    public void addCollection(String itemName, long amount) {
        String key = itemName.toUpperCase();
        collections.put(key, collections.getOrDefault(key, 0L) + amount);
        updateTimestamp();
    }
    
    private void updateTimestamp() {
        this.updatedAt = System.currentTimeMillis();
    }
    
    private void checkLevelUp() {
        int newLevel = calculateLevel(this.experience);
        if (newLevel > this.level) {
            this.level = newLevel;
        }
    }
    
    private int calculateLevel(double experience) {
        // Zjednodušený výpočet levelu
        return (int) Math.floor(Math.sqrt(experience / 100)) + 1;
    }
    
    /**
     * Model pro skill data
     */
    public static class SkillData {
        private String name;
        private int level;
        private double experience;
        
        public SkillData(String name, int level, double experience) {
            this.name = name;
            this.level = level;
            this.experience = experience;
        }
        
        // Gettery a settery
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public int getLevel() {
            return level;
        }
        
        public void setLevel(int level) {
            this.level = level;
        }
        
        public double getExperience() {
            return experience;
        }
        
        public void setExperience(double experience) {
            this.experience = experience;
        }
        
        public void addExperience(double exp) {
            this.experience += exp;
            // Přepočítej level
            this.level = calculateSkillLevel(this.experience);
        }
        
        private int calculateSkillLevel(double experience) {
            // Zjednodušený výpočet skill levelu
            if (experience < 50) return 0;
            if (experience < 175) return 1;
            if (experience < 375) return 2;
            if (experience < 675) return 3;
            if (experience < 1175) return 4;
            if (experience < 1925) return 5;
            if (experience < 2925) return 6;
            if (experience < 4425) return 7;
            if (experience < 6425) return 8;
            if (experience < 9925) return 9;
            if (experience < 14925) return 10;
            // ... další levely
            return Math.min(50, (int) (experience / 1000)); // Max level 50
        }
    }
}
