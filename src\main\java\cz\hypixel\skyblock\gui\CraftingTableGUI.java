package cz.hypixel.skyblock.gui;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.CustomRecipe;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.event.inventory.InventoryCloseEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * GUI pro crafting table podle Hypixel SkyBlock
 */
public class CraftingTableGUI implements Listener {

    private final HypixelSkyBlockCZ plugin;
    private static final String GUI_TITLE = "§6§lCrafting Table";
    
    // Sloty pro crafting grid (3x3)
    private static final int[] CRAFTING_SLOTS = {10, 11, 12, 19, 20, 21, 28, 29, 30};
    private static final int RESULT_SLOT = 24;
    
    // Mapa pro sledování crafting gridů hráčů
    private final Map<UUID, ItemStack[]> playerCraftingGrids;

    public CraftingTableGUI(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.playerCraftingGrids = new HashMap<>();
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * Otevře crafting table GUI
     */
    public void openCraftingTable(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, GUI_TITLE);

        // Nastav crafting grid
        setupCraftingGrid(gui);
        
        // Nastav result slot
        setItem(gui, RESULT_SLOT, createResultSlotPlaceholder());
        
        // Informační panel
        setItem(gui, 4, createInfoPanel());
        
        // Tlačítka
        setItem(gui, 45, createRecipeBookButton());
        setItem(gui, 46, createClearButton());
        setItem(gui, 47, createHelpButton());
        
        setItem(gui, 53, createCloseButton());

        // Dekorativní předměty
        fillBorders(gui);

        // Inicializuj crafting grid pro hráče
        playerCraftingGrids.put(player.getUniqueId(), new ItemStack[9]);

        player.openInventory(gui);
    }

    /**
     * Nastaví crafting grid
     */
    private void setupCraftingGrid(Inventory gui) {
        for (int slot : CRAFTING_SLOTS) {
            gui.setItem(slot, createCraftingSlotPlaceholder());
        }
    }

    /**
     * Vytvoří placeholder pro crafting slot
     */
    private ItemStack createCraftingSlotPlaceholder() {
        ItemStack item = new ItemStack(Material.LIGHT_GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§7Crafting Slot");
        meta.setLore(Arrays.asList("", "§7Vlož materiály sem"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří placeholder pro result slot
     */
    private ItemStack createResultSlotPlaceholder() {
        ItemStack item = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§7Result");
        meta.setLore(Arrays.asList("", "§7Výsledek crafting se", "§7zobrazí zde"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří informační panel
     */
    private ItemStack createInfoPanel() {
        ItemStack item = new ItemStack(Material.CRAFTING_TABLE);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§6§lCrafting Table");
        
        meta.setLore(Arrays.asList(
            "",
            "§7Vytvárej custom předměty pomocí",
            "§7speciálních SkyBlock receptů!",
            "",
            "§7Vlož materiály do 3x3 gridu",
            "§7a výsledek se zobrazí vpravo.",
            "",
            "§eKlikni na Recipe Book pro recepty!"
        ));
        
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Recipe Book"
     */
    private ItemStack createRecipeBookButton() {
        ItemStack item = new ItemStack(Material.KNOWLEDGE_BOOK);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§aRecipe Book");
        meta.setLore(Arrays.asList("", "§7Zobraz dostupné recepty", "", "§eKlikni pro otevření!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Clear"
     */
    private ItemStack createClearButton() {
        ItemStack item = new ItemStack(Material.RED_DYE);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§cVyčistit");
        meta.setLore(Arrays.asList("", "§7Vyčistí crafting grid", "", "§eKlikni pro vyčištění!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Help"
     */
    private ItemStack createHelpButton() {
        ItemStack item = new ItemStack(Material.BOOK);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§eNápověda");
        
        meta.setLore(Arrays.asList(
            "",
            "§7Jak craftovat:",
            "§71. Vlož materiály do 3x3 gridu",
            "§72. Výsledek se zobrazí vpravo",
            "§73. Klikni na výsledek pro craft",
            "",
            "§7Některé recepty vyžadují:",
            "§7- Určitý level v Collections",
            "§7- Speciální materiály",
            "§7- Unlock requirements"
        ));
        
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Close"
     */
    private ItemStack createCloseButton() {
        ItemStack item = new ItemStack(Material.BARRIER);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§cZavřít");
        meta.setLore(Arrays.asList("", "§7Klikni pro zavření"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Aktualizuje result slot podle crafting gridu
     */
    private void updateResultSlot(Player player, Inventory gui) {
        UUID playerUuid = player.getUniqueId();
        ItemStack[] craftingGrid = playerCraftingGrids.get(playerUuid);
        
        if (craftingGrid == null) return;
        
        // Najdi matching recept
        CustomRecipe recipe = plugin.getCustomItemManager().findMatchingRecipe(craftingGrid);
        
        if (recipe != null && recipe.isUnlockedFor(playerUuid)) {
            // Zobraz výsledek
            ItemStack result = recipe.createResult();
            gui.setItem(RESULT_SLOT, result);
        } else {
            // Žádný matching recept
            gui.setItem(RESULT_SLOT, createResultSlotPlaceholder());
        }
    }

    /**
     * Zpracuje craft akci
     */
    private void processCraft(Player player, Inventory gui) {
        UUID playerUuid = player.getUniqueId();
        ItemStack[] craftingGrid = playerCraftingGrids.get(playerUuid);
        
        if (craftingGrid == null) return;
        
        CustomRecipe recipe = plugin.getCustomItemManager().findMatchingRecipe(craftingGrid);
        
        if (recipe != null && recipe.isUnlockedFor(playerUuid)) {
            // Vytvoř výsledek
            ItemStack result = recipe.createResult();
            
            // Dej hráči výsledek
            player.getInventory().addItem(result);
            
            // Spotřebuj ingredience
            recipe.consumeIngredients(craftingGrid);
            
            // Aktualizuj GUI
            updateCraftingGridDisplay(gui, craftingGrid);
            updateResultSlot(player, gui);
            
            // Přidej crafting XP
            plugin.getSkillsManager().addExperience(playerUuid, "CRAFTING", 10.0);
            
            player.sendMessage("§aÚspěšně jsi vytvořil " + result.getItemMeta().getDisplayName() + "!");
        }
    }

    /**
     * Aktualizuje zobrazení crafting gridu
     */
    private void updateCraftingGridDisplay(Inventory gui, ItemStack[] craftingGrid) {
        for (int i = 0; i < 9; i++) {
            int slot = CRAFTING_SLOTS[i];
            ItemStack item = craftingGrid[i];
            
            if (item == null || item.getType() == Material.AIR) {
                gui.setItem(slot, createCraftingSlotPlaceholder());
            } else {
                gui.setItem(slot, item);
            }
        }
    }

    /**
     * Vyčistí crafting grid
     */
    private void clearCraftingGrid(Player player, Inventory gui) {
        UUID playerUuid = player.getUniqueId();
        ItemStack[] craftingGrid = playerCraftingGrids.get(playerUuid);
        
        if (craftingGrid == null) return;
        
        // Vrať předměty hráči
        for (ItemStack item : craftingGrid) {
            if (item != null && item.getType() != Material.AIR) {
                player.getInventory().addItem(item);
            }
        }
        
        // Vyčisti grid
        Arrays.fill(craftingGrid, null);
        
        // Aktualizuj GUI
        updateCraftingGridDisplay(gui, craftingGrid);
        updateResultSlot(player, gui);
        
        player.sendMessage("§aCrafting grid vyčištěn!");
    }

    /**
     * Vyplní okraje GUI
     */
    private void fillBorders(Inventory gui) {
        ItemStack border = new ItemStack(Material.BROWN_STAINED_GLASS_PANE);
        ItemMeta meta = border.getItemMeta();
        meta.setDisplayName(" ");
        border.setItemMeta(meta);

        // Horní a dolní řada
        for (int i = 0; i < 9; i++) {
            if (gui.getItem(i) == null) gui.setItem(i, border);
            if (gui.getItem(i + 45) == null) gui.setItem(i + 45, border);
        }

        // Levý a pravý sloupec (kromě crafting oblasti)
        int[] borderSlots = {9, 17, 18, 26, 27, 35, 36, 44};
        for (int slot : borderSlots) {
            if (gui.getItem(slot) == null) gui.setItem(slot, border);
        }
        
        // Speciální sloty kolem result
        int[] resultBorderSlots = {13, 14, 15, 22, 23, 25, 31, 32, 33};
        for (int slot : resultBorderSlots) {
            if (gui.getItem(slot) == null) gui.setItem(slot, border);
        }
    }

    /**
     * Nastaví item na slot
     */
    private void setItem(Inventory gui, int slot, ItemStack item) {
        gui.setItem(slot, item);
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        
        String title = event.getView().getTitle();
        if (!title.equals(GUI_TITLE)) return;
        
        int slot = event.getSlot();
        
        // Crafting sloty - povoleno
        if (isCraftingSlot(slot)) {
            handleCraftingSlotClick(player, event);
            return;
        }
        
        // Result slot
        if (slot == RESULT_SLOT) {
            event.setCancelled(true);
            processCraft(player, event.getInventory());
            return;
        }
        
        // Ostatní sloty - zablokováno
        event.setCancelled(true);
        
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || clicked.getType() == Material.AIR) return;
        
        ItemMeta meta = clicked.getItemMeta();
        if (meta == null || meta.getDisplayName() == null) return;
        
        String displayName = meta.getDisplayName();
        handleButtonClick(player, displayName, event.getInventory());
    }

    /**
     * Zpracuje klik na crafting slot
     */
    private void handleCraftingSlotClick(Player player, InventoryClickEvent event) {
        // Aktualizuj crafting grid
        Bukkit.getScheduler().runTaskLater(plugin, () -> {
            updateCraftingGridFromGUI(player, event.getInventory());
            updateResultSlot(player, event.getInventory());
        }, 1L);
    }

    /**
     * Aktualizuje crafting grid z GUI
     */
    private void updateCraftingGridFromGUI(Player player, Inventory gui) {
        UUID playerUuid = player.getUniqueId();
        ItemStack[] craftingGrid = playerCraftingGrids.get(playerUuid);
        
        if (craftingGrid == null) return;
        
        for (int i = 0; i < 9; i++) {
            int slot = CRAFTING_SLOTS[i];
            ItemStack item = gui.getItem(slot);
            
            if (item != null && item.getType() != Material.LIGHT_GRAY_STAINED_GLASS_PANE) {
                craftingGrid[i] = item.clone();
            } else {
                craftingGrid[i] = null;
            }
        }
    }

    /**
     * Zpracuje klik na tlačítko
     */
    private void handleButtonClick(Player player, String displayName, Inventory gui) {
        switch (displayName) {
            case "§aRecipe Book":
                player.sendMessage("§eRecipe Book bude implementován později!");
                break;
            case "§cVyčistit":
                clearCraftingGrid(player, gui);
                break;
            case "§eNápověda":
                // Nápověda už je zobrazena v lore
                break;
            case "§cZavřít":
                player.closeInventory();
                break;
        }
    }

    /**
     * Zkontroluje, jestli je slot crafting slot
     */
    private boolean isCraftingSlot(int slot) {
        for (int craftingSlot : CRAFTING_SLOTS) {
            if (slot == craftingSlot) return true;
        }
        return false;
    }

    @EventHandler
    public void onInventoryClose(InventoryCloseEvent event) {
        if (!(event.getPlayer() instanceof Player)) return;
        Player player = (Player) event.getPlayer();
        
        String title = event.getView().getTitle();
        if (!title.equals(GUI_TITLE)) return;
        
        // Vrať předměty z crafting gridu
        UUID playerUuid = player.getUniqueId();
        ItemStack[] craftingGrid = playerCraftingGrids.get(playerUuid);
        
        if (craftingGrid != null) {
            for (ItemStack item : craftingGrid) {
                if (item != null && item.getType() != Material.AIR) {
                    player.getInventory().addItem(item);
                }
            }
            
            playerCraftingGrids.remove(playerUuid);
        }
    }
}
