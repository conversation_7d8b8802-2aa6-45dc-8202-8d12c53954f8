package cz.hypixel.skyblock.managers;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.BazaarOrder;
import cz.hypixel.skyblock.data.BazaarOrderType;
import cz.hypixel.skyblock.data.BazaarOrderStatus;
import cz.hypixel.skyblock.database.PlayerData;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.Comparator;
import java.util.HashMap;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;

/**
 * Manager pro Bazaar systém podle Hypixel SkyBlock
 */
public class BazaarManager {

    private final HypixelSkyBlockCZ plugin;
    private final Logger logger;
    private final DataSource dataSource;
    private final Map<String, List<BazaarOrder>> buyOrdersCache;
    private final Map<String, List<BazaarOrder>> sellOrdersCache;
    private final Set<String> bazaarItems;

    public BazaarManager(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.dataSource = plugin.getPlayerDataManager().getDataSource();
        this.buyOrdersCache = new HashMap<>();
        this.sellOrdersCache = new HashMap<>();
        this.bazaarItems = new HashSet<>();
        
        initializeBazaarItems();
        startOrderMatchingTask();
        
        logger.info("BazaarManager inicializován s " + bazaarItems.size() + " předměty");
    }

    /**
     * Inicializuje seznam předmětů dostupných v Bazaaru
     */
    private void initializeBazaarItems() {
        // Farming předměty
        bazaarItems.addAll(Arrays.asList(
            "WHEAT", "CARROT", "POTATO", "PUMPKIN", "MELON", "SUGAR_CANE", "CACTUS",
            "COCOA_BEANS", "NETHER_WART", "MUSHROOM", "INK_SACK", "RAW_FISH"
        ));
        
        // Mining předměty
        bazaarItems.addAll(Arrays.asList(
            "COBBLESTONE", "COAL", "IRON_INGOT", "GOLD_INGOT", "DIAMOND", "EMERALD",
            "LAPIS_LAZULI", "REDSTONE", "QUARTZ", "OBSIDIAN", "GLOWSTONE_DUST"
        ));
        
        // Combat předměty
        bazaarItems.addAll(Arrays.asList(
            "ROTTEN_FLESH", "BONE", "STRING", "SPIDER_EYE", "GUNPOWDER", "SLIME_BALL",
            "BLAZE_ROD", "GHAST_TEAR", "MAGMA_CREAM", "ENDER_PEARL"
        ));
        
        // Foraging předměty
        bazaarItems.addAll(Arrays.asList(
            "OAK_LOG", "BIRCH_LOG", "SPRUCE_LOG", "JUNGLE_LOG", "ACACIA_LOG", "DARK_OAK_LOG",
            "APPLE", "STICK", "FEATHER"
        ));
        
        // Enchanted předměty (příklady)
        bazaarItems.addAll(Arrays.asList(
            "ENCHANTED_WHEAT", "ENCHANTED_CARROT", "ENCHANTED_POTATO", "ENCHANTED_COAL",
            "ENCHANTED_IRON", "ENCHANTED_GOLD", "ENCHANTED_DIAMOND", "ENCHANTED_EMERALD"
        ));
    }

    /**
     * Vytvoří nový Bazaar order
     */
    public CompletableFuture<Boolean> createOrder(Player player, String itemName, BazaarOrderType orderType, 
                                                 int amount, double pricePerUnit) {
        return CompletableFuture.supplyAsync(() -> {
            UUID playerUuid = player.getUniqueId();
            
            // Zkontroluj, jestli je předmět v Bazaaru
            if (!isBazaarItem(itemName)) {
                player.sendMessage("§cTento předmět není dostupný v Bazaaru!");
                return false;
            }
            
            // Zkontroluj parametry
            if (amount <= 0 || pricePerUnit <= 0) {
                player.sendMessage("§cNeplatné množství nebo cena!");
                return false;
            }
            
            PlayerData playerData = plugin.getPlayerDataManager().loadPlayerData(playerUuid).join();
            if (playerData == null) {
                player.sendMessage("§cChyba při načítání dat!");
                return false;
            }
            
            double totalPrice = amount * pricePerUnit;
            
            if (orderType == BazaarOrderType.BUY) {
                // Buy order - zkontroluj mince
                if (playerData.getCoins() < totalPrice) {
                    player.sendMessage("§cNemáš dostatek mincí!");
                    player.sendMessage("§7Potřebuješ: §e" + BankManager.formatCoins(totalPrice));
                    return false;
                }
                
                // Odeber mince
                playerData.removeCoins(totalPrice);
                plugin.getPlayerDataManager().savePlayerData(playerData);
                
            } else {
                // Sell order - zkontroluj předměty
                if (!hasEnoughItems(player, itemName, amount)) {
                    player.sendMessage("§cNemáš dostatek předmětů!");
                    return false;
                }
                
                // Odeber předměty
                removeItemsFromInventory(player, itemName, amount);
            }
            
            // Vytvoř order
            BazaarOrder order = new BazaarOrder(playerUuid, itemName, orderType, amount, pricePerUnit);
            
            // Ulož do databáze
            String sql = """
                INSERT INTO bazaar_orders (player_uuid, item_name, order_type, amount, price_per_unit, 
                                         filled_amount, status, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;
            
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
                
                stmt.setString(1, playerUuid.toString());
                stmt.setString(2, order.getItemName());
                stmt.setString(3, order.getOrderType().name());
                stmt.setInt(4, order.getAmount());
                stmt.setDouble(5, order.getPricePerUnit());
                stmt.setInt(6, order.getFilledAmount());
                stmt.setString(7, order.getStatus().name());
                stmt.setLong(8, order.getCreatedAt());
                stmt.setLong(9, order.getUpdatedAt());
                
                int result = stmt.executeUpdate();
                
                if (result > 0) {
                    // Získej vygenerované ID
                    try (ResultSet rs = stmt.getGeneratedKeys()) {
                        if (rs.next()) {
                            order.setId(rs.getInt(1));
                        }
                    }
                    
                    // Přidej do cache
                    addOrderToCache(order);
                    
                    // Pokus se okamžitě spárovat s existujícími ordery
                    matchOrders(itemName);
                    
                    player.sendMessage("§aBazaar order vytvořen!");
                    player.sendMessage("§7Typ: " + orderType.getColoredName());
                    player.sendMessage("§7Předmět: §e" + getItemDisplayName(itemName));
                    player.sendMessage("§7Množství: §e" + amount);
                    player.sendMessage("§7Cena za kus: §e" + BankManager.formatCoins(pricePerUnit));
                    player.sendMessage("§7Celková cena: §e" + BankManager.formatCoins(totalPrice));
                    
                    return true;
                }
                
            } catch (SQLException e) {
                logger.severe("Chyba při vytváření Bazaar orderu: " + e.getMessage());
                
                // Vrať prostředky při chybě
                if (orderType == BazaarOrderType.BUY) {
                    playerData.addCoins(totalPrice);
                    plugin.getPlayerDataManager().savePlayerData(playerData);
                } else {
                    giveItemsToPlayer(player, itemName, amount);
                }
            }
            
            return false;
        });
    }

    /**
     * Instant sell - prodá předměty okamžitě za nejlepší cenu
     */
    public CompletableFuture<Boolean> instantSell(Player player, String itemName, int amount) {
        return CompletableFuture.supplyAsync(() -> {
            if (!hasEnoughItems(player, itemName, amount)) {
                player.sendMessage("§cNemáš dostatek předmětů!");
                return false;
            }
            
            // Najdi nejlepší buy ordery
            List<BazaarOrder> buyOrders = getBuyOrders(itemName).join();
            buyOrders.sort((a, b) -> Double.compare(b.getPricePerUnit(), a.getPricePerUnit())); // Nejvyšší cena první
            
            int remainingAmount = amount;
            double totalEarned = 0;
            
            for (BazaarOrder buyOrder : buyOrders) {
                if (remainingAmount <= 0) break;
                if (!buyOrder.isActive()) continue;
                
                int fillAmount = Math.min(remainingAmount, buyOrder.getRemainingAmount());
                double earned = fillAmount * buyOrder.getPricePerUnit();
                
                // Vyplň order
                buyOrder.fill(fillAmount);
                updateOrderInDatabase(buyOrder);
                
                // Dej mince prodávajícímu
                PlayerData sellerData = plugin.getPlayerDataManager().loadPlayerData(player.getUniqueId()).join();
                if (sellerData != null) {
                    sellerData.addCoins(earned);
                    plugin.getPlayerDataManager().savePlayerData(sellerData);
                }
                
                // Dej předměty kupujícímu
                Player buyer = Bukkit.getPlayer(buyOrder.getPlayerUuid());
                if (buyer != null) {
                    giveItemsToPlayer(buyer, itemName, fillAmount);
                    buyer.sendMessage("§aTvůj Bazaar buy order byl vyplněn!");
                    buyer.sendMessage("§7Předmět: §e" + getItemDisplayName(itemName));
                    buyer.sendMessage("§7Množství: §e" + fillAmount);
                    buyer.sendMessage("§7Cena: §c-" + BankManager.formatCoins(earned));
                }
                
                remainingAmount -= fillAmount;
                totalEarned += earned;
            }
            
            if (remainingAmount < amount) {
                // Odeber prodané předměty
                removeItemsFromInventory(player, itemName, amount - remainingAmount);
                
                player.sendMessage("§aInstant sell dokončen!");
                player.sendMessage("§7Prodáno: §e" + (amount - remainingAmount) + "x " + getItemDisplayName(itemName));
                player.sendMessage("§7Vydělano: §e+" + BankManager.formatCoins(totalEarned));
                
                if (remainingAmount > 0) {
                    player.sendMessage("§7Neprodáno: §c" + remainingAmount + "x §7(žádné buy ordery)");
                }
                
                return true;
            } else {
                player.sendMessage("§cŽádné buy ordery pro tento předmět!");
                return false;
            }
        });
    }

    /**
     * Instant buy - koupí předměty okamžitě za nejlepší cenu
     */
    public CompletableFuture<Boolean> instantBuy(Player player, String itemName, int amount) {
        return CompletableFuture.supplyAsync(() -> {
            // Najdi nejlepší sell ordery
            List<BazaarOrder> sellOrders = getSellOrders(itemName).join();
            sellOrders.sort(Comparator.comparingDouble(BazaarOrder::getPricePerUnit)); // Nejnižší cena první
            
            int remainingAmount = amount;
            double totalCost = 0;
            
            // Spočítej celkovou cenu
            for (BazaarOrder sellOrder : sellOrders) {
                if (remainingAmount <= 0) break;
                if (!sellOrder.isActive()) continue;
                
                int fillAmount = Math.min(remainingAmount, sellOrder.getRemainingAmount());
                totalCost += fillAmount * sellOrder.getPricePerUnit();
                remainingAmount -= fillAmount;
            }
            
            if (remainingAmount > 0) {
                player.sendMessage("§cNedostatek sell orderů pro tento předmět!");
                return false;
            }
            
            // Zkontroluj mince
            PlayerData buyerData = plugin.getPlayerDataManager().loadPlayerData(player.getUniqueId()).join();
            if (buyerData == null || buyerData.getCoins() < totalCost) {
                player.sendMessage("§cNemáš dostatek mincí!");
                player.sendMessage("§7Potřebuješ: §e" + BankManager.formatCoins(totalCost));
                return false;
            }
            
            // Proveď nákup
            buyerData.removeCoins(totalCost);
            plugin.getPlayerDataManager().savePlayerData(buyerData);
            
            remainingAmount = amount;
            double actualCost = 0;
            
            for (BazaarOrder sellOrder : sellOrders) {
                if (remainingAmount <= 0) break;
                if (!sellOrder.isActive()) continue;
                
                int fillAmount = Math.min(remainingAmount, sellOrder.getRemainingAmount());
                double cost = fillAmount * sellOrder.getPricePerUnit();
                
                // Vyplň order
                sellOrder.fill(fillAmount);
                updateOrderInDatabase(sellOrder);
                
                // Dej mince prodávajícímu
                PlayerData sellerData = plugin.getPlayerDataManager().loadPlayerData(sellOrder.getPlayerUuid()).join();
                if (sellerData != null) {
                    sellerData.addCoins(cost);
                    plugin.getPlayerDataManager().savePlayerData(sellerData);
                }
                
                // Pošli zprávu prodávajícímu
                Player seller = Bukkit.getPlayer(sellOrder.getPlayerUuid());
                if (seller != null) {
                    seller.sendMessage("§aTvůj Bazaar sell order byl vyplněn!");
                    seller.sendMessage("§7Předmět: §e" + getItemDisplayName(itemName));
                    seller.sendMessage("§7Množství: §e" + fillAmount);
                    seller.sendMessage("§7Vydělano: §e+" + BankManager.formatCoins(cost));
                }
                
                remainingAmount -= fillAmount;
                actualCost += cost;
            }
            
            // Dej předměty kupujícímu
            giveItemsToPlayer(player, itemName, amount);
            
            player.sendMessage("§aInstant buy dokončen!");
            player.sendMessage("§7Koupeno: §e" + amount + "x " + getItemDisplayName(itemName));
            player.sendMessage("§7Zaplaceno: §c-" + BankManager.formatCoins(actualCost));
            
            return true;
        });
    }

    /**
     * Získá buy ordery pro předmět
     */
    public CompletableFuture<List<BazaarOrder>> getBuyOrders(String itemName) {
        return CompletableFuture.supplyAsync(() -> {
            List<BazaarOrder> cached = buyOrdersCache.get(itemName);
            if (cached != null) {
                return new ArrayList<>(cached);
            }

            return loadOrdersFromDatabase(itemName, BazaarOrderType.BUY);
        });
    }

    /**
     * Získá sell ordery pro předmět
     */
    public CompletableFuture<List<BazaarOrder>> getSellOrders(String itemName) {
        return CompletableFuture.supplyAsync(() -> {
            List<BazaarOrder> cached = sellOrdersCache.get(itemName);
            if (cached != null) {
                return new ArrayList<>(cached);
            }

            return loadOrdersFromDatabase(itemName, BazaarOrderType.SELL);
        });
    }

    /**
     * Načte ordery z databáze
     */
    private List<BazaarOrder> loadOrdersFromDatabase(String itemName, BazaarOrderType orderType) {
        List<BazaarOrder> orders = new ArrayList<>();

        String sql = """
            SELECT id, player_uuid, item_name, order_type, amount, price_per_unit,
                   filled_amount, status, created_at, updated_at
            FROM bazaar_orders
            WHERE item_name = ? AND order_type = ? AND status = 'ACTIVE'
            ORDER BY price_per_unit DESC
        """;

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, itemName);
            stmt.setString(2, orderType.name());

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    BazaarOrder order = createOrderFromResultSet(rs);
                    if (order != null && order.isActive()) {
                        orders.add(order);
                    }
                }
            }

        } catch (SQLException e) {
            logger.severe("Chyba při načítání Bazaar orderů: " + e.getMessage());
        }

        return orders;
    }

    /**
     * Vytvoří BazaarOrder z ResultSet
     */
    private BazaarOrder createOrderFromResultSet(ResultSet rs) throws SQLException {
        BazaarOrder order = new BazaarOrder();
        order.setId(rs.getInt("id"));
        order.setPlayerUuid(UUID.fromString(rs.getString("player_uuid")));
        order.setItemName(rs.getString("item_name"));
        order.setOrderType(BazaarOrderType.fromString(rs.getString("order_type")));
        order.setAmount(rs.getInt("amount"));
        order.setPricePerUnit(rs.getDouble("price_per_unit"));
        order.setFilledAmount(rs.getInt("filled_amount"));
        order.setStatus(BazaarOrderStatus.fromString(rs.getString("status")));
        order.setCreatedAt(rs.getLong("created_at"));
        order.setUpdatedAt(rs.getLong("updated_at"));

        return order;
    }

    /**
     * Zkontroluje, jestli je předmět dostupný v Bazaaru
     */
    public boolean isBazaarItem(String itemName) {
        return bazaarItems.contains(itemName.toUpperCase());
    }

    /**
     * Získá všechny Bazaar předměty
     */
    public Set<String> getBazaarItems() {
        return new HashSet<>(bazaarItems);
    }

    /**
     * Získá ordery hráče
     */
    public CompletableFuture<List<BazaarOrder>> getPlayerOrders(UUID playerUuid) {
        return CompletableFuture.supplyAsync(() -> {
            List<BazaarOrder> orders = new ArrayList<>();

            String sql = """
                SELECT id, player_uuid, item_name, order_type, amount, price_per_unit,
                       filled_amount, status, created_at, updated_at
                FROM bazaar_orders
                WHERE player_uuid = ?
                ORDER BY created_at DESC
            """;

            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {

                stmt.setString(1, playerUuid.toString());

                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        BazaarOrder order = createOrderFromResultSet(rs);
                        if (order != null) {
                            orders.add(order);
                        }
                    }
                }

            } catch (SQLException e) {
                logger.severe("Chyba při načítání orderů hráče: " + e.getMessage());
            }

            return orders;
        });
    }

    /**
     * Spustí úlohu pro párování orderů
     */
    private void startOrderMatchingTask() {
        Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            for (String itemName : bazaarItems) {
                matchOrders(itemName);
            }
        }, 20 * 10, 20 * 10); // Každých 10 sekund
    }

    /**
     * Páruje buy a sell ordery pro předmět
     */
    private void matchOrders(String itemName) {
        List<BazaarOrder> buyOrders = getBuyOrders(itemName).join();
        List<BazaarOrder> sellOrders = getSellOrders(itemName).join();

        // Seřaď ordery podle ceny
        buyOrders.sort((a, b) -> Double.compare(b.getPricePerUnit(), a.getPricePerUnit())); // Nejvyšší buy cena první
        sellOrders.sort(Comparator.comparingDouble(BazaarOrder::getPricePerUnit)); // Nejnižší sell cena první

        for (BazaarOrder buyOrder : buyOrders) {
            if (!buyOrder.isActive()) continue;

            for (BazaarOrder sellOrder : sellOrders) {
                if (!sellOrder.isActive()) continue;

                // Zkontroluj, jestli se ceny překrývají
                if (buyOrder.getPricePerUnit() >= sellOrder.getPricePerUnit()) {
                    // Spáruj ordery
                    int matchAmount = Math.min(buyOrder.getRemainingAmount(), sellOrder.getRemainingAmount());
                    double matchPrice = sellOrder.getPricePerUnit(); // Použij sell cenu

                    // Vyplň ordery
                    buyOrder.fill(matchAmount);
                    sellOrder.fill(matchAmount);

                    // Aktualizuj v databázi
                    updateOrderInDatabase(buyOrder);
                    updateOrderInDatabase(sellOrder);

                    // Zpracuj transakci
                    processOrderMatch(buyOrder, sellOrder, matchAmount, matchPrice);

                    if (sellOrder.isCompleted()) {
                        break; // Sell order je vyplněn, přejdi na další buy order
                    }
                }
            }
        }
    }

    /**
     * Zpracuje spárování orderů
     */
    private void processOrderMatch(BazaarOrder buyOrder, BazaarOrder sellOrder, int amount, double pricePerUnit) {
        double totalPrice = amount * pricePerUnit;

        // Dej předměty kupujícímu
        Player buyer = Bukkit.getPlayer(buyOrder.getPlayerUuid());
        if (buyer != null) {
            giveItemsToPlayer(buyer, buyOrder.getItemName(), amount);
            buyer.sendMessage("§aTvůj Bazaar buy order byl spárován!");
            buyer.sendMessage("§7Předmět: §e" + getItemDisplayName(buyOrder.getItemName()));
            buyer.sendMessage("§7Množství: §e" + amount);
            buyer.sendMessage("§7Cena: §e" + BankManager.formatCoins(pricePerUnit) + " za kus");
        }

        // Dej mince prodávajícímu
        PlayerData sellerData = plugin.getPlayerDataManager().loadPlayerData(sellOrder.getPlayerUuid()).join();
        if (sellerData != null) {
            sellerData.addCoins(totalPrice);
            plugin.getPlayerDataManager().savePlayerData(sellerData);
        }

        Player seller = Bukkit.getPlayer(sellOrder.getPlayerUuid());
        if (seller != null) {
            seller.sendMessage("§aTvůj Bazaar sell order byl spárován!");
            seller.sendMessage("§7Předmět: §e" + getItemDisplayName(sellOrder.getItemName()));
            seller.sendMessage("§7Množství: §e" + amount);
            seller.sendMessage("§7Vydělano: §e+" + BankManager.formatCoins(totalPrice));
        }
    }

    /**
     * Zkontroluje, jestli má hráč dostatek předmětů
     */
    private boolean hasEnoughItems(Player player, String itemName, int amount) {
        Material material = getMaterialFromName(itemName);
        if (material == null) return false;

        int count = 0;
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.getType() == material) {
                count += item.getAmount();
            }
        }

        return count >= amount;
    }

    /**
     * Odebere předměty z inventáře
     */
    private void removeItemsFromInventory(Player player, String itemName, int amount) {
        Material material = getMaterialFromName(itemName);
        if (material == null) return;

        ItemStack itemToRemove = new ItemStack(material, amount);
        player.getInventory().removeItem(itemToRemove);
    }

    /**
     * Dá předměty hráči
     */
    private void giveItemsToPlayer(Player player, String itemName, int amount) {
        Material material = getMaterialFromName(itemName);
        if (material == null) return;

        ItemStack item = new ItemStack(material, amount);

        // Pokus se přidat do inventáře
        HashMap<Integer, ItemStack> leftover = player.getInventory().addItem(item);

        // Pokud se nevešlo vše, dropni zbytek
        if (!leftover.isEmpty()) {
            for (ItemStack leftoverItem : leftover.values()) {
                player.getWorld().dropItemNaturally(player.getLocation(), leftoverItem);
            }
            player.sendMessage("§7Některé předměty byly vyhozeny na zem!");
        }
    }

    /**
     * Převede název předmětu na Material
     */
    private Material getMaterialFromName(String itemName) {
        try {
            return Material.valueOf(itemName.toUpperCase());
        } catch (IllegalArgumentException e) {
            // Pro enchantované předměty použij základní materiál
            if (itemName.startsWith("ENCHANTED_")) {
                String baseName = itemName.substring(10);
                try {
                    return Material.valueOf(baseName);
                } catch (IllegalArgumentException ex) {
                    return null;
                }
            }
            return null;
        }
    }

    /**
     * Získá display název předmětu
     */
    private String getItemDisplayName(String itemName) {
        if (itemName.startsWith("ENCHANTED_")) {
            return "Enchanted " + itemName.substring(10).toLowerCase().replace("_", " ");
        }
        return itemName.toLowerCase().replace("_", " ");
    }

    /**
     * Aktualizuje order v databázi
     */
    private void updateOrderInDatabase(BazaarOrder order) {
        String sql = "UPDATE bazaar_orders SET filled_amount = ?, status = ?, updated_at = ? WHERE id = ?";

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setInt(1, order.getFilledAmount());
            stmt.setString(2, order.getStatus().name());
            stmt.setLong(3, order.getUpdatedAt());
            stmt.setInt(4, order.getId());

            stmt.executeUpdate();

        } catch (SQLException e) {
            logger.warning("Chyba při aktualizaci Bazaar orderu: " + e.getMessage());
        }
    }

    /**
     * Přidá order do cache
     */
    private void addOrderToCache(BazaarOrder order) {
        String itemName = order.getItemName();

        if (order.getOrderType() == BazaarOrderType.BUY) {
            buyOrdersCache.computeIfAbsent(itemName, k -> new ArrayList<>()).add(order);
        } else {
            sellOrdersCache.computeIfAbsent(itemName, k -> new ArrayList<>()).add(order);
        }
    }
}