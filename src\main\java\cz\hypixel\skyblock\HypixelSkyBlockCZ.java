package cz.hypixel.skyblock;

import cz.hypixel.skyblock.api.HypixelApiClient;
import cz.hypixel.skyblock.commands.SkyBlockCommand;
import cz.hypixel.skyblock.commands.IslandCommand;
import cz.hypixel.skyblock.commands.ShopCommand;
import cz.hypixel.skyblock.listeners.PlayerListener;
import cz.hypixel.skyblock.managers.IslandManager;
import cz.hypixel.skyblock.managers.PlayerDataManager;
import cz.hypixel.skyblock.managers.ShopManager;
import cz.hypixel.skyblock.managers.CollectionsManager;
import cz.hypixel.skyblock.managers.SkillsManager;
import cz.hypixel.skyblock.managers.BankManager;
import cz.hypixel.skyblock.managers.AuctionManager;
import cz.hypixel.skyblock.managers.BazaarManager;
import cz.hypixel.skyblock.commands.CollectionsCommand;
import cz.hypixel.skyblock.commands.SkillsCommand;
import cz.hypixel.skyblock.commands.BankCommand;
import cz.hypixel.skyblock.commands.AuctionCommand;
import cz.hypixel.skyblock.commands.BazaarCommand;
import cz.hypixel.skyblock.listeners.CollectionListener;
import org.bukkit.plugin.java.JavaPlugin;

import java.util.logging.Logger;

/**
 * Hlavní třída pluginu HypixelSkyBlockCZ
 * 
 * <AUTHOR>
 * @version 1.0
 */
public final class HypixelSkyBlockCZ extends JavaPlugin {

    private static HypixelSkyBlockCZ instance;
    private Logger logger;
    
    // Managers
    private IslandManager islandManager;
    private PlayerDataManager playerDataManager;
    private ShopManager shopManager;
    private CollectionsManager collectionsManager;
    private SkillsManager skillsManager;
    private BankManager bankManager;
    private AuctionManager auctionManager;
    private BazaarManager bazaarManager;

    // API Client
    private HypixelApiClient hypixelApiClient;

    @Override
    public void onEnable() {
        instance = this;
        logger = getLogger();
        
        logger.info("=== HypixelSkyBlockCZ ===");
        logger.info("Plugin se spouští...");
        
        // Inicializace konfigurace
        saveDefaultConfig();
        
        // Inicializace API klienta
        hypixelApiClient = new HypixelApiClient(this);

        // Inicializace managerů
        initializeManagers();
        
        // Registrace příkazů
        registerCommands();
        
        // Registrace event listenerů
        registerListeners();
        
        logger.info("Plugin úspěšně spuštěn!");
        logger.info("Verze: " + getDescription().getVersion());
        logger.info("Autor: " + getDescription().getAuthors());
        logger.info("========================");
    }

    @Override
    public void onDisable() {
        logger.info("=== HypixelSkyBlockCZ ===");
        logger.info("Plugin se vypína...");
        
        // Uložení dat
        if (playerDataManager != null) {
            playerDataManager.saveAllData();
            playerDataManager.shutdown();
        }

        if (islandManager != null) {
            islandManager.saveAllIslands();
        }

        // Ukončení API klienta
        if (hypixelApiClient != null) {
            hypixelApiClient.shutdown();
        }
        
        logger.info("Plugin úspěšně vypnut!");
        logger.info("========================");
    }
    
    /**
     * Inicializace všech managerů
     */
    private void initializeManagers() {
        logger.info("Inicializace managerů...");

        playerDataManager = new PlayerDataManager(this);
        islandManager = new IslandManager(this);
        shopManager = new ShopManager(this);
        collectionsManager = new CollectionsManager(this);
        skillsManager = new SkillsManager(this);
        bankManager = new BankManager(this);
        auctionManager = new AuctionManager(this);
        bazaarManager = new BazaarManager(this);

        logger.info("Managery úspěšně inicializovány!");
    }
    
    /**
     * Registrace všech příkazů
     */
    private void registerCommands() {
        logger.info("Registrace příkazů...");

        getCommand("skyblock").setExecutor(new SkyBlockCommand(this));
        getCommand("island").setExecutor(new IslandCommand(this));
        getCommand("shop").setExecutor(new ShopCommand(this));
        getCommand("collections").setExecutor(new CollectionsCommand(this));
        getCommand("skills").setExecutor(new SkillsCommand(this));
        getCommand("bank").setExecutor(new BankCommand(this, bankManager));
        getCommand("ah").setExecutor(new AuctionCommand(this));
        getCommand("bazaar").setExecutor(new BazaarCommand(this));

        logger.info("Příkazy úspěšně zaregistrovány!");
    }
    
    /**
     * Registrace všech event listenerů
     */
    private void registerListeners() {
        logger.info("Registrace event listenerů...");

        getServer().getPluginManager().registerEvents(new PlayerListener(this), this);
        getServer().getPluginManager().registerEvents(new CollectionListener(this), this);

        logger.info("Event listenery úspěšně zaregistrovány!");
    }
    
    // Gettery pro přístup k managerům
    
    public static HypixelSkyBlockCZ getInstance() {
        return instance;
    }
    
    public IslandManager getIslandManager() {
        return islandManager;
    }
    
    public PlayerDataManager getPlayerDataManager() {
        return playerDataManager;
    }
    
    public ShopManager getShopManager() {
        return shopManager;
    }

    public CollectionsManager getCollectionsManager() {
        return collectionsManager;
    }

    public SkillsManager getSkillsManager() {
        return skillsManager;
    }

    public BankManager getBankManager() {
        return bankManager;
    }

    public AuctionManager getAuctionManager() {
        return auctionManager;
    }

    public BazaarManager getBazaarManager() {
        return bazaarManager;
    }

    public HypixelApiClient getHypixelApiClient() {
        return hypixelApiClient;
    }
}
