package cz.hypixel.skyblock.data;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Data třída pro mazlíčky podle Hypixel SkyBlock
 */
public class Pet {
    
    private int id;
    private UUID ownerUuid;
    private PetType petType;
    private PetRarity rarity;
    private String customName;
    private double experience;
    private int level;
    private boolean active;
    private long obtainedAt;
    private Map<String, Object> metadata;
    
    public Pet() {
        this.experience = 0;
        this.level = 1;
        this.active = false;
        this.obtainedAt = System.currentTimeMillis();
        this.metadata = new HashMap<>();
    }
    
    public Pet(UUID ownerUuid, PetType petType, PetRarity rarity) {
        this();
        this.ownerUuid = ownerUuid;
        this.petType = petType;
        this.rarity = rarity;
        this.customName = petType.getDisplayName();
    }
    
    // Gettery a settery
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public UUID getOwnerUuid() {
        return ownerUuid;
    }
    
    public void setOwnerUuid(UUID ownerUuid) {
        this.ownerUuid = ownerUuid;
    }
    
    public PetType getPetType() {
        return petType;
    }
    
    public void setPetType(PetType petType) {
        this.petType = petType;
    }
    
    public PetRarity getRarity() {
        return rarity;
    }
    
    public void setRarity(PetRarity rarity) {
        this.rarity = rarity;
    }
    
    public String getCustomName() {
        return customName;
    }
    
    public void setCustomName(String customName) {
        this.customName = customName;
    }
    
    public double getExperience() {
        return experience;
    }
    
    public void setExperience(double experience) {
        this.experience = experience;
        this.level = calculateLevel(experience);
    }
    
    public int getLevel() {
        return level;
    }
    
    public void setLevel(int level) {
        this.level = level;
    }
    
    public boolean isActive() {
        return active;
    }
    
    public void setActive(boolean active) {
        this.active = active;
    }
    
    public long getObtainedAt() {
        return obtainedAt;
    }
    
    public void setObtainedAt(long obtainedAt) {
        this.obtainedAt = obtainedAt;
    }
    
    public Map<String, Object> getMetadata() {
        return metadata;
    }
    
    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }
    
    // Utility metody
    public void addExperience(double exp) {
        this.experience += exp;
        this.level = calculateLevel(this.experience);
    }
    
    public double getExperienceToNextLevel() {
        return getExperienceForLevel(level + 1) - experience;
    }
    
    public double getProgressToNextLevel() {
        if (level >= getMaxLevel()) {
            return 1.0;
        }
        
        double currentLevelExp = getExperienceForLevel(level);
        double nextLevelExp = getExperienceForLevel(level + 1);
        double progress = (experience - currentLevelExp) / (nextLevelExp - currentLevelExp);
        
        return Math.max(0, Math.min(1, progress));
    }
    
    public String getProgressBar() {
        double progress = getProgressToNextLevel();
        int bars = (int) (progress * 20);
        
        StringBuilder sb = new StringBuilder("§7[");
        for (int i = 0; i < 20; i++) {
            if (i < bars) {
                sb.append("§a■");
            } else {
                sb.append("§7■");
            }
        }
        sb.append("§7]");
        
        return sb.toString();
    }
    
    public String getDisplayName() {
        return rarity.getColorCode() + "[Lvl " + level + "] " + customName;
    }
    
    public String getFullDisplayName() {
        return rarity.getColorCode() + "[Lvl " + level + "] " + customName + " " + rarity.getDisplayName();
    }
    
    public int getMaxLevel() {
        return rarity.getMaxLevel();
    }
    
    public boolean isMaxLevel() {
        return level >= getMaxLevel();
    }
    
    /**
     * Spočítá level podle experience
     */
    private int calculateLevel(double exp) {
        for (int lvl = 1; lvl <= getMaxLevel(); lvl++) {
            if (exp < getExperienceForLevel(lvl)) {
                return lvl - 1;
            }
        }
        return getMaxLevel();
    }
    
    /**
     * Získá potřebnou experience pro level
     */
    private double getExperienceForLevel(int targetLevel) {
        if (targetLevel <= 1) return 0;
        
        // Hypixel SkyBlock pet experience formula
        double totalExp = 0;
        for (int i = 1; i < targetLevel; i++) {
            if (i <= 10) {
                totalExp += 100 * i;
            } else if (i <= 25) {
                totalExp += 150 * i;
            } else if (i <= 50) {
                totalExp += 250 * i;
            } else if (i <= 75) {
                totalExp += 500 * i;
            } else {
                totalExp += 1000 * i;
            }
        }
        
        return totalExp;
    }
    
    /**
     * Získá stat bonusy od mazlíčka
     */
    public Map<String, Double> getStatBonuses() {
        Map<String, Double> bonuses = new HashMap<>();
        
        // Základní bonusy podle typu mazlíčka
        Map<String, Double> baseBonuses = petType.getBaseBonuses();
        
        // Aplikuj level multiplikátor
        double levelMultiplier = 1.0 + (level - 1) * 0.1; // 10% za level
        
        // Aplikuj rarity multiplikátor
        double rarityMultiplier = rarity.getStatMultiplier();
        
        for (Map.Entry<String, Double> entry : baseBonuses.entrySet()) {
            double bonus = entry.getValue() * levelMultiplier * rarityMultiplier;
            bonuses.put(entry.getKey(), bonus);
        }
        
        return bonuses;
    }
    
    /**
     * Získá speciální schopnosti mazlíčka
     */
    public String[] getAbilities() {
        return petType.getAbilities(level, rarity);
    }
    
    /**
     * Zkontroluje, jestli má mazlíček určitou schopnost
     */
    public boolean hasAbility(String abilityName) {
        String[] abilities = getAbilities();
        for (String ability : abilities) {
            if (ability.contains(abilityName)) {
                return true;
            }
        }
        return false;
    }
    
    @Override
    public String toString() {
        return "Pet{" +
                "id=" + id +
                ", ownerUuid=" + ownerUuid +
                ", petType=" + petType +
                ", rarity=" + rarity +
                ", level=" + level +
                ", experience=" + experience +
                ", active=" + active +
                '}';
    }
}
