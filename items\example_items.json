[{"id": "ENCHANTED_WHEAT", "displayName": "Enchanted Wheat", "material": "WHEAT", "itemType": "ENCHANTED_MATERIAL", "rarity": "UNCOMMON", "stats": {"farming_fortune": 5.0}, "description": ["A magical wheat that glows with", "enchanted energy. Used in many", "advanced crafting recipes."], "abilities": [], "metadata": {"collection_item": true, "farming_material": true}, "requirements": [], "stackable": true, "maxStackSize": 64, "texture": null}, {"id": "ENCHANTED_COAL", "displayName": "Enchanted Coal", "material": "COAL", "itemType": "ENCHANTED_MATERIAL", "rarity": "UNCOMMON", "stats": {"mining_fortune": 5.0}, "description": ["Coal infused with magical energy.", "Burns much longer than regular coal."], "abilities": [], "metadata": {"collection_item": true, "mining_material": true, "fuel_value": 1600}, "requirements": [], "stackable": true, "maxStackSize": 64, "texture": null}, {"id": "ASPECT_OF_THE_END", "displayName": "Aspect of the End", "material": "DIAMOND_SWORD", "itemType": "SWORD", "rarity": "RARE", "stats": {"damage": 100.0, "strength": 100.0}, "description": ["A powerful sword from the End.", "Forged from the essence of", "Endermen and void energy."], "abilities": ["Teleport: Right-click to teleport 8 blocks ahead! Costs 50 Mana."], "metadata": {"weapon": true, "teleport_range": 8, "mana_cost": 50}, "requirements": ["Combat Level 12"], "stackable": false, "maxStackSize": 1, "texture": null}, {"id": "SUPERIOR_DRAGON_HELMET", "displayName": "Superior Dragon Helmet", "material": "DRAGON_HEAD", "itemType": "HELMET", "rarity": "LEGENDARY", "stats": {"health": 90.0, "defense": 130.0, "strength": 10.0, "crit_chance": 2.0, "crit_damage": 10.0, "intelligence": 25.0}, "description": ["The helmet of the Superior Dragon.", "Grants incredible power to those", "worthy enough to wear it."], "abilities": ["Superior Blood: Most of your stats are increased by 5% and Aspect of the Dragons ability deals 50% more damage."], "metadata": {"armor": true, "dragon_armor": true, "set_bonus": "superior_dragon"}, "requirements": ["Combat Level 20"], "stackable": false, "maxStackSize": 1, "texture": null}, {"id": "TREECAPITATOR_AXE", "displayName": "Treecapitator Axe", "material": "DIAMOND_AXE", "itemType": "AXE", "rarity": "EPIC", "stats": {"damage": 120.0, "strength": 40.0, "foraging_fortune": 50.0}, "description": ["An axe that can cut down", "entire trees in one swing.", "Perfect for efficient foraging."], "abilities": ["Treecapitator: Breaking one log breaks the entire tree!"], "metadata": {"tool": true, "treecapitator": true, "max_logs": 64}, "requirements": ["Foraging Level 15"], "stackable": false, "maxStackSize": 1, "texture": null}]