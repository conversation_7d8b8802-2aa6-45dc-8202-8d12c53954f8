package cz.hypixel.skyblock.data;

/**
 * Enum pro typy skillů v Hypixel SkyBlock
 */
public enum SkillType {
    
    // Hlavní skills
    COMBAT("Combat", "§c", false),
    MINING("Mining", "§8", false),
    FARMING("Farming", "§6", false),
    FORAGING("Foraging", "§2", false),
    FISHING("Fishing", "§3", false),
    ENCHANTING("Enchanting", "§d", false),
    ALCHEMY("Alchemy", "§e", false),
    TAMING("Taming", "§a", false),
    DUNGEONEERING("Dungeoneering", "§5", false),
    CARPENTRY("Carpentry", "§7", false),
    
    // Kosmetické skills
    SOCIAL("Social", "§b", true),
    RUNECRAFTING("Runecrafting", "§4", true);
    
    private final String displayName;
    private final String colorCode;
    private final boolean cosmetic;
    
    SkillType(String displayName, String colorCode, boolean cosmetic) {
        this.displayName = displayName;
        this.colorCode = colorCode;
        this.cosmetic = cosmetic;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getColorCode() {
        return colorCode;
    }
    
    public String getColoredName() {
        return colorCode + displayName;
    }
    
    public boolean isCosmetic() {
        return cosmetic;
    }
    
    public static SkillType fromString(String name) {
        for (SkillType skill : values()) {
            if (skill.name().equalsIgnoreCase(name) || 
                skill.displayName.equalsIgnoreCase(name)) {
                return skill;
            }
        }
        return null;
    }
}
