package cz.hypixel.skyblock.gui;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.AuctionCategory;
import cz.hypixel.skyblock.data.AuctionData;
import cz.hypixel.skyblock.managers.BankManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * GUI pro Auction House podle Hypixel SkyBlock
 */
public class AuctionHouseGUI implements Listener {

    private final HypixelSkyBlockCZ plugin;
    private static final String GUI_TITLE = "§6§lAuction House";
    private static final String CATEGORY_TITLE = "§6§lAuction House - ";

    public AuctionHouseGUI(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * Otevře hlavní Auction House menu
     */
    public void openAuctionHouse(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, GUI_TITLE);

        // Kategorie aukcí
        setItem(gui, 10, createCategoryItem(AuctionCategory.WEAPONS, player));
        setItem(gui, 11, createCategoryItem(AuctionCategory.ARMOR, player));
        setItem(gui, 12, createCategoryItem(AuctionCategory.ACCESSORIES, player));
        setItem(gui, 13, createCategoryItem(AuctionCategory.CONSUMABLES, player));
        setItem(gui, 14, createCategoryItem(AuctionCategory.BLOCKS, player));
        setItem(gui, 15, createCategoryItem(AuctionCategory.TOOLS, player));
        setItem(gui, 16, createCategoryItem(AuctionCategory.MISC, player));

        setItem(gui, 19, createCategoryItem(AuctionCategory.PETS, player));
        setItem(gui, 20, createCategoryItem(AuctionCategory.ENCHANTED_BOOKS, player));
        setItem(gui, 21, createCategoryItem(AuctionCategory.REFORGE_STONES, player));

        // Speciální tlačítka
        setItem(gui, 48, createMyAuctionsButton());
        setItem(gui, 49, createCreateAuctionButton());
        setItem(gui, 50, createSearchButton());

        // Dekorativní předměty
        fillBorders(gui);

        player.openInventory(gui);
    }

    /**
     * Otevře GUI pro konkrétní kategorii
     */
    public void openCategoryMenu(Player player, AuctionCategory category, int page) {
        String title = CATEGORY_TITLE + category.getColoredName();
        Inventory gui = Bukkit.createInventory(null, 54, title);

        // Načti aukce pro kategorii
        plugin.getAuctionManager().getAuctionsByCategory(category, page, 28).thenAccept(auctions -> {
            Bukkit.getScheduler().runTask(plugin, () -> {
                int slot = 10;
                for (AuctionData auction : auctions) {
                    if (slot >= 44) break;
                    
                    ItemStack item = createAuctionItem(auction);
                    gui.setItem(slot, item);
                    
                    slot++;
                    if (slot == 17) slot = 19;
                    if (slot == 26) slot = 28;
                    if (slot == 35) slot = 37;
                }

                // Navigační tlačítka
                if (page > 0) {
                    setItem(gui, 45, createPreviousPageButton(page));
                }
                setItem(gui, 49, createBackButton());
                setItem(gui, 53, createNextPageButton(page));

                fillBorders(gui);
                
                // Znovu otevři GUI s načtenými daty
                player.closeInventory();
                player.openInventory(gui);
            });
        });

        // Dočasně otevři prázdné GUI
        fillBorders(gui);
        setItem(gui, 22, createLoadingItem());
        player.openInventory(gui);
    }

    /**
     * Vytvoří item pro kategorii
     */
    private ItemStack createCategoryItem(AuctionCategory category, Player player) {
        ItemStack item = new ItemStack(category.getDisplayMaterial());
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName(category.getColoredName());
        
        List<String> lore = Arrays.asList(
            "",
            "§7Klikni pro zobrazení aukcí",
            "§7v této kategorii!",
            "",
            "§eKlikni pro otevření!"
        );
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }

    /**
     * Vytvoří item pro aukci
     */
    private ItemStack createAuctionItem(AuctionData auction) {
        // Pokus se deserializovat původní předmět
        ItemStack item = plugin.getAuctionManager().deserializeItemStack(auction.getItemData());
        if (item == null) {
            item = new ItemStack(Material.BARRIER);
        }
        
        ItemMeta meta = item.getItemMeta();
        if (meta == null) {
            meta = Bukkit.getItemFactory().getItemMeta(item.getType());
        }
        
        meta.setDisplayName("§e" + auction.getItemName());
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Prodávající: §e" + getPlayerName(auction.getSellerUuid()));
        lore.add("§7Kategorie: " + auction.getCategory().getColoredName());
        
        if (auction.isBuyItNow()) {
            lore.add("");
            lore.add("§6§lBUY IT NOW");
            lore.add("§7Cena: §6" + BankManager.formatCoins(auction.getBinPrice()));
        } else {
            lore.add("");
            if (auction.getCurrentBid() > 0) {
                lore.add("§7Současný bid: §e" + BankManager.formatCoins(auction.getCurrentBid()));
                lore.add("§7Nejvyšší dražitel: §e" + getPlayerName(auction.getHighestBidderUuid()));
            } else {
                lore.add("§7Počáteční cena: §e" + BankManager.formatCoins(auction.getStartingBid()));
                lore.add("§7Žádné bidy");
            }
            lore.add("§7Další min. bid: §e" + BankManager.formatCoins(auction.getNextMinimumBid()));
        }
        
        lore.add("");
        lore.add("§7Zbývá: §e" + auction.getTimeLeftFormatted());
        lore.add("");
        
        if (auction.isBuyItNow()) {
            lore.add("§eKlikni pro koupi!");
        } else {
            lore.add("§eKlikni pro dražení!");
        }
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }

    /**
     * Vytvoří tlačítko "Moje aukce"
     */
    private ItemStack createMyAuctionsButton() {
        ItemStack item = new ItemStack(Material.PLAYER_HEAD);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§aMoje aukce");
        meta.setLore(Arrays.asList("", "§7Zobraz své aktivní aukce", "", "§eKlikni pro otevření!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Vytvořit aukci"
     */
    private ItemStack createCreateAuctionButton() {
        ItemStack item = new ItemStack(Material.GOLD_BLOCK);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§6Vytvořit aukci");
        meta.setLore(Arrays.asList("", "§7Prodej předmět v aukci", "", "§eKlikni pro vytvoření!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Hledat"
     */
    private ItemStack createSearchButton() {
        ItemStack item = new ItemStack(Material.COMPASS);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§bHledat");
        meta.setLore(Arrays.asList("", "§7Hledej konkrétní předměty", "", "§eKlikni pro hledání!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Zpět"
     */
    private ItemStack createBackButton() {
        ItemStack item = new ItemStack(Material.ARROW);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§cZpět");
        meta.setLore(Arrays.asList("", "§7Klikni pro návrat"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Předchozí stránka"
     */
    private ItemStack createPreviousPageButton(int currentPage) {
        ItemStack item = new ItemStack(Material.ARROW);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§ePředchozí stránka");
        meta.setLore(Arrays.asList("", "§7Stránka: §e" + currentPage, "", "§eKlikni pro přechod!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Další stránka"
     */
    private ItemStack createNextPageButton(int currentPage) {
        ItemStack item = new ItemStack(Material.ARROW);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§eNalší stránka");
        meta.setLore(Arrays.asList("", "§7Stránka: §e" + (currentPage + 2), "", "§eKlikni pro přechod!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří loading item
     */
    private ItemStack createLoadingItem() {
        ItemStack item = new ItemStack(Material.CLOCK);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§eNačítání...");
        meta.setLore(Arrays.asList("", "§7Načítají se aukce..."));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vyplní okraje GUI
     */
    private void fillBorders(Inventory gui) {
        ItemStack border = new ItemStack(Material.YELLOW_STAINED_GLASS_PANE);
        ItemMeta meta = border.getItemMeta();
        meta.setDisplayName(" ");
        border.setItemMeta(meta);

        // Horní a dolní řada
        for (int i = 0; i < 9; i++) {
            if (gui.getItem(i) == null) gui.setItem(i, border);
            if (gui.getItem(i + 45) == null) gui.setItem(i + 45, border);
        }

        // Levý a pravý sloupec
        for (int i = 9; i < 45; i += 9) {
            if (gui.getItem(i) == null) gui.setItem(i, border);
            if (gui.getItem(i + 8) == null) gui.setItem(i + 8, border);
        }
    }

    /**
     * Nastaví item na slot
     */
    private void setItem(Inventory gui, int slot, ItemStack item) {
        gui.setItem(slot, item);
    }

    /**
     * Získá jméno hráče podle UUID
     */
    private String getPlayerName(java.util.UUID uuid) {
        if (uuid == null) return "Neznámý";
        
        Player player = Bukkit.getPlayer(uuid);
        if (player != null) {
            return player.getName();
        }
        
        // V produkci by se mělo načítat z databáze
        return "Offline hráč";
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        
        String title = event.getView().getTitle();
        if (!title.startsWith("§6§lAuction House")) return;
        
        event.setCancelled(true);
        
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || clicked.getType() == Material.AIR) return;
        
        ItemMeta meta = clicked.getItemMeta();
        if (meta == null || meta.getDisplayName() == null) return;
        
        String displayName = meta.getDisplayName();
        
        // Hlavní menu
        if (title.equals(GUI_TITLE)) {
            handleMainMenuClick(player, displayName);
        }
        // Kategorie menu
        else if (title.startsWith(CATEGORY_TITLE)) {
            handleCategoryMenuClick(player, displayName, title);
        }
    }

    /**
     * Zpracuje klik v hlavním menu
     */
    private void handleMainMenuClick(Player player, String displayName) {
        // Kategorie
        for (AuctionCategory category : AuctionCategory.values()) {
            if (displayName.contains(category.getDisplayName())) {
                openCategoryMenu(player, category, 0);
                return;
            }
        }
        
        // Speciální tlačítka
        if (displayName.equals("§aMoje aukce")) {
            // TODO: Implementovat GUI pro hráčovy aukce
            player.sendMessage("§eMoje aukce budou implementovány později!");
        } else if (displayName.equals("§6Vytvořit aukci")) {
            // TODO: Implementovat GUI pro vytvoření aukce
            player.sendMessage("§eVytvoření aukce bude implementováno později!");
        } else if (displayName.equals("§bHledat")) {
            // TODO: Implementovat vyhledávání
            player.sendMessage("§eVyhledávání bude implementováno později!");
        }
    }

    /**
     * Zpracuje klik v menu kategorie
     */
    private void handleCategoryMenuClick(Player player, String displayName, String title) {
        if (displayName.equals("§cZpět")) {
            openAuctionHouse(player);
        } else if (displayName.equals("§ePředchozí stránka")) {
            // TODO: Implementovat stránkování
            player.sendMessage("§eStránkování bude implementováno později!");
        } else if (displayName.equals("§eNalší stránka")) {
            // TODO: Implementovat stránkování
            player.sendMessage("§eStránkování bude implementováno později!");
        } else {
            // TODO: Implementovat klik na aukci (bid/buy)
            player.sendMessage("§eInterakce s aukcemi budou implementovány později!");
        }
    }
}
