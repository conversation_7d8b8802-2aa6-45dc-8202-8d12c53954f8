package cz.hypixel.skyblock.data;

import org.bukkit.Material;

/**
 * Data třída pro skill informace
 */
public class SkillData {
    
    private final SkillType skillType;
    private final String displayName;
    private final String description;
    private final Material displayMaterial;
    private final String statBonus;
    private final String passiveAbility;
    private final int maxLevel;
    
    public SkillData(SkillType skillType, String displayName, String description, 
                     Material displayMaterial, String statBonus, String passiveAbility, int maxLevel) {
        this.skillType = skillType;
        this.displayName = displayName;
        this.description = description;
        this.displayMaterial = displayMaterial;
        this.statBonus = statBonus;
        this.passiveAbility = passiveAbility;
        this.maxLevel = maxLevel;
    }
    
    public SkillType getSkillType() {
        return skillType;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getDescription() {
        return description;
    }
    
    public Material getDisplayMaterial() {
        return displayMaterial;
    }
    
    public String getStatBonus() {
        return statBonus;
    }
    
    public String getPassiveAbility() {
        return passiveAbility;
    }
    
    public int getMaxLevel() {
        return maxLevel;
    }
    
    public String getColoredName() {
        return skillType.getColorCode() + displayName;
    }
    
    public boolean isCosmetic() {
        return skillType.isCosmetic();
    }
    
    @Override
    public String toString() {
        return "SkillData{" +
                "skillType=" + skillType +
                ", displayName='" + displayName + '\'' +
                ", maxLevel=" + maxLevel +
                '}';
    }
}
