package cz.hypixel.skyblock.commands;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.Minion;
import cz.hypixel.skyblock.data.MinionType;
import cz.hypixel.skyblock.gui.MinionGUI;
import cz.hypixel.skyblock.managers.BankManager;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.List;
import java.util.Map;

/**
 * Příkaz pro Minion systém
 */
public class MinionCommand implements CommandExecutor {

    private final HypixelSkyBlockCZ plugin;
    private final MinionGUI minionGUI;

    public MinionCommand(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.minionGUI = new MinionGUI(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cTento příkaz může použít pouze hráč!");
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            showHelp(player);
            return true;
        }

        String action = args[0].toLowerCase();

        switch (action) {
            case "place":
            case "create":
                handlePlaceMinion(player, args);
                break;
                
            case "list":
            case "my":
                handleListMinions(player);
                break;
                
            case "info":
                handleMinionInfo(player, args);
                break;
                
            case "recipe":
                handleShowRecipe(player, args);
                break;
                
            case "types":
                handleShowTypes(player);
                break;
                
            case "help":
                showHelp(player);
                break;
                
            default:
                player.sendMessage("§cNeznámý příkaz! Použij §e/minion help §cpro nápovědu.");
                break;
        }

        return true;
    }

    /**
     * Zpracuje umístění miniona
     */
    private void handlePlaceMinion(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage("§cPoužití: /minion place <typ> <tier>");
            player.sendMessage("§7Příklad: /minion place wheat 1");
            return;
        }

        String typeName = args[1];
        MinionType minionType = MinionType.fromString(typeName);
        
        if (minionType == null) {
            player.sendMessage("§cNeznámý typ miniona: " + typeName);
            player.sendMessage("§7Použij §e/minion types §7pro seznam typů.");
            return;
        }

        int tier;
        try {
            tier = Integer.parseInt(args[2]);
        } catch (NumberFormatException e) {
            player.sendMessage("§cNeplatný tier!");
            return;
        }

        if (tier < 1 || tier > minionType.getMaxTier()) {
            player.sendMessage("§cTier musí být mezi 1-" + minionType.getMaxTier() + "!");
            return;
        }

        // Umísti miniona na pozici hráče
        Location location = player.getLocation().getBlock().getLocation().add(0, 1, 0);
        
        plugin.getMinionManager().placeMinion(player, minionType, tier, location)
            .thenAccept(success -> {
                if (!success) {
                    player.sendMessage("§cNepodařilo se umístit miniona!");
                }
            });
    }

    /**
     * Zobrazí seznam minionů hráče
     */
    private void handleListMinions(Player player) {
        List<Minion> minions = plugin.getMinionManager().getPlayerMinions(player.getUniqueId());
        
        player.sendMessage("§6§l=== MOJE MINIONI ===");
        player.sendMessage("§7Počet: §e" + minions.size() + "§7/§e" + 
                         plugin.getMinionManager().getMaxMinionsForPlayer(player.getUniqueId()));
        
        if (minions.isEmpty()) {
            player.sendMessage("§7Nemáš žádné miniony.");
            player.sendMessage("§7Použij §e/minion place <typ> <tier> §7pro vytvoření.");
            return;
        }

        for (int i = 0; i < Math.min(minions.size(), 10); i++) {
            Minion minion = minions.get(i);
            String status = minion.isActive() ? "§aAktivní" : "§cNeaktivní";
            String location = formatLocation(minion.getLocation());
            
            player.sendMessage("§7[§e" + minion.getId() + "§7] " + 
                             minion.getDisplayName() + " §7- " + status);
            player.sendMessage("  §7Pozice: §e" + location);
            player.sendMessage("  §7Storage: §e" + minion.getStorage().size() + "§7/§e" + minion.getMaxStorageSlots());
            player.sendMessage("  §7Uptime: §e" + minion.getUptimeFormatted());
        }
        
        if (minions.size() > 10) {
            player.sendMessage("§7... a " + (minions.size() - 10) + " dalších");
        }
    }

    /**
     * Zobrazí informace o minion typu
     */
    private void handleMinionInfo(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cPoužití: /minion info <typ>");
            player.sendMessage("§7Příklad: /minion info wheat");
            return;
        }

        String typeName = args[1];
        MinionType minionType = MinionType.fromString(typeName);
        
        if (minionType == null) {
            player.sendMessage("§cNeznámý typ miniona: " + typeName);
            return;
        }

        player.sendMessage("§6§l=== " + minionType.getColoredName().toUpperCase() + " ===");
        player.sendMessage("§7Kategorie: §e" + minionType.getCategory());
        player.sendMessage("§7Max Tier: §e" + minionType.getMaxTier());
        player.sendMessage("§7Základní rychlost: §e" + minionType.getBaseActionTime() + "s");
        player.sendMessage("");
        
        player.sendMessage("§7Produkce podle tierů:");
        for (int tier = 1; tier <= Math.min(minionType.getMaxTier(), 5); tier++) {
            ItemStack[] items = minionType.generateItems(tier);
            StringBuilder production = new StringBuilder();
            for (ItemStack item : items) {
                if (item != null) {
                    if (production.length() > 0) production.append(", ");
                    production.append(item.getAmount()).append("x ").append(item.getType().name());
                }
            }
            
            double speed = minionType.getBaseActionTime() / Math.pow(1.1, tier - 1);
            player.sendMessage("§7- Tier " + tier + ": §e" + production + " §7(každých " + 
                             String.format("%.1f", speed) + "s)");
        }
        
        if (minionType.getMaxTier() > 5) {
            player.sendMessage("§7... a " + (minionType.getMaxTier() - 5) + " dalších tierů");
        }
        
        player.sendMessage("");
        player.sendMessage("§7Upgrade ceny:");
        for (int tier = 2; tier <= Math.min(minionType.getMaxTier(), 6); tier++) {
            double cost = minionType.getUpgradeCost(tier);
            player.sendMessage("§7- Tier " + tier + ": §6" + BankManager.formatCoins(cost));
        }
    }

    /**
     * Zobrazí crafting recept
     */
    private void handleShowRecipe(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage("§cPoužití: /minion recipe <typ> <tier>");
            player.sendMessage("§7Příklad: /minion recipe wheat 1");
            return;
        }

        String typeName = args[1];
        MinionType minionType = MinionType.fromString(typeName);
        
        if (minionType == null) {
            player.sendMessage("§cNeznámý typ miniona: " + typeName);
            return;
        }

        int tier;
        try {
            tier = Integer.parseInt(args[2]);
        } catch (NumberFormatException e) {
            player.sendMessage("§cNeplatný tier!");
            return;
        }

        if (tier < 1 || tier > minionType.getMaxTier()) {
            player.sendMessage("§cTier musí být mezi 1-" + minionType.getMaxTier() + "!");
            return;
        }

        Map<Material, Integer> recipe = minionType.getCraftingRecipe(tier);
        
        player.sendMessage("§6§l=== RECEPT ===");
        player.sendMessage("§7Minion: " + minionType.getColoredName() + " Tier " + tier);
        player.sendMessage("");
        player.sendMessage("§7Potřebné materiály:");
        
        for (Map.Entry<Material, Integer> entry : recipe.entrySet()) {
            int has = countItems(player, entry.getKey());
            int needed = entry.getValue();
            String color = has >= needed ? "§a" : "§c";
            
            player.sendMessage("§7- " + entry.getKey().name() + ": " + color + has + "§7/" + needed);
        }
        
        if (tier > 1) {
            double upgradeCost = minionType.getUpgradeCost(tier);
            player.sendMessage("");
            player.sendMessage("§7Cena upgradu: §6" + BankManager.formatCoins(upgradeCost));
        }
    }

    /**
     * Zobrazí všechny typy minionů
     */
    private void handleShowTypes(Player player) {
        player.sendMessage("§6§l=== TYPY MINIONŮ ===");
        
        // Farming
        player.sendMessage("§e§lFarming:");
        MinionType[] farmingMinions = MinionType.getByCategory("farming");
        for (MinionType type : farmingMinions) {
            player.sendMessage("§7- " + type.getColoredName() + " §7(Max tier: " + type.getMaxTier() + ")");
        }
        
        // Mining
        player.sendMessage("");
        player.sendMessage("§7§lMining:");
        MinionType[] miningMinions = MinionType.getByCategory("mining");
        for (MinionType type : miningMinions) {
            player.sendMessage("§7- " + type.getColoredName() + " §7(Max tier: " + type.getMaxTier() + ")");
        }
        
        // Combat
        player.sendMessage("");
        player.sendMessage("§c§lCombat:");
        MinionType[] combatMinions = MinionType.getByCategory("combat");
        for (MinionType type : combatMinions) {
            player.sendMessage("§7- " + type.getColoredName() + " §7(Max tier: " + type.getMaxTier() + ")");
        }
        
        // Foraging
        player.sendMessage("");
        player.sendMessage("§2§lForaging:");
        MinionType[] foragingMinions = MinionType.getByCategory("foraging");
        for (MinionType type : foragingMinions) {
            player.sendMessage("§7- " + type.getColoredName() + " §7(Max tier: " + type.getMaxTier() + ")");
        }
        
        // Fishing
        player.sendMessage("");
        player.sendMessage("§9§lFishing:");
        MinionType[] fishingMinions = MinionType.getByCategory("fishing");
        for (MinionType type : fishingMinions) {
            player.sendMessage("§7- " + type.getColoredName() + " §7(Max tier: " + type.getMaxTier() + ")");
        }
    }

    /**
     * Zobrazí nápovědu
     */
    private void showHelp(Player player) {
        player.sendMessage("§6§l=== MINION SYSTÉM - NÁPOVĚDA ===");
        player.sendMessage("§e/minion place <typ> <tier> §7- Umístí miniona");
        player.sendMessage("§e/minion list §7- Zobrazí tvoje miniony");
        player.sendMessage("§e/minion info <typ> §7- Informace o typu miniona");
        player.sendMessage("§e/minion recipe <typ> <tier> §7- Zobrazí crafting recept");
        player.sendMessage("§e/minion types §7- Zobrazí všechny typy minionů");
        player.sendMessage("");
        player.sendMessage("§7Příklady:");
        player.sendMessage("§7- §e/minion place wheat 1 §7- Umístí Wheat Minion Tier 1");
        player.sendMessage("§7- §e/minion info coal §7- Info o Coal Minion");
        player.sendMessage("§7- §e/minion recipe diamond 5 §7- Recept pro Diamond Minion Tier 5");
        player.sendMessage("");
        player.sendMessage("§7Minioni automaticky produkují předměty a přidávají");
        player.sendMessage("§7XP do příslušných skillů a kolekcí!");
    }

    /**
     * Spočítá předměty v inventáři
     */
    private int countItems(Player player, Material material) {
        int count = 0;
        for (ItemStack item : player.getInventory().getContents()) {
            if (item != null && item.getType() == material) {
                count += item.getAmount();
            }
        }
        return count;
    }

    /**
     * Formátuje lokaci
     */
    private String formatLocation(Location location) {
        return String.format("%.0f, %.0f, %.0f", location.getX(), location.getY(), location.getZ());
    }
}
