package cz.hypixel.skyblock.commands;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.SkillType;
import cz.hypixel.skyblock.gui.SkillsGUI;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * Příkaz pro otevření Skills GUI a správu skillů
 */
public class SkillsCommand implements CommandExecutor {

    private final HypixelSkyBlockCZ plugin;
    private final SkillsGUI skillsGUI;

    public SkillsCommand(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.skillsGUI = new SkillsGUI(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cTento příkaz může použít pouze hráč!");
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            // Otevři Skills GUI
            skillsGUI.openSkillsMenu(player);
            return true;
        }

        if (args.length == 1) {
            String skillName = args[0].toLowerCase();
            
            // Zobraz informace o konkrétním skillu
            SkillType skillType = SkillType.fromString(skillName);
            if (skillType == null) {
                player.sendMessage("§cNeplatný skill! Dostupné skills:");
                for (SkillType skill : SkillType.values()) {
                    player.sendMessage("§7- §e" + skill.getDisplayName());
                }
                return true;
            }

            int level = plugin.getSkillsManager().getPlayerSkillLevel(player, skillType);
            double xp = plugin.getSkillsManager().getPlayerSkillXp(player, skillType);
            double xpToNext = plugin.getSkillsManager().getXpToNextLevel(skillType, xp);

            player.sendMessage("§6§l=== " + skillType.getColoredName() + " §6§l===");
            player.sendMessage("§7Level: §e" + level);
            player.sendMessage("§7XP: §e" + String.format("%.0f", xp));
            
            if (xpToNext > 0) {
                player.sendMessage("§7XP do dalšího levelu: §e" + String.format("%.0f", xpToNext));
            } else {
                player.sendMessage("§a§lMAX LEVEL!");
            }

            return true;
        }

        if (args.length == 3 && args[0].equalsIgnoreCase("add") && sender.hasPermission("skyblock.admin")) {
            // Admin příkaz pro přidání XP: /skills add <skill> <xp>
            String skillName = args[1];
            SkillType skillType = SkillType.fromString(skillName);
            
            if (skillType == null) {
                player.sendMessage("§cNeplatný skill!");
                return true;
            }

            try {
                double xp = Double.parseDouble(args[2]);
                boolean success = plugin.getSkillsManager().addSkillXp(player, skillType, xp);
                
                if (success) {
                    player.sendMessage("§aPřidáno " + xp + " XP do " + skillType.getColoredName() + "!");
                } else {
                    player.sendMessage("§cChyba při přidávání XP!");
                }
            } catch (NumberFormatException e) {
                player.sendMessage("§cNeplatné číslo XP!");
            }

            return true;
        }

        // Nápověda
        player.sendMessage("§6§l=== Skills Příkazy ===");
        player.sendMessage("§e/skills §7- Otevře Skills GUI");
        player.sendMessage("§e/skills <skill> §7- Zobrazí informace o skillu");
        
        if (sender.hasPermission("skyblock.admin")) {
            player.sendMessage("§e/skills add <skill> <xp> §7- Přidá XP do skillu");
        }

        return true;
    }
}
