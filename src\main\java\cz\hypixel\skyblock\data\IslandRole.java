package cz.hypixel.skyblock.data;

import java.util.Arrays;
import java.util.List;

/**
 * Enum pro role na ostrově podle Hypixel SkyBlock
 */
public enum IslandRole {
    
    OWNER("Vlastník", "§c", Arrays.asList(
        "island.build", "island.break", "island.interact", "island.invite", 
        "island.kick", "island.promote", "island.demote", "island.settings",
        "island.delete", "island.warp", "island.chest", "island.animals"
    )),
    
    CO_OWNER("Spoluvlastník", "§6", Arrays.asList(
        "island.build", "island.break", "island.interact", "island.invite",
        "island.kick", "island.promote", "island.warp", "island.chest", "island.animals"
    )),
    
    MODERATOR("Moderátor", "§e", Arrays.asList(
        "island.build", "island.break", "island.interact", "island.invite",
        "island.warp", "island.chest", "island.animals"
    )),
    
    MEMBER("Člen", "§a", Arrays.asList(
        "island.build", "island.break", "island.interact", "island.warp", "island.chest"
    )),
    
    GUEST("Host", "§7", Arrays.asList(
        "island.interact", "island.warp"
    ));
    
    private final String displayName;
    private final String colorCode;
    private final List<String> permissions;
    
    IslandRole(String displayName, String colorCode, List<String> permissions) {
        this.displayName = displayName;
        this.colorCode = colorCode;
        this.permissions = permissions;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getColorCode() {
        return colorCode;
    }
    
    public String getColoredName() {
        return colorCode + displayName;
    }
    
    public List<String> getPermissions() {
        return permissions;
    }
    
    public boolean hasPermission(String permission) {
        return permissions.contains(permission);
    }
    
    public boolean canBuild() {
        return hasPermission("island.build");
    }
    
    public boolean canBreak() {
        return hasPermission("island.break");
    }
    
    public boolean canInteract() {
        return hasPermission("island.interact");
    }
    
    public boolean canInvite() {
        return hasPermission("island.invite");
    }
    
    public boolean canKick() {
        return hasPermission("island.kick");
    }
    
    public boolean canPromote() {
        return hasPermission("island.promote");
    }
    
    public boolean canDemote() {
        return hasPermission("island.demote");
    }
    
    public boolean canAccessChests() {
        return hasPermission("island.chest");
    }
    
    public boolean canManageAnimals() {
        return hasPermission("island.animals");
    }
    
    public boolean canUseWarps() {
        return hasPermission("island.warp");
    }
    
    public boolean canChangeSettings() {
        return hasPermission("island.settings");
    }
    
    public boolean canDeleteIsland() {
        return hasPermission("island.delete");
    }
    
    /**
     * Získá roli podle názvu
     */
    public static IslandRole fromString(String name) {
        for (IslandRole role : values()) {
            if (role.name().equalsIgnoreCase(name) || 
                role.displayName.equalsIgnoreCase(name)) {
                return role;
            }
        }
        return null;
    }
    
    /**
     * Zkontroluje, jestli může tato role povýšit cílovou roli
     */
    public boolean canPromote(IslandRole targetRole) {
        if (!canPromote()) {
            return false;
        }
        
        // Vlastník může povýšit kohokoliv
        if (this == OWNER) {
            return true;
        }
        
        // Spoluvlastník může povýšit pouze členy a hosty
        if (this == CO_OWNER) {
            return targetRole == MEMBER || targetRole == GUEST;
        }
        
        // Moderátor může povýšit pouze hosty
        if (this == MODERATOR) {
            return targetRole == GUEST;
        }
        
        return false;
    }
    
    /**
     * Zkontroluje, jestli může tato role degradovat cílovou roli
     */
    public boolean canDemote(IslandRole targetRole) {
        if (!canDemote()) {
            return false;
        }
        
        // Vlastník může degradovat kohokoliv (kromě sebe)
        if (this == OWNER) {
            return targetRole != OWNER;
        }
        
        // Spoluvlastník může degradovat moderátory a členy
        if (this == CO_OWNER) {
            return targetRole == MODERATOR || targetRole == MEMBER;
        }
        
        return false;
    }
    
    /**
     * Získá další vyšší roli
     */
    public IslandRole getNextHigherRole() {
        switch (this) {
            case GUEST:
                return MEMBER;
            case MEMBER:
                return MODERATOR;
            case MODERATOR:
                return CO_OWNER;
            case CO_OWNER:
                return OWNER;
            case OWNER:
                return OWNER; // Vlastník je nejvyšší
            default:
                return this;
        }
    }
    
    /**
     * Získá další nižší roli
     */
    public IslandRole getNextLowerRole() {
        switch (this) {
            case OWNER:
                return CO_OWNER;
            case CO_OWNER:
                return MODERATOR;
            case MODERATOR:
                return MEMBER;
            case MEMBER:
                return GUEST;
            case GUEST:
                return GUEST; // Host je nejnižší
            default:
                return this;
        }
    }
    
    /**
     * Získá pořadí role (vyšší číslo = vyšší role)
     */
    public int getOrder() {
        switch (this) {
            case GUEST:
                return 1;
            case MEMBER:
                return 2;
            case MODERATOR:
                return 3;
            case CO_OWNER:
                return 4;
            case OWNER:
                return 5;
            default:
                return 0;
        }
    }
    
    /**
     * Porovná dvě role podle jejich pořadí
     */
    public boolean isHigherThan(IslandRole other) {
        return this.getOrder() > other.getOrder();
    }
    
    public boolean isLowerThan(IslandRole other) {
        return this.getOrder() < other.getOrder();
    }
    
    public boolean isEqualTo(IslandRole other) {
        return this.getOrder() == other.getOrder();
    }
}
