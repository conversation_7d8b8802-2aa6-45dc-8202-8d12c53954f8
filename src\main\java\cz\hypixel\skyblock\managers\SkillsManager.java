package cz.hypixel.skyblock.managers;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.database.PlayerData;
import cz.hypixel.skyblock.data.SkillData;
import cz.hypixel.skyblock.data.SkillType;
import org.bukkit.Material;
import org.bukkit.entity.Player;

import java.util.*;
import java.util.logging.Logger;

/**
 * Manager pro správu systému skillů podle Hypixel SkyBlock
 */
public class SkillsManager {

    private final HypixelSkyBlockCZ plugin;
    private final Logger logger;
    private final Map<SkillType, SkillData> skills;
    private final Map<SkillType, double[]> skillXpTable;

    public SkillsManager(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.skills = new HashMap<>();
        this.skillXpTable = new HashMap<>();
        
        initializeSkills();
        initializeXpTables();
        logger.info("SkillsManager inicializován s " + skills.size() + " skills");
    }

    /**
     * Inicializuje všechny skills podle Hypixel SkyBlock
     */
    private void initializeSkills() {
        // Combat Skill
        skills.put(SkillType.COMBAT, new SkillData(
            SkillType.COMBAT,
            "Combat",
            "Bojuj s moby a speciálními bossy pro získání Combat XP!",
            Material.STONE_SWORD,
            "Crit Chance",
            "Warrior - Způsobuj 4-210% více damage mobům",
            50
        ));

        // Mining Skill
        skills.put(SkillType.MINING, new SkillData(
            SkillType.MINING,
            "Mining",
            "Ponořte se do hlubokých jeskyní a najděte vzácné rudy!",
            Material.STONE_PICKAXE,
            "Defense",
            "Spelunker - +4-240 Mining Fortune pro více ore dropů",
            50
        ));

        // Farming Skill
        skills.put(SkillType.FARMING, new SkillData(
            SkillType.FARMING,
            "Farming",
            "Sklízejte plodiny a stříhejte ovce pro Farming XP!",
            Material.GOLDEN_HOE,
            "Health",
            "Farmhand - +4-240 Farming Fortune pro více cropů",
            50
        ));

        // Foraging Skill
        skills.put(SkillType.FORAGING, new SkillData(
            SkillType.FORAGING,
            "Foraging",
            "Kácejte stromy a sbírejte další rostliny pro Foraging XP!",
            Material.JUNGLE_SAPLING,
            "Strength",
            "Logger - +4-200 Foraging Fortune pro více logů",
            50
        ));

        // Fishing Skill
        skills.put(SkillType.FISHING, new SkillData(
            SkillType.FISHING,
            "Fishing",
            "Navštivte místní rybník pro rybaření a Fishing XP!",
            Material.FISHING_ROD,
            "Health",
            "Treasure Hunter - +0.5-5 Treasure Chance",
            50
        ));

        // Enchanting Skill
        skills.put(SkillType.ENCHANTING, new SkillData(
            SkillType.ENCHANTING,
            "Enchanting",
            "Enchantujte předměty pro získání Enchanting XP!",
            Material.ENCHANTING_TABLE,
            "Intelligence + Ability Damage",
            "Conjurer - Získávejte 5-300% více experience orbů",
            50
        ));

        // Alchemy Skill
        skills.put(SkillType.ALCHEMY, new SkillData(
            SkillType.ALCHEMY,
            "Alchemy",
            "Vařte lektvary pro získání Alchemy XP!",
            Material.BREWING_STAND,
            "Intelligence",
            "Brewer - Lektvary trvají 1-50% déle",
            50
        ));

        // Taming Skill
        skills.put(SkillType.TAMING, new SkillData(
            SkillType.TAMING,
            "Taming",
            "Levelujte pety pro získání Taming XP!",
            Material.GHAST_SPAWN_EGG,
            "Pet Luck",
            "Zoologist - Získávejte 1-60% více pet XP",
            50
        ));

        // Dungeoneering Skill
        skills.put(SkillType.DUNGEONEERING, new SkillData(
            SkillType.DUNGEONEERING,
            "Dungeoneering",
            "Dokončujte Dungeony pro levelování tříd!",
            Material.WITHER_SKELETON_SKULL,
            "Health",
            "Zvyšuje základní stats Dungeon předmětů o 10-475%",
            50
        ));

        // Carpentry Skill (Cosmetic)
        skills.put(SkillType.CARPENTRY, new SkillData(
            SkillType.CARPENTRY,
            "Carpentry",
            "Craftujte předměty pro získání Carpentry XP!",
            Material.CRAFTING_TABLE,
            "Health",
            "Žádné",
            50
        ));

        // Social Skill (Cosmetic)
        skills.put(SkillType.SOCIAL, new SkillData(
            SkillType.SOCIAL,
            "Social",
            "Získávejte Social XP za návštěvy ostrovů!",
            Material.EMERALD,
            "Žádné",
            "Žádné",
            25
        ));

        // Runecrafting Skill (Cosmetic)
        skills.put(SkillType.RUNECRAFTING, new SkillData(
            SkillType.RUNECRAFTING,
            "Runecrafting",
            "Zabíjejte bossy a kombinujte runy pro Runecrafting XP!",
            Material.MAGMA_CREAM,
            "Žádné",
            "Žádné",
            25
        ));
    }

    /**
     * Inicializuje XP tabulky pro všechny skills
     */
    private void initializeXpTables() {
        // Standardní XP tabulka pro většinu skillů (level 0-50)
        double[] standardXp = new double[51];
        standardXp[0] = 0;
        standardXp[1] = 50;
        standardXp[2] = 175;
        standardXp[3] = 375;
        standardXp[4] = 675;
        standardXp[5] = 1175;
        standardXp[6] = 1925;
        standardXp[7] = 2925;
        standardXp[8] = 4425;
        standardXp[9] = 6425;
        standardXp[10] = 9925;
        standardXp[11] = 14925;
        standardXp[12] = 22425;
        standardXp[13] = 32425;
        standardXp[14] = 47425;
        standardXp[15] = 67425;
        standardXp[16] = 97425;
        standardXp[17] = 147425;
        standardXp[18] = 222425;
        standardXp[19] = 322425;
        standardXp[20] = 522425;
        
        // Pokračování pro vyšší levely
        for (int i = 21; i <= 50; i++) {
            standardXp[i] = standardXp[i-1] + (200000 + (i-20) * 50000);
        }

        // Aplikuj standardní tabulku na většinu skillů
        skillXpTable.put(SkillType.COMBAT, standardXp.clone());
        skillXpTable.put(SkillType.MINING, standardXp.clone());
        skillXpTable.put(SkillType.FARMING, standardXp.clone());
        skillXpTable.put(SkillType.FORAGING, standardXp.clone());
        skillXpTable.put(SkillType.FISHING, standardXp.clone());
        skillXpTable.put(SkillType.ENCHANTING, standardXp.clone());
        skillXpTable.put(SkillType.ALCHEMY, standardXp.clone());
        skillXpTable.put(SkillType.TAMING, standardXp.clone());
        skillXpTable.put(SkillType.CARPENTRY, standardXp.clone());

        // Dungeoneering má vlastní tabulku
        double[] dungeoneeringXp = new double[51];
        for (int i = 0; i <= 50; i++) {
            dungeoneeringXp[i] = i * 50000; // Zjednodušená tabulka
        }
        skillXpTable.put(SkillType.DUNGEONEERING, dungeoneeringXp);

        // Cosmetic skills mají nižší požadavky
        double[] cosmeticXp = new double[26];
        for (int i = 0; i <= 25; i++) {
            cosmeticXp[i] = i * 10000; // Jednodušší progrese
        }
        skillXpTable.put(SkillType.SOCIAL, cosmeticXp);
        skillXpTable.put(SkillType.RUNECRAFTING, cosmeticXp);
    }

    /**
     * Přidá XP do skillu hráče
     */
    public boolean addSkillXp(Player player, SkillType skillType, double xp) {
        PlayerData playerData = plugin.getPlayerDataManager().loadPlayerData(player.getUniqueId()).join();
        if (playerData == null) {
            return false;
        }

        PlayerData.SkillData skillData = playerData.getSkill(skillType.name());
        if (skillData == null) {
            skillData = new PlayerData.SkillData(skillType.name(), 0, 0);
            playerData.setSkill(skillType.name(), 0, 0);
        }

        double oldXp = skillData.getExperience();
        int oldLevel = skillData.getLevel();
        
        skillData.addExperience(xp);
        int newLevel = calculateLevel(skillType, skillData.getExperience());
        skillData.setLevel(newLevel);

        // Zkontroluj level up
        if (newLevel > oldLevel) {
            handleLevelUp(player, skillType, oldLevel, newLevel);
        }

        // Uložit data
        plugin.getPlayerDataManager().savePlayerData(playerData);
        return true;
    }

    /**
     * Vypočítá level na základě XP
     */
    public int calculateLevel(SkillType skillType, double xp) {
        double[] xpTable = skillXpTable.get(skillType);
        if (xpTable == null) {
            return 0;
        }

        for (int i = xpTable.length - 1; i >= 0; i--) {
            if (xp >= xpTable[i]) {
                return i;
            }
        }
        return 0;
    }

    /**
     * Získá požadované XP pro level
     */
    public double getXpForLevel(SkillType skillType, int level) {
        double[] xpTable = skillXpTable.get(skillType);
        if (xpTable == null || level < 0 || level >= xpTable.length) {
            return 0;
        }
        return xpTable[level];
    }

    /**
     * Získá XP potřebné pro další level
     */
    public double getXpToNextLevel(SkillType skillType, double currentXp) {
        int currentLevel = calculateLevel(skillType, currentXp);
        double[] xpTable = skillXpTable.get(skillType);
        
        if (xpTable == null || currentLevel >= xpTable.length - 1) {
            return 0; // Max level dosažen
        }
        
        return xpTable[currentLevel + 1] - currentXp;
    }

    /**
     * Zpracuje level up
     */
    private void handleLevelUp(Player player, SkillType skillType, int oldLevel, int newLevel) {
        SkillData skill = skills.get(skillType);
        if (skill == null) {
            return;
        }

        for (int level = oldLevel + 1; level <= newLevel; level++) {
            player.sendMessage("§6§l[SKILL UP] §r§e" + skill.getDisplayName() + " " + level + "!");
            
            // Přidej odměny za level up
            grantLevelRewards(player, skillType, level);
            
            // Zvuk a efekty
            player.playSound(player.getLocation(), "entity.player.levelup", 1.0f, 1.0f);
        }
    }

    /**
     * Udělí odměny za level up
     */
    private void grantLevelRewards(Player player, SkillType skillType, int level) {
        // Základní odměny podle typu skillu
        switch (skillType) {
            case COMBAT:
                // +0.5% Crit Chance per level
                player.sendMessage("§a+ 0.5% Crit Chance");
                break;
            case MINING:
                // +4 Mining Fortune per level
                player.sendMessage("§a+ 4 Mining Fortune");
                break;
            case FARMING:
                // +4 Farming Fortune per level
                player.sendMessage("§a+ 4 Farming Fortune");
                break;
            case FORAGING:
                // +4 Foraging Fortune per level
                player.sendMessage("§a+ 4 Foraging Fortune");
                break;
            case FISHING:
                // +0.1 Treasure Chance per level
                player.sendMessage("§a+ 0.1 Treasure Chance");
                break;
            case ENCHANTING:
                // +1 Intelligence per level
                player.sendMessage("§a+ 1 Intelligence");
                break;
            case ALCHEMY:
                // +1 Intelligence per level
                player.sendMessage("§a+ 1 Intelligence");
                break;
            case TAMING:
                // +1 Pet Luck per level
                player.sendMessage("§a+ 1 Pet Luck");
                break;
            default:
                break;
        }

        // Speciální odměny na určitých levelech
        if (level % 5 == 0) {
            player.sendMessage("§6+ Speciální odměna za level " + level + "!");
        }
    }

    /**
     * Získá skill data
     */
    public SkillData getSkillData(SkillType skillType) {
        return skills.get(skillType);
    }

    /**
     * Získá všechny skills
     */
    public Map<SkillType, SkillData> getAllSkills() {
        return new HashMap<>(skills);
    }

    /**
     * Získá skill level hráče
     */
    public int getPlayerSkillLevel(Player player, SkillType skillType) {
        PlayerData playerData = plugin.getPlayerDataManager().loadPlayerData(player.getUniqueId()).join();
        if (playerData == null) {
            return 0;
        }

        PlayerData.SkillData skillData = playerData.getSkill(skillType.name());
        return skillData != null ? skillData.getLevel() : 0;
    }

    /**
     * Získá skill XP hráče
     */
    public double getPlayerSkillXp(Player player, SkillType skillType) {
        PlayerData playerData = plugin.getPlayerDataManager().loadPlayerData(player.getUniqueId()).join();
        if (playerData == null) {
            return 0;
        }

        PlayerData.SkillData skillData = playerData.getSkill(skillType.name());
        return skillData != null ? skillData.getExperience() : 0;
    }
}
