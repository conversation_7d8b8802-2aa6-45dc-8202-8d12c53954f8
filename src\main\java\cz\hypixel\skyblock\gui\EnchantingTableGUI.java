package cz.hypixel.skyblock.gui;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.SkyBlockEnchantment;
import cz.hypixel.skyblock.managers.BankManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * GUI pro Enchanting Table podle Hypixel SkyBlock
 */
public class EnchantingTableGUI implements Listener {

    private final HypixelSkyBlockCZ plugin;
    private static final String GUI_TITLE = "§5§lEnchanting Table";
    private static final String ENCHANT_TITLE = "§5§lEnchant - ";

    public EnchantingTableGUI(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * Otevře Enchanting Table GUI
     */
    public void openEnchantingTable(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, GUI_TITLE);

        // Slot pro předmět k enchantování (střed)
        setItem(gui, 22, createItemSlotPlaceholder());

        // Informační panel
        setItem(gui, 4, createInfoPanel());

        // Tlačítka
        setItem(gui, 45, createEnchantButton());
        setItem(gui, 46, createRemoveEnchantButton());
        setItem(gui, 47, createViewEnchantsButton());

        setItem(gui, 51, createHelpButton());
        setItem(gui, 53, createCloseButton());

        // Dekorativní předměty
        fillBorders(gui);

        player.openInventory(gui);
    }

    /**
     * Otevře GUI pro výběr enchantmentu
     */
    public void openEnchantSelection(Player player, ItemStack item) {
        if (item == null || item.getType().isAir()) {
            player.sendMessage("§cMusíš vložit předmět k enchantování!");
            return;
        }

        String title = ENCHANT_TITLE + item.getType().name();
        Inventory gui = Bukkit.createInventory(null, 54, title);

        // Zobraz dostupné enchantmenty
        SkyBlockEnchantment[] availableEnchants = SkyBlockEnchantment.getForMaterial(item.getType());
        
        int slot = 10;
        for (SkyBlockEnchantment enchant : availableEnchants) {
            if (slot >= 44) break;
            
            ItemStack enchantItem = createEnchantmentOption(enchant, item);
            gui.setItem(slot, enchantItem);
            
            slot++;
            if (slot == 17) slot = 19;
            if (slot == 26) slot = 28;
            if (slot == 35) slot = 37;
        }

        // Zobraz současné enchantmenty
        setItem(gui, 4, createCurrentEnchantsDisplay(item));

        setItem(gui, 49, createBackButton());
        fillBorders(gui);

        player.openInventory(gui);
    }

    /**
     * Vytvoří placeholder pro slot předmětu
     */
    private ItemStack createItemSlotPlaceholder() {
        ItemStack item = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§7Vlož předmět k enchantování");
        meta.setLore(Arrays.asList("", "§7Přetáhni předmět sem", "§7pro enchantování"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří informační panel
     */
    private ItemStack createInfoPanel() {
        ItemStack item = new ItemStack(Material.ENCHANTED_BOOK);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§5§lEnchanting Table");
        
        List<String> lore = Arrays.asList(
            "",
            "§7Enchantuj své předměty pomocí",
            "§7speciálních SkyBlock enchantmentů!",
            "",
            "§7Potřebuješ:",
            "§7- Mince na zaplacení",
            "§7- Materiály (Lapis, XP bottles, atd.)",
            "",
            "§eVlož předmět a vyber enchantment!"
        );
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Enchant"
     */
    private ItemStack createEnchantButton() {
        ItemStack item = new ItemStack(Material.EXPERIENCE_BOTTLE);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§aEnchant předmět");
        meta.setLore(Arrays.asList("", "§7Klikni pro výběr enchantmentu", "", "§eKlikni pro otevření!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Remove Enchant"
     */
    private ItemStack createRemoveEnchantButton() {
        ItemStack item = new ItemStack(Material.GRINDSTONE);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§cOdeber enchantment");
        meta.setLore(Arrays.asList("", "§7Odeber enchantment z předmětu", "", "§eKlikni pro odebrání!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "View Enchants"
     */
    private ItemStack createViewEnchantsButton() {
        ItemStack item = new ItemStack(Material.SPYGLASS);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§bZobraz enchantmenty");
        meta.setLore(Arrays.asList("", "§7Zobraz všechny enchantmenty", "§7na předmětu", "", "§eKlikni pro zobrazení!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří možnost enchantmentu
     */
    private ItemStack createEnchantmentOption(SkyBlockEnchantment enchant, ItemStack targetItem) {
        ItemStack item = new ItemStack(Material.ENCHANTED_BOOK);
        ItemMeta meta = item.getItemMeta();
        
        // Zjisti současný level
        int currentLevel = plugin.getEnchantingManager().getEnchantmentLevel(targetItem, enchant);
        int nextLevel = currentLevel + 1;
        
        if (nextLevel > enchant.getMaxLevel()) {
            // Už je na max levelu
            meta.setDisplayName("§c" + enchant.getDisplayName() + " (MAX)");
            
            List<String> lore = Arrays.asList(
                "",
                "§7Současný level: " + enchant.getFormattedName(currentLevel),
                "§c§lMAX LEVEL!",
                "",
                "§7" + enchant.getDescription(currentLevel)
            );
            meta.setLore(lore);
            
        } else {
            meta.setDisplayName(enchant.getFormattedName(nextLevel));
            
            List<String> lore = new ArrayList<>();
            lore.add("");
            
            if (currentLevel > 0) {
                lore.add("§7Současný level: " + enchant.getFormattedName(currentLevel));
                lore.add("§7Nový level: " + enchant.getFormattedName(nextLevel));
            } else {
                lore.add("§7Level: " + enchant.getFormattedName(nextLevel));
            }
            
            lore.add("");
            lore.add("§7" + enchant.getDescription(nextLevel));
            lore.add("");
            
            double cost = enchant.getEnchantCost(nextLevel);
            lore.add("§7Cena: §6" + BankManager.formatCoins(cost));
            lore.add("");
            lore.add("§eKlikni pro enchantování!");
            
            meta.setLore(lore);
        }
        
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří display současných enchantmentů
     */
    private ItemStack createCurrentEnchantsDisplay(ItemStack item) {
        ItemStack display = new ItemStack(item.getType());
        ItemMeta meta = display.getItemMeta();
        
        meta.setDisplayName("§eSoučasné enchantmenty");
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        
        Map<SkyBlockEnchantment, Integer> enchants = plugin.getEnchantingManager().getEnchantments(item);
        
        if (enchants.isEmpty()) {
            lore.add("§7Žádné enchantmenty");
        } else {
            for (Map.Entry<SkyBlockEnchantment, Integer> entry : enchants.entrySet()) {
                SkyBlockEnchantment enchant = entry.getKey();
                int level = entry.getValue();
                lore.add("§7- " + enchant.getFormattedName(level));
            }
        }
        
        meta.setLore(lore);
        display.setItemMeta(meta);
        return display;
    }

    /**
     * Vytvoří tlačítko "Help"
     */
    private ItemStack createHelpButton() {
        ItemStack item = new ItemStack(Material.BOOK);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§eNápověda");
        
        List<String> lore = Arrays.asList(
            "",
            "§7Jak enchantovat:",
            "§71. Vlož předmět do slotu",
            "§72. Klikni na 'Enchant předmět'",
            "§73. Vyber enchantment",
            "§74. Potvrď enchantování",
            "",
            "§7Potřebné materiály:",
            "§7- Lapis Lazuli",
            "§7- Experience Bottles",
            "§7- Speciální materiály pro vyšší levely"
        );
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Close"
     */
    private ItemStack createCloseButton() {
        ItemStack item = new ItemStack(Material.BARRIER);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§cZavřít");
        meta.setLore(Arrays.asList("", "§7Klikni pro zavření"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Back"
     */
    private ItemStack createBackButton() {
        ItemStack item = new ItemStack(Material.ARROW);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§cZpět");
        meta.setLore(Arrays.asList("", "§7Klikni pro návrat"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vyplní okraje GUI
     */
    private void fillBorders(Inventory gui) {
        ItemStack border = new ItemStack(Material.PURPLE_STAINED_GLASS_PANE);
        ItemMeta meta = border.getItemMeta();
        meta.setDisplayName(" ");
        border.setItemMeta(meta);

        // Horní a dolní řada
        for (int i = 0; i < 9; i++) {
            if (gui.getItem(i) == null) gui.setItem(i, border);
            if (gui.getItem(i + 45) == null) gui.setItem(i + 45, border);
        }

        // Levý a pravý sloupec
        for (int i = 9; i < 45; i += 9) {
            if (gui.getItem(i) == null) gui.setItem(i, border);
            if (gui.getItem(i + 8) == null) gui.setItem(i + 8, border);
        }
    }

    /**
     * Nastaví item na slot
     */
    private void setItem(Inventory gui, int slot, ItemStack item) {
        gui.setItem(slot, item);
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        
        String title = event.getView().getTitle();
        if (!title.startsWith("§5§lEnchanting Table") && !title.startsWith("§5§lEnchant - ")) return;
        
        event.setCancelled(true);
        
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || clicked.getType() == Material.AIR) return;
        
        ItemMeta meta = clicked.getItemMeta();
        if (meta == null || meta.getDisplayName() == null) return;
        
        String displayName = meta.getDisplayName();
        
        // Hlavní Enchanting Table menu
        if (title.equals(GUI_TITLE)) {
            handleMainMenuClick(player, displayName, event);
        }
        // Enchant selection menu
        else if (title.startsWith(ENCHANT_TITLE)) {
            handleEnchantSelectionClick(player, displayName, title);
        }
    }

    /**
     * Zpracuje klik v hlavním menu
     */
    private void handleMainMenuClick(Player player, String displayName, InventoryClickEvent event) {
        if (displayName.equals("§aEnchant předmět")) {
            // Zkontroluj, jestli je vložen předmět
            ItemStack item = event.getInventory().getItem(22);
            if (item != null && !item.getType().isAir() && !item.getType().name().contains("GLASS")) {
                openEnchantSelection(player, item);
            } else {
                player.sendMessage("§cMusíš nejdříve vložit předmět k enchantování!");
            }
        } else if (displayName.equals("§cOdeber enchantment")) {
            player.sendMessage("§eOdebrání enchantmentů bude implementováno později!");
        } else if (displayName.equals("§bZobraz enchantmenty")) {
            ItemStack item = event.getInventory().getItem(22);
            if (item != null && !item.getType().isAir() && !item.getType().name().contains("GLASS")) {
                showItemEnchantments(player, item);
            } else {
                player.sendMessage("§cMusíš nejdříve vložit předmět!");
            }
        } else if (displayName.equals("§eNápověda")) {
            // Nápověda už je zobrazena v lore
        } else if (displayName.equals("§cZavřít")) {
            player.closeInventory();
        }
    }

    /**
     * Zpracuje klik v menu výběru enchantmentu
     */
    private void handleEnchantSelectionClick(Player player, String displayName, String title) {
        if (displayName.equals("§cZpět")) {
            openEnchantingTable(player);
        } else if (displayName.startsWith("§c") && displayName.contains("MAX")) {
            player.sendMessage("§cTento enchantment je už na maximálním levelu!");
        } else {
            // TODO: Implementovat enchantování
            player.sendMessage("§eEnchantování bude implementováno později!");
        }
    }

    /**
     * Zobrazí enchantmenty na předmětu
     */
    private void showItemEnchantments(Player player, ItemStack item) {
        Map<SkyBlockEnchantment, Integer> enchants = plugin.getEnchantingManager().getEnchantments(item);
        
        player.sendMessage("§5§l=== ENCHANTMENTY ===");
        player.sendMessage("§7Předmět: §e" + item.getType().name());
        
        if (enchants.isEmpty()) {
            player.sendMessage("§7Žádné enchantmenty");
        } else {
            for (Map.Entry<SkyBlockEnchantment, Integer> entry : enchants.entrySet()) {
                SkyBlockEnchantment enchant = entry.getKey();
                int level = entry.getValue();
                player.sendMessage("§7- " + enchant.getFormattedName(level) + " §7- " + enchant.getDescription(level));
            }
        }
    }
}
