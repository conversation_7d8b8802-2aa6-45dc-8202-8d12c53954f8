package cz.hypixel.skyblock.gui;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.BazaarOrder;
import cz.hypixel.skyblock.data.BazaarOrderType;
import cz.hypixel.skyblock.managers.BankManager;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;

/**
 * GUI pro Bazaar podle Hypixel SkyBlock
 */
public class BazaarGUI implements Listener {

    private final HypixelSkyBlockCZ plugin;
    private static final String GUI_TITLE = "§6§lBazaar";
    private static final String ITEM_TITLE = "§6§lBazaar - ";

    public BazaarGUI(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * Otevře hlavní Bazaar menu
     */
    public void openBazaar(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, GUI_TITLE);

        Set<String> bazaarItems = plugin.getBazaarManager().getBazaarItems();
        List<String> itemList = new ArrayList<>(bazaarItems);
        
        // Zobraz první 28 předmětů
        int slot = 10;
        for (int i = 0; i < Math.min(itemList.size(), 28); i++) {
            String itemName = itemList.get(i);
            ItemStack item = createBazaarItemDisplay(itemName);
            gui.setItem(slot, item);
            
            slot++;
            if (slot == 17) slot = 19;
            if (slot == 26) slot = 28;
            if (slot == 35) slot = 37;
            if (slot >= 44) break;
        }

        // Speciální tlačítka
        setItem(gui, 48, createMyOrdersButton());
        setItem(gui, 49, createSearchButton());
        setItem(gui, 50, createManageButton());

        // Dekorativní předměty
        fillBorders(gui);

        player.openInventory(gui);
    }

    /**
     * Otevře GUI pro konkrétní předmět
     */
    public void openItemMenu(Player player, String itemName) {
        String title = ITEM_TITLE + getItemDisplayName(itemName);
        Inventory gui = Bukkit.createInventory(null, 54, title);

        // Načti ordery pro předmět
        plugin.getBazaarManager().getBuyOrders(itemName).thenAccept(buyOrders -> {
            plugin.getBazaarManager().getSellOrders(itemName).thenAccept(sellOrders -> {
                Bukkit.getScheduler().runTask(plugin, () -> {
                    // Informace o předmětu
                    setItem(gui, 4, createItemInfoDisplay(itemName, buyOrders, sellOrders));
                    
                    // Buy ordery (levá strana)
                    setItem(gui, 19, createOrderTypeHeader(BazaarOrderType.BUY));
                    int buySlot = 28;
                    for (int i = 0; i < Math.min(buyOrders.size(), 7); i++) {
                        setItem(gui, buySlot, createOrderDisplay(buyOrders.get(i)));
                        buySlot++;
                    }
                    
                    // Sell ordery (pravá strana)
                    setItem(gui, 25, createOrderTypeHeader(BazaarOrderType.SELL));
                    int sellSlot = 34;
                    for (int i = 0; i < Math.min(sellOrders.size(), 7); i++) {
                        setItem(gui, sellSlot, createOrderDisplay(sellOrders.get(i)));
                        sellSlot++;
                    }
                    
                    // Akční tlačítka
                    setItem(gui, 45, createInstantBuyButton(itemName));
                    setItem(gui, 46, createCreateBuyOrderButton(itemName));
                    setItem(gui, 47, createCreateSellOrderButton(itemName));
                    setItem(gui, 48, createInstantSellButton(itemName));
                    
                    setItem(gui, 49, createBackButton());
                    
                    fillBorders(gui);
                    
                    // Znovu otevři GUI s načtenými daty
                    player.closeInventory();
                    player.openInventory(gui);
                });
            });
        });

        // Dočasně otevři prázdné GUI
        fillBorders(gui);
        setItem(gui, 22, createLoadingItem());
        player.openInventory(gui);
    }

    /**
     * Vytvoří display pro Bazaar předmět
     */
    private ItemStack createBazaarItemDisplay(String itemName) {
        Material material = getMaterialFromName(itemName);
        ItemStack item = new ItemStack(material != null ? material : Material.BARRIER);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName("§e" + getItemDisplayName(itemName));
        
        List<String> lore = Arrays.asList(
            "",
            "§7Klikni pro zobrazení",
            "§7buy/sell orderů!",
            "",
            "§eKlikni pro otevření!"
        );
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }

    /**
     * Vytvoří informační display pro předmět
     */
    private ItemStack createItemInfoDisplay(String itemName, List<BazaarOrder> buyOrders, List<BazaarOrder> sellOrders) {
        Material material = getMaterialFromName(itemName);
        ItemStack item = new ItemStack(material != null ? material : Material.BARRIER);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName("§e" + getItemDisplayName(itemName));
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        
        // Nejlepší ceny
        if (!buyOrders.isEmpty()) {
            double bestBuyPrice = buyOrders.stream()
                .filter(BazaarOrder::isActive)
                .mapToDouble(BazaarOrder::getPricePerUnit)
                .max().orElse(0);
            lore.add("§7Nejlepší buy cena: §a" + BankManager.formatCoins(bestBuyPrice));
        } else {
            lore.add("§7Nejlepší buy cena: §cŽádné ordery");
        }
        
        if (!sellOrders.isEmpty()) {
            double bestSellPrice = sellOrders.stream()
                .filter(BazaarOrder::isActive)
                .mapToDouble(BazaarOrder::getPricePerUnit)
                .min().orElse(0);
            lore.add("§7Nejlepší sell cena: §c" + BankManager.formatCoins(bestSellPrice));
        } else {
            lore.add("§7Nejlepší sell cena: §cŽádné ordery");
        }
        
        lore.add("");
        lore.add("§7Buy orderů: §e" + buyOrders.size());
        lore.add("§7Sell orderů: §e" + sellOrders.size());
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }

    /**
     * Vytvoří header pro typ orderu
     */
    private ItemStack createOrderTypeHeader(BazaarOrderType orderType) {
        Material material = orderType == BazaarOrderType.BUY ? Material.EMERALD : Material.REDSTONE;
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName(orderType.getColoredName() + " Ordery");
        meta.setLore(Arrays.asList("", "§7" + orderType.getDescription()));
        item.setItemMeta(meta);
        
        return item;
    }

    /**
     * Vytvoří display pro order
     */
    private ItemStack createOrderDisplay(BazaarOrder order) {
        Material material = order.getOrderType() == BazaarOrderType.BUY ? Material.EMERALD_BLOCK : Material.REDSTONE_BLOCK;
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName(order.getOrderType().getColoredName() + " Order");
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Množství: §e" + order.getRemainingAmount() + "§7/§e" + order.getAmount());
        lore.add("§7Cena za kus: §e" + BankManager.formatCoins(order.getPricePerUnit()));
        lore.add("§7Celková cena: §e" + BankManager.formatCoins(order.getRemainingPrice()));
        lore.add("");
        lore.add("§7Progress: " + order.getProgressFormatted());
        lore.add("§7Status: " + order.getStatus().getColoredName());
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }

    /**
     * Vytvoří tlačítko "Instant Buy"
     */
    private ItemStack createInstantBuyButton(String itemName) {
        ItemStack item = new ItemStack(Material.EMERALD);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§aInstant Buy");
        meta.setLore(Arrays.asList("", "§7Koup okamžitě za nejlepší cenu", "", "§eKlikni pro koupi!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Instant Sell"
     */
    private ItemStack createInstantSellButton(String itemName) {
        ItemStack item = new ItemStack(Material.REDSTONE);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§cInstant Sell");
        meta.setLore(Arrays.asList("", "§7Prodej okamžitě za nejlepší cenu", "", "§eKlikni pro prodej!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Create Buy Order"
     */
    private ItemStack createCreateBuyOrderButton(String itemName) {
        ItemStack item = new ItemStack(Material.LIME_DYE);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§aVytvořit Buy Order");
        meta.setLore(Arrays.asList("", "§7Vytvoř buy order za svou cenu", "", "§eKlikni pro vytvoření!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Create Sell Order"
     */
    private ItemStack createCreateSellOrderButton(String itemName) {
        ItemStack item = new ItemStack(Material.RED_DYE);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§cVytvořit Sell Order");
        meta.setLore(Arrays.asList("", "§7Vytvoř sell order za svou cenu", "", "§eKlikni pro vytvoření!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Moje ordery"
     */
    private ItemStack createMyOrdersButton() {
        ItemStack item = new ItemStack(Material.PLAYER_HEAD);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§aMoje ordery");
        meta.setLore(Arrays.asList("", "§7Zobraz své aktivní ordery", "", "§eKlikni pro otevření!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Hledat"
     */
    private ItemStack createSearchButton() {
        ItemStack item = new ItemStack(Material.COMPASS);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§bHledat");
        meta.setLore(Arrays.asList("", "§7Hledej konkrétní předměty", "", "§eKlikni pro hledání!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Správa"
     */
    private ItemStack createManageButton() {
        ItemStack item = new ItemStack(Material.WRITABLE_BOOK);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§6Správa orderů");
        meta.setLore(Arrays.asList("", "§7Spravuj své ordery", "", "§eKlikni pro otevření!"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří tlačítko "Zpět"
     */
    private ItemStack createBackButton() {
        ItemStack item = new ItemStack(Material.ARROW);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§cZpět");
        meta.setLore(Arrays.asList("", "§7Klikni pro návrat"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vytvoří loading item
     */
    private ItemStack createLoadingItem() {
        ItemStack item = new ItemStack(Material.CLOCK);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§eNačítání...");
        meta.setLore(Arrays.asList("", "§7Načítají se ordery..."));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vyplní okraje GUI
     */
    private void fillBorders(Inventory gui) {
        ItemStack border = new ItemStack(Material.ORANGE_STAINED_GLASS_PANE);
        ItemMeta meta = border.getItemMeta();
        meta.setDisplayName(" ");
        border.setItemMeta(meta);

        // Horní a dolní řada
        for (int i = 0; i < 9; i++) {
            if (gui.getItem(i) == null) gui.setItem(i, border);
            if (gui.getItem(i + 45) == null) gui.setItem(i + 45, border);
        }

        // Levý a pravý sloupec
        for (int i = 9; i < 45; i += 9) {
            if (gui.getItem(i) == null) gui.setItem(i, border);
            if (gui.getItem(i + 8) == null) gui.setItem(i + 8, border);
        }
    }

    /**
     * Nastaví item na slot
     */
    private void setItem(Inventory gui, int slot, ItemStack item) {
        gui.setItem(slot, item);
    }

    /**
     * Převede název předmětu na Material
     */
    private Material getMaterialFromName(String itemName) {
        try {
            return Material.valueOf(itemName.toUpperCase());
        } catch (IllegalArgumentException e) {
            if (itemName.startsWith("ENCHANTED_")) {
                String baseName = itemName.substring(10);
                try {
                    return Material.valueOf(baseName);
                } catch (IllegalArgumentException ex) {
                    return Material.BARRIER;
                }
            }
            return Material.BARRIER;
        }
    }

    /**
     * Získá display název předmětu
     */
    private String getItemDisplayName(String itemName) {
        if (itemName.startsWith("ENCHANTED_")) {
            return "Enchanted " + itemName.substring(10).toLowerCase().replace("_", " ");
        }
        return itemName.toLowerCase().replace("_", " ");
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        
        String title = event.getView().getTitle();
        if (!title.startsWith("§6§lBazaar")) return;
        
        event.setCancelled(true);
        
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || clicked.getType() == Material.AIR) return;
        
        ItemMeta meta = clicked.getItemMeta();
        if (meta == null || meta.getDisplayName() == null) return;
        
        String displayName = meta.getDisplayName();
        
        // Hlavní menu
        if (title.equals(GUI_TITLE)) {
            handleMainMenuClick(player, displayName);
        }
        // Předmět menu
        else if (title.startsWith(ITEM_TITLE)) {
            handleItemMenuClick(player, displayName, title);
        }
    }

    /**
     * Zpracuje klik v hlavním menu
     */
    private void handleMainMenuClick(Player player, String displayName) {
        if (displayName.equals("§aMoje ordery")) {
            player.sendMessage("§eMoje ordery budou implementovány později!");
        } else if (displayName.equals("§bHledat")) {
            player.sendMessage("§eVyhledávání bude implementováno později!");
        } else if (displayName.equals("§6Správa orderů")) {
            player.sendMessage("§eSpráva orderů bude implementována později!");
        } else {
            // Klik na předmět
            String itemName = displayName.substring(2).toUpperCase().replace(" ", "_");
            if (itemName.startsWith("ENCHANTED ")) {
                itemName = "ENCHANTED_" + itemName.substring(10);
            }
            openItemMenu(player, itemName);
        }
    }

    /**
     * Zpracuje klik v menu předmětu
     */
    private void handleItemMenuClick(Player player, String displayName, String title) {
        String itemName = title.substring(ITEM_TITLE.length()).toUpperCase().replace(" ", "_");
        if (itemName.startsWith("ENCHANTED ")) {
            itemName = "ENCHANTED_" + itemName.substring(10);
        }
        
        if (displayName.equals("§cZpět")) {
            openBazaar(player);
        } else if (displayName.equals("§aInstant Buy")) {
            player.sendMessage("§eInstant buy bude implementován později!");
        } else if (displayName.equals("§cInstant Sell")) {
            player.sendMessage("§eInstant sell bude implementován později!");
        } else if (displayName.equals("§aVytvořit Buy Order")) {
            player.sendMessage("§eVytvoření buy orderu bude implementováno později!");
        } else if (displayName.equals("§cVytvořit Sell Order")) {
            player.sendMessage("§eVytvoření sell orderu bude implementováno později!");
        }
    }
}
