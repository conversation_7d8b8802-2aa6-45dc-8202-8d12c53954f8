package cz.hypixel.skyblock.managers;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.IslandData;
import cz.hypixel.skyblock.data.IslandRole;
import org.bukkit.*;
import org.bukkit.entity.Player;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;

/**
 * Manager pro správu ostrovů podle Hypixel SkyBlock
 */
public class IslandManager {

    private final HypixelSkyBlockCZ plugin;
    private final Logger logger;
    private final DataSource dataSource;
    private final Map<UUID, IslandData> islandCache;
    private final Map<Integer, IslandData> islandIdCache;

    public IslandManager(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.dataSource = plugin.getPlayerDataManager().getDataSource();
        this.islandCache = new HashMap<>();
        this.islandIdCache = new HashMap<>();

        logger.info("IslandManager inicializován");
    }

    /**
     * Vytvoří nový ostrov pro hráče
     */
    public CompletableFuture<Boolean> createIsland(Player player) {
        return CompletableFuture.supplyAsync(() -> {
            UUID playerId = player.getUniqueId();

            // Zkontroluj, jestli hráč už nemá ostrov
            if (hasIsland(player).join()) {
                return false;
            }

            // Najdi volné místo pro ostrov
            Location islandLocation = findFreeIslandLocation();

            // Vytvoř ostrov v databázi
            IslandData islandData = new IslandData(playerId, islandLocation.getWorld().getName(), islandLocation);

            String sql = """
                INSERT INTO islands (owner_uuid, world_name, spawn_x, spawn_y, spawn_z, size, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            """;

            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {

                stmt.setString(1, playerId.toString());
                stmt.setString(2, islandData.getWorldName());
                stmt.setDouble(3, islandData.getSpawnX());
                stmt.setDouble(4, islandData.getSpawnY());
                stmt.setDouble(5, islandData.getSpawnZ());
                stmt.setInt(6, islandData.getSize());
                stmt.setLong(7, islandData.getCreatedAt());
                stmt.setLong(8, islandData.getUpdatedAt());

                int result = stmt.executeUpdate();

                if (result > 0) {
                    // Získej vygenerované ID
                    try (ResultSet rs = stmt.getGeneratedKeys()) {
                        if (rs.next()) {
                            islandData.setId(rs.getInt(1));
                        }
                    }

                    // Přidej do cache
                    islandCache.put(playerId, islandData);
                    islandIdCache.put(islandData.getId(), islandData);

                    // Vygeneruj ostrov ve světě
                    generateIslandStructure(islandLocation);

                    logger.info("Vytvořen ostrov pro hráče: " + player.getName() + " (ID: " + islandData.getId() + ")");
                    return true;
                }

            } catch (SQLException e) {
                logger.severe("Chyba při vytváření ostrova: " + e.getMessage());
            }

            return false;
        });
    }

    /**
     * Smaže ostrov hráče
     */
    public CompletableFuture<Boolean> deleteIsland(Player player) {
        return CompletableFuture.supplyAsync(() -> {
            UUID playerId = player.getUniqueId();

            IslandData island = islandCache.get(playerId);
            if (island == null) {
                island = loadIslandByOwner(playerId).join();
                if (island == null) {
                    return false; // Hráč nemá ostrov
                }
            }

            String sql = "DELETE FROM islands WHERE owner_uuid = ?";

            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {

                stmt.setString(1, playerId.toString());
                int result = stmt.executeUpdate();

                if (result > 0) {
                    // Odstraň z cache
                    islandCache.remove(playerId);
                    islandIdCache.remove(island.getId());

                    logger.info("Smazán ostrov hráče: " + player.getName() + " (ID: " + island.getId() + ")");
                    return true;
                }

            } catch (SQLException e) {
                logger.severe("Chyba při mazání ostrova: " + e.getMessage());
            }

            return false;
        });
    }

    /**
     * Teleportuje hráče na jeho ostrov
     */
    public CompletableFuture<Boolean> teleportToIsland(Player player) {
        return getIslandByOwner(player.getUniqueId()).thenApply(island -> {
            if (island == null) {
                return false;
            }

            Location spawnLocation = island.getSpawnLocation();
            if (spawnLocation.getWorld() == null) {
                logger.warning("Svět ostrova " + island.getWorldName() + " neexistuje!");
                return false;
            }

            player.teleport(spawnLocation);
            return true;
        });
    }

    /**
     * Zkontroluje, jestli hráč má ostrov
     */
    public CompletableFuture<Boolean> hasIsland(Player player) {
        return getIslandByOwner(player.getUniqueId()).thenApply(island -> island != null);
    }

    /**
     * Získá ostrov podle vlastníka
     */
    public CompletableFuture<IslandData> getIslandByOwner(UUID ownerUuid) {
        // Zkontroluj cache
        IslandData cached = islandCache.get(ownerUuid);
        if (cached != null) {
            return CompletableFuture.completedFuture(cached);
        }

        return loadIslandByOwner(ownerUuid);
    }

    /**
     * Načte ostrov z databáze podle vlastníka
     */
    public CompletableFuture<IslandData> loadIslandByOwner(UUID ownerUuid) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = """
                SELECT id, owner_uuid, island_name, world_name, spawn_x, spawn_y, spawn_z,
                       size, created_at, updated_at
                FROM islands WHERE owner_uuid = ?
            """;

            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {

                stmt.setString(1, ownerUuid.toString());

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        IslandData island = new IslandData();
                        island.setId(rs.getInt("id"));
                        island.setOwnerUuid(UUID.fromString(rs.getString("owner_uuid")));
                        island.setIslandName(rs.getString("island_name"));
                        island.setWorldName(rs.getString("world_name"));
                        island.setSpawnX(rs.getDouble("spawn_x"));
                        island.setSpawnY(rs.getDouble("spawn_y"));
                        island.setSpawnZ(rs.getDouble("spawn_z"));
                        island.setSize(rs.getInt("size"));
                        island.setCreatedAt(rs.getLong("created_at"));
                        island.setUpdatedAt(rs.getLong("updated_at"));

                        // Načti členy ostrova
                        loadIslandMembers(island);

                        // Přidej do cache
                        islandCache.put(ownerUuid, island);
                        islandIdCache.put(island.getId(), island);

                        return island;
                    }
                }

            } catch (SQLException e) {
                logger.severe("Chyba při načítání ostrova: " + e.getMessage());
            }

            return null;
        });
    }

    /**
     * Načte členy ostrova z databáze
     */
    private void loadIslandMembers(IslandData island) {
        String sql = "SELECT player_uuid, role, joined_at FROM island_members WHERE island_id = ?";

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setInt(1, island.getId());

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    UUID playerUuid = UUID.fromString(rs.getString("player_uuid"));
                    IslandRole role = IslandRole.fromString(rs.getString("role"));
                    long joinedAt = rs.getLong("joined_at");

                    if (role != null) {
                        island.addMember(playerUuid, role);
                    }
                }
            }

        } catch (SQLException e) {
            logger.warning("Chyba při načítání členů ostrova: " + e.getMessage());
        }
    }

    /**
     * Najde volné místo pro nový ostrov
     */
    private Location findFreeIslandLocation() {
        World world = Bukkit.getWorld("skyblock");
        if (world == null) {
            // Vytvoř SkyBlock svět, pokud neexistuje
            world = Bukkit.createWorld(new WorldCreator("skyblock").type(WorldType.FLAT));
        }

        // Jednoduchý algoritmus pro hledání volného místa
        // V produkci by měl být sofistikovanější
        int islandSpacing = 1000;
        int currentIslands = islandCache.size();

        int x = (currentIslands % 10) * islandSpacing;
        int z = (currentIslands / 10) * islandSpacing;

        return new Location(world, x, 100, z);
    }

    /**
     * Vygeneruje základní strukturu ostrova
     */
    private void generateIslandStructure(Location center) {
        World world = center.getWorld();
        if (world == null) return;

        // Základní ostrov 7x7 z hlíny
        for (int x = -3; x <= 3; x++) {
            for (int z = -3; z <= 3; z++) {
                for (int y = -2; y <= 0; y++) {
                    Location loc = center.clone().add(x, y, z);
                    if (y == 0) {
                        world.getBlockAt(loc).setType(Material.GRASS_BLOCK);
                    } else {
                        world.getBlockAt(loc).setType(Material.DIRT);
                    }
                }
            }
        }

        // Strom uprostřed
        Location treeLocation = center.clone().add(0, 1, 0);
        world.getBlockAt(treeLocation).setType(Material.OAK_LOG);
        world.getBlockAt(treeLocation.clone().add(0, 1, 0)).setType(Material.OAK_LOG);
        world.getBlockAt(treeLocation.clone().add(0, 2, 0)).setType(Material.OAK_LEAVES);

        // Základní listy kolem
        for (int x = -1; x <= 1; x++) {
            for (int z = -1; z <= 1; z++) {
                if (x != 0 || z != 0) {
                    world.getBlockAt(treeLocation.clone().add(x, 2, z)).setType(Material.OAK_LEAVES);
                }
            }
        }

        // Truhla s počátečními předměty
        Location chestLocation = center.clone().add(2, 1, 0);
        world.getBlockAt(chestLocation).setType(Material.CHEST);

        // TODO: Přidat počáteční předměty do truhly
    }

    /**
     * Přidá člena na ostrov
     */
    public CompletableFuture<Boolean> addMemberToIsland(UUID ownerUuid, UUID memberUuid, IslandRole role) {
        return getIslandByOwner(ownerUuid).thenCompose(island -> {
            if (island == null) {
                return CompletableFuture.completedFuture(false);
            }

            return CompletableFuture.supplyAsync(() -> {
                String sql = "INSERT INTO island_members (island_id, player_uuid, role, joined_at) VALUES (?, ?, ?, ?)";

                try (Connection conn = dataSource.getConnection();
                     PreparedStatement stmt = conn.prepareStatement(sql)) {

                    stmt.setInt(1, island.getId());
                    stmt.setString(2, memberUuid.toString());
                    stmt.setString(3, role.name());
                    stmt.setLong(4, System.currentTimeMillis());

                    int result = stmt.executeUpdate();

                    if (result > 0) {
                        island.addMember(memberUuid, role);
                        return true;
                    }

                } catch (SQLException e) {
                    logger.severe("Chyba při přidávání člena na ostrov: " + e.getMessage());
                }

                return false;
            });
        });
    }

    /**
     * Odstraní člena z ostrova
     */
    public CompletableFuture<Boolean> removeMemberFromIsland(UUID ownerUuid, UUID memberUuid) {
        return getIslandByOwner(ownerUuid).thenCompose(island -> {
            if (island == null) {
                return CompletableFuture.completedFuture(false);
            }

            return CompletableFuture.supplyAsync(() -> {
                String sql = "DELETE FROM island_members WHERE island_id = ? AND player_uuid = ?";

                try (Connection conn = dataSource.getConnection();
                     PreparedStatement stmt = conn.prepareStatement(sql)) {

                    stmt.setInt(1, island.getId());
                    stmt.setString(2, memberUuid.toString());

                    int result = stmt.executeUpdate();

                    if (result > 0) {
                        island.removeMember(memberUuid);
                        return true;
                    }

                } catch (SQLException e) {
                    logger.severe("Chyba při odstraňování člena z ostrova: " + e.getMessage());
                }

                return false;
            });
        });
    }

    /**
     * Uloží všechny ostrovy (pro kompatibilitu)
     */
    public void saveAllIslands() {
        // V této implementaci se data ukládají průběžně
        logger.info("Všechny ostrovy jsou automaticky ukládány do databáze");
    }
}
