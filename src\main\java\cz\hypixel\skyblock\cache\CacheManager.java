package cz.hypixel.skyblock.cache;

import com.google.gson.Gson;
import cz.hypixel.skyblock.HypixelSkyBlockCZ;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;

/**
 * Cache manager pro API data s TTL podporou
 */
public class CacheManager {
    
    private final HypixelSkyBlockCZ plugin;
    private final Logger logger;
    private final Gson gson;
    private final ConcurrentHashMap<String, CacheEntry> cache;
    private final ScheduledExecutorService scheduler;
    private final long defaultTtlMinutes;
    
    public CacheManager(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.gson = new Gson();
        this.cache = new ConcurrentHashMap<>();
        this.scheduler = Executors.newScheduledThreadPool(1);
        this.defaultTtlMinutes = plugin.getConfig().getLong("hypixel_api.cache_ttl", 5);
        
        // Spusť cleanup task každou minutu
        scheduler.scheduleAtFixedRate(this::cleanupExpiredEntries, 1, 1, TimeUnit.MINUTES);
        
        logger.info("Cache manager inicializován s TTL " + defaultTtlMinutes + " minut");
    }
    
    /**
     * Uloží objekt do cache
     */
    public <T> void put(String key, T object) {
        put(key, object, defaultTtlMinutes);
    }
    
    /**
     * Uloží objekt do cache s vlastním TTL
     */
    public <T> void put(String key, T object, long ttlMinutes) {
        if (key == null || object == null) {
            return;
        }
        
        String jsonData = gson.toJson(object);
        long expirationTime = System.currentTimeMillis() + (ttlMinutes * 60 * 1000);
        
        cache.put(key, new CacheEntry(jsonData, expirationTime));
        
        if (plugin.getConfig().getBoolean("plugin.debug", false)) {
            logger.info("Cache uložen: " + key + " (TTL: " + ttlMinutes + " min)");
        }
    }
    
    /**
     * Získá objekt z cache
     */
    public <T> T get(String key, Class<T> clazz) {
        if (key == null || clazz == null) {
            return null;
        }
        
        CacheEntry entry = cache.get(key);
        if (entry == null) {
            return null;
        }
        
        // Zkontroluj expiraci
        if (entry.isExpired()) {
            cache.remove(key);
            if (plugin.getConfig().getBoolean("plugin.debug", false)) {
                logger.info("Cache expirován: " + key);
            }
            return null;
        }
        
        try {
            T object = gson.fromJson(entry.getData(), clazz);
            if (plugin.getConfig().getBoolean("plugin.debug", false)) {
                logger.info("Cache hit: " + key);
            }
            return object;
        } catch (Exception e) {
            logger.warning("Chyba při deserializaci cache: " + key + " - " + e.getMessage());
            cache.remove(key);
            return null;
        }
    }
    
    /**
     * Zkontroluje, jestli klíč existuje v cache a není expirovaný
     */
    public boolean contains(String key) {
        CacheEntry entry = cache.get(key);
        if (entry == null) {
            return false;
        }
        
        if (entry.isExpired()) {
            cache.remove(key);
            return false;
        }
        
        return true;
    }
    
    /**
     * Odstraní klíč z cache
     */
    public void remove(String key) {
        cache.remove(key);
        if (plugin.getConfig().getBoolean("plugin.debug", false)) {
            logger.info("Cache odstraněn: " + key);
        }
    }
    
    /**
     * Vyčistí celý cache
     */
    public void clear() {
        int size = cache.size();
        cache.clear();
        logger.info("Cache vyčištěn (" + size + " položek)");
    }
    
    /**
     * Získá statistiky cache
     */
    public CacheStats getStats() {
        int totalEntries = cache.size();
        int expiredEntries = 0;
        
        long currentTime = System.currentTimeMillis();
        for (CacheEntry entry : cache.values()) {
            if (entry.getExpirationTime() <= currentTime) {
                expiredEntries++;
            }
        }
        
        return new CacheStats(totalEntries, expiredEntries, totalEntries - expiredEntries);
    }
    
    /**
     * Vyčistí expirované položky
     */
    private void cleanupExpiredEntries() {
        long currentTime = System.currentTimeMillis();

        // Použij AtomicInteger pro thread-safe počítání
        java.util.concurrent.atomic.AtomicInteger removedCount = new java.util.concurrent.atomic.AtomicInteger(0);

        cache.entrySet().removeIf(entry -> {
            if (entry.getValue().getExpirationTime() <= currentTime) {
                removedCount.incrementAndGet();
                return true;
            }
            return false;
        });

        int removed = removedCount.get();
        if (removed > 0 && plugin.getConfig().getBoolean("plugin.debug", false)) {
            logger.info("Cache cleanup: odstraněno " + removed + " expirovaných položek");
        }
    }
    
    /**
     * Ukončí cache manager
     */
    public void shutdown() {
        scheduler.shutdown();
        try {
            if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                scheduler.shutdownNow();
            }
        } catch (InterruptedException e) {
            scheduler.shutdownNow();
            Thread.currentThread().interrupt();
        }
        
        clear();
        logger.info("Cache manager ukončen");
    }
    
    /**
     * Cache entry s TTL
     */
    private static class CacheEntry {
        private final String data;
        private final long expirationTime;
        
        public CacheEntry(String data, long expirationTime) {
            this.data = data;
            this.expirationTime = expirationTime;
        }
        
        public String getData() {
            return data;
        }
        
        public long getExpirationTime() {
            return expirationTime;
        }
        
        public boolean isExpired() {
            return System.currentTimeMillis() > expirationTime;
        }
    }
    
    /**
     * Cache statistiky
     */
    public static class CacheStats {
        private final int totalEntries;
        private final int expiredEntries;
        private final int validEntries;
        
        public CacheStats(int totalEntries, int expiredEntries, int validEntries) {
            this.totalEntries = totalEntries;
            this.expiredEntries = expiredEntries;
            this.validEntries = validEntries;
        }
        
        public int getTotalEntries() {
            return totalEntries;
        }
        
        public int getExpiredEntries() {
            return expiredEntries;
        }
        
        public int getValidEntries() {
            return validEntries;
        }
        
        @Override
        public String toString() {
            return String.format("Cache Stats: %d celkem, %d platných, %d expirovaných", 
                    totalEntries, validEntries, expiredEntries);
        }
    }
}
