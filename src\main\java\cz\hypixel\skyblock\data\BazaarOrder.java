package cz.hypixel.skyblock.data;

import java.util.UUID;

/**
 * Data třída pro Bazaar order podle Hypixel SkyBlock
 */
public class BazaarOrder {
    
    private int id;
    private UUID playerUuid;
    private String itemName;
    private BazaarOrderType orderType;
    private int amount;
    private double pricePerUnit;
    private int filledAmount;
    private BazaarOrderStatus status;
    private long createdAt;
    private long updatedAt;
    
    public BazaarOrder() {
        this.status = BazaarOrderStatus.ACTIVE;
        this.filledAmount = 0;
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }
    
    public BazaarOrder(UUID playerUuid, String itemName, BazaarOrderType orderType, 
                      int amount, double pricePerUnit) {
        this();
        this.playerUuid = playerUuid;
        this.itemName = itemName;
        this.orderType = orderType;
        this.amount = amount;
        this.pricePerUnit = pricePerUnit;
    }
    
    // Gettery a settery
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public UUID getPlayerUuid() {
        return playerUuid;
    }
    
    public void setPlayerUuid(UUID playerUuid) {
        this.playerUuid = playerUuid;
    }
    
    public String getItemName() {
        return itemName;
    }
    
    public void setItemName(String itemName) {
        this.itemName = itemName;
    }
    
    public BazaarOrderType getOrderType() {
        return orderType;
    }
    
    public void setOrderType(BazaarOrderType orderType) {
        this.orderType = orderType;
    }
    
    public int getAmount() {
        return amount;
    }
    
    public void setAmount(int amount) {
        this.amount = amount;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public double getPricePerUnit() {
        return pricePerUnit;
    }
    
    public void setPricePerUnit(double pricePerUnit) {
        this.pricePerUnit = pricePerUnit;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public int getFilledAmount() {
        return filledAmount;
    }
    
    public void setFilledAmount(int filledAmount) {
        this.filledAmount = filledAmount;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public BazaarOrderStatus getStatus() {
        return status;
    }
    
    public void setStatus(BazaarOrderStatus status) {
        this.status = status;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public long getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }
    
    public long getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    // Utility metody
    public int getRemainingAmount() {
        return amount - filledAmount;
    }
    
    public double getTotalPrice() {
        return amount * pricePerUnit;
    }
    
    public double getFilledPrice() {
        return filledAmount * pricePerUnit;
    }
    
    public double getRemainingPrice() {
        return getRemainingAmount() * pricePerUnit;
    }
    
    public boolean isCompleted() {
        return filledAmount >= amount;
    }
    
    public boolean isActive() {
        return status == BazaarOrderStatus.ACTIVE && !isCompleted();
    }
    
    public boolean canFill(int requestedAmount) {
        return isActive() && requestedAmount <= getRemainingAmount();
    }
    
    public void fill(int fillAmount) {
        if (!canFill(fillAmount)) {
            throw new IllegalArgumentException("Nelze vyplnit order s " + fillAmount + " předměty");
        }
        
        this.filledAmount += fillAmount;
        this.updatedAt = System.currentTimeMillis();
        
        if (isCompleted()) {
            this.status = BazaarOrderStatus.COMPLETED;
        }
    }
    
    public double getProgress() {
        return amount > 0 ? (double) filledAmount / amount : 0.0;
    }
    
    public String getProgressFormatted() {
        return String.format("%.1f%%", getProgress() * 100);
    }
    
    @Override
    public String toString() {
        return "BazaarOrder{" +
                "id=" + id +
                ", playerUuid=" + playerUuid +
                ", itemName='" + itemName + '\'' +
                ", orderType=" + orderType +
                ", amount=" + amount +
                ", pricePerUnit=" + pricePerUnit +
                ", filledAmount=" + filledAmount +
                ", status=" + status +
                '}';
    }
}
