package cz.hypixel.skyblock.gui;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.database.PlayerData;
import cz.hypixel.skyblock.data.CollectionData;
import cz.hypixel.skyblock.data.CollectionTier;
import org.bukkit.Bukkit;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.Inventory;
import org.bukkit.inventory.ItemStack;
import org.bukkit.inventory.meta.ItemMeta;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;

/**
 * GUI pro zobrazení kolekcí podle Hypixel SkyBlock
 */
public class CollectionsGUI implements Listener {

    private final HypixelSkyBlockCZ plugin;
    private static final String GUI_TITLE = "§6§lKolekce";

    public CollectionsGUI(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        plugin.getServer().getPluginManager().registerEvents(this, plugin);
    }

    /**
     * Otevře hlavní Collections GUI
     */
    public void openCollectionsMenu(Player player) {
        Inventory gui = Bukkit.createInventory(null, 54, GUI_TITLE);

        // Kategorie kolekcí
        setItem(gui, 10, createCategoryItem(Material.GOLDEN_HOE, "§6Farming", "farming", player));
        setItem(gui, 12, createCategoryItem(Material.STONE_PICKAXE, "§8Mining", "mining", player));
        setItem(gui, 14, createCategoryItem(Material.STONE_SWORD, "§cCombat", "combat", player));
        setItem(gui, 16, createCategoryItem(Material.JUNGLE_SAPLING, "§2Foraging", "foraging", player));
        setItem(gui, 28, createCategoryItem(Material.FISHING_ROD, "§3Fishing", "fishing", player));
        setItem(gui, 30, createCategoryItem(Material.WITHER_SKELETON_SKULL, "§5Boss", "boss", player));
        setItem(gui, 32, createCategoryItem(Material.MAGMA_CREAM, "§dRift", "rift", player));

        // Dekorativní předměty
        fillBorders(gui);

        player.openInventory(gui);
    }

    /**
     * Otevře GUI pro konkrétní kategorii kolekcí
     */
    public void openCategoryMenu(Player player, String category) {
        Map<String, CollectionData> collections = plugin.getCollectionsManager().getCollectionsByCategory(category);
        
        if (collections.isEmpty()) {
            player.sendMessage("§cTato kategorie neobsahuje žádné kolekce!");
            return;
        }

        String categoryDisplayName = getCategoryDisplayName(category);
        Inventory gui = Bukkit.createInventory(null, 54, "§6§lKolekce - " + categoryDisplayName);

        PlayerData playerData = plugin.getPlayerDataManager().loadPlayerData(player.getUniqueId()).join();
        if (playerData == null) {
            player.sendMessage("§cChyba při načítání dat hráče!");
            return;
        }

        int slot = 10;
        for (Map.Entry<String, CollectionData> entry : collections.entrySet()) {
            CollectionData collection = entry.getValue();
            long playerAmount = playerData.getCollectionAmount(collection.getName());
            
            ItemStack item = createCollectionItem(collection, playerAmount);
            gui.setItem(slot, item);
            
            slot++;
            if (slot == 17) slot = 19;
            if (slot == 26) slot = 28;
            if (slot == 35) slot = 37;
            if (slot >= 44) break;
        }

        // Zpět tlačítko
        setItem(gui, 49, createBackButton());
        fillBorders(gui);

        player.openInventory(gui);
    }

    /**
     * Vytvoří item pro kategorii kolekcí
     */
    private ItemStack createCategoryItem(Material material, String name, String category, Player player) {
        ItemStack item = new ItemStack(material);
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName(name);
        
        // Spočítej statistiky kategorie
        Map<String, CollectionData> collections = plugin.getCollectionsManager().getCollectionsByCategory(category);
        PlayerData playerData = plugin.getPlayerDataManager().loadPlayerData(player.getUniqueId()).join();
        
        int totalCollections = collections.size();
        int unlockedCollections = 0;
        int totalTiers = 0;
        int unlockedTiers = 0;
        
        if (playerData != null) {
            for (CollectionData collection : collections.values()) {
                long playerAmount = playerData.getCollectionAmount(collection.getName());
                int currentTier = collection.getCurrentTier(playerAmount);
                
                if (currentTier > 0) {
                    unlockedCollections++;
                }
                
                totalTiers += collection.getMaxTier();
                unlockedTiers += currentTier;
            }
        }
        
        List<String> lore = Arrays.asList(
            "",
            "§7Kolekce: §e" + unlockedCollections + "§7/§e" + totalCollections,
            "§7Tier: §e" + unlockedTiers + "§7/§e" + totalTiers,
            "",
            "§eKlikni pro otevření!"
        );
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }

    /**
     * Vytvoří item pro konkrétní kolekci
     */
    private ItemStack createCollectionItem(CollectionData collection, long playerAmount) {
        ItemStack item = new ItemStack(collection.getDisplayMaterial());
        ItemMeta meta = item.getItemMeta();
        
        meta.setDisplayName("§e" + collection.getDisplayName());
        
        List<String> lore = new ArrayList<>();
        lore.add("");
        lore.add("§7Kategorie: " + collection.getCategoryDisplayName());
        lore.add("§7Množství: §e" + formatNumber(playerAmount));
        
        int currentTier = collection.getCurrentTier(playerAmount);
        int maxTier = collection.getMaxTier();
        
        lore.add("§7Tier: §e" + currentTier + "§7/§e" + maxTier);
        
        if (currentTier < maxTier) {
            CollectionTier nextTier = collection.getNextTier(playerAmount);
            if (nextTier != null) {
                double progress = collection.getProgress(playerAmount);
                String progressBar = createProgressBar(progress);
                
                lore.add("");
                lore.add("§7Další tier: §e" + nextTier.getRomanNumeral());
                lore.add("§7Potřeba: §e" + formatNumber(nextTier.getRequiredAmount()));
                lore.add(progressBar);
                
                lore.add("");
                lore.add("§7Odměny za další tier:");
                for (String reward : nextTier.getRewards()) {
                    lore.add("§a+ " + reward);
                }
            }
        } else {
            lore.add("");
            lore.add("§a§lVŠECHNY TIER ODEMČENY!");
        }
        
        lore.add("");
        lore.add("§eKlikni pro zobrazení všech tier!");
        
        meta.setLore(lore);
        item.setItemMeta(meta);
        
        return item;
    }

    /**
     * Vytvoří zpět tlačítko
     */
    private ItemStack createBackButton() {
        ItemStack item = new ItemStack(Material.ARROW);
        ItemMeta meta = item.getItemMeta();
        meta.setDisplayName("§cZpět");
        meta.setLore(Arrays.asList("", "§7Klikni pro návrat"));
        item.setItemMeta(meta);
        return item;
    }

    /**
     * Vyplní okraje GUI dekorativními předměty
     */
    private void fillBorders(Inventory gui) {
        ItemStack border = new ItemStack(Material.GRAY_STAINED_GLASS_PANE);
        ItemMeta meta = border.getItemMeta();
        meta.setDisplayName(" ");
        border.setItemMeta(meta);

        // Horní a dolní řada
        for (int i = 0; i < 9; i++) {
            gui.setItem(i, border);
            gui.setItem(i + 45, border);
        }

        // Levý a pravý sloupec
        for (int i = 9; i < 45; i += 9) {
            gui.setItem(i, border);
            gui.setItem(i + 8, border);
        }
    }

    /**
     * Nastaví item na konkrétní slot
     */
    private void setItem(Inventory gui, int slot, ItemStack item) {
        gui.setItem(slot, item);
    }

    /**
     * Vytvoří progress bar
     */
    private String createProgressBar(double progress) {
        int bars = 20;
        int filled = (int) (progress * bars);
        
        StringBuilder bar = new StringBuilder("§7[");
        for (int i = 0; i < bars; i++) {
            if (i < filled) {
                bar.append("§a■");
            } else {
                bar.append("§7■");
            }
        }
        bar.append("§7] §e").append(String.format("%.1f", progress * 100)).append("%");
        
        return bar.toString();
    }

    /**
     * Formátuje číslo pro zobrazení
     */
    private String formatNumber(long number) {
        if (number >= 1_000_000_000) {
            return String.format("%.1fB", number / 1_000_000_000.0);
        } else if (number >= 1_000_000) {
            return String.format("%.1fM", number / 1_000_000.0);
        } else if (number >= 1_000) {
            return String.format("%.1fk", number / 1_000.0);
        } else {
            return String.valueOf(number);
        }
    }

    /**
     * Získá display název kategorie
     */
    private String getCategoryDisplayName(String category) {
        switch (category.toLowerCase()) {
            case "farming": return "§6Farming";
            case "mining": return "§8Mining";
            case "combat": return "§cCombat";
            case "foraging": return "§2Foraging";
            case "fishing": return "§3Fishing";
            case "boss": return "§5Boss";
            case "rift": return "§dRift";
            default: return "§7" + category;
        }
    }

    @EventHandler
    public void onInventoryClick(InventoryClickEvent event) {
        if (!(event.getWhoClicked() instanceof Player)) return;
        Player player = (Player) event.getWhoClicked();
        
        if (!event.getView().getTitle().startsWith("§6§lKolekce")) return;
        
        event.setCancelled(true);
        
        ItemStack clicked = event.getCurrentItem();
        if (clicked == null || clicked.getType() == Material.AIR) return;
        
        ItemMeta meta = clicked.getItemMeta();
        if (meta == null || meta.getDisplayName() == null) return;
        
        String displayName = meta.getDisplayName();
        
        // Hlavní menu - kategorie
        if (event.getView().getTitle().equals(GUI_TITLE)) {
            if (displayName.contains("Farming")) {
                openCategoryMenu(player, "farming");
            } else if (displayName.contains("Mining")) {
                openCategoryMenu(player, "mining");
            } else if (displayName.contains("Combat")) {
                openCategoryMenu(player, "combat");
            } else if (displayName.contains("Foraging")) {
                openCategoryMenu(player, "foraging");
            } else if (displayName.contains("Fishing")) {
                openCategoryMenu(player, "fishing");
            } else if (displayName.contains("Boss")) {
                openCategoryMenu(player, "boss");
            } else if (displayName.contains("Rift")) {
                openCategoryMenu(player, "rift");
            }
        }
        // Kategorie menu
        else if (event.getView().getTitle().startsWith("§6§lKolekce - ")) {
            if (displayName.equals("§cZpět")) {
                openCollectionsMenu(player);
            }
            // TODO: Otevřít detailní view konkrétní kolekce
        }
    }
}
