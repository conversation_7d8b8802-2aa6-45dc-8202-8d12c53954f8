package cz.hypixel.skyblock.data;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

import java.util.*;

/**
 * Třída pro custom recepty podle Hypixel SkyBlock
 */
public class CustomRecipe {
    
    private String id;
    private String displayName;
    private RecipeType recipeType;
    private CustomItem result;
    private int resultAmount;
    private Map<Integer, RecipeIngredient> ingredients; // Slot -> Ingredient pro shaped recepty
    private List<RecipeIngredient> shapelessIngredients; // Pro shapeless recepty
    private List<String> unlockRequirements;
    private String category;
    private boolean enabled;
    private Map<String, Object> metadata;
    
    public CustomRecipe() {
        this.ingredients = new HashMap<>();
        this.shapelessIngredients = new ArrayList<>();
        this.unlockRequirements = new ArrayList<>();
        this.enabled = true;
        this.metadata = new HashMap<>();
        this.resultAmount = 1;
    }
    
    public CustomRecipe(String id, String displayName, RecipeType recipeType, CustomItem result) {
        this();
        this.id = id;
        this.displayName = displayName;
        this.recipeType = recipeType;
        this.result = result;
    }
    
    // Gettery a settery
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public void setDisplayName(String displayName) {
        this.displayName = displayName;
    }
    
    public RecipeType getRecipeType() {
        return recipeType;
    }
    
    public void setRecipeType(RecipeType recipeType) {
        this.recipeType = recipeType;
    }
    
    public CustomItem getResult() {
        return result;
    }
    
    public void setResult(CustomItem result) {
        this.result = result;
    }
    
    public int getResultAmount() {
        return resultAmount;
    }
    
    public void setResultAmount(int resultAmount) {
        this.resultAmount = resultAmount;
    }
    
    public Map<Integer, RecipeIngredient> getIngredients() {
        return ingredients;
    }
    
    public void setIngredients(Map<Integer, RecipeIngredient> ingredients) {
        this.ingredients = ingredients;
    }
    
    public List<RecipeIngredient> getShapelessIngredients() {
        return shapelessIngredients;
    }
    
    public void setShapelessIngredients(List<RecipeIngredient> shapelessIngredients) {
        this.shapelessIngredients = shapelessIngredients;
    }
    
    public List<String> getUnlockRequirements() {
        return unlockRequirements;
    }
    
    public void setUnlockRequirements(List<String> unlockRequirements) {
        this.unlockRequirements = unlockRequirements;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public boolean isEnabled() {
        return enabled;
    }
    
    public void setEnabled(boolean enabled) {
        this.enabled = enabled;
    }
    
    public Map<String, Object> getMetadata() {
        return metadata;
    }
    
    public void setMetadata(Map<String, Object> metadata) {
        this.metadata = metadata;
    }
    
    // Utility metody pro shaped recepty
    public void setIngredient(int slot, RecipeIngredient ingredient) {
        if (slot >= 0 && slot < 9) {
            ingredients.put(slot, ingredient);
        }
    }
    
    public void setIngredient(int row, int col, RecipeIngredient ingredient) {
        int slot = row * 3 + col;
        setIngredient(slot, ingredient);
    }
    
    public RecipeIngredient getIngredient(int slot) {
        return ingredients.get(slot);
    }
    
    public RecipeIngredient getIngredient(int row, int col) {
        int slot = row * 3 + col;
        return getIngredient(slot);
    }
    
    // Utility metody pro shapeless recepty
    public void addShapelessIngredient(RecipeIngredient ingredient) {
        shapelessIngredients.add(ingredient);
    }
    
    public void addShapelessIngredient(Material material, int amount) {
        shapelessIngredients.add(new RecipeIngredient(material, amount));
    }
    
    public void addShapelessIngredient(String customItemId, int amount) {
        shapelessIngredients.add(new RecipeIngredient(customItemId, amount));
    }
    
    /**
     * Zkontroluje, jestli inventář obsahuje všechny ingredience
     */
    public boolean matches(ItemStack[] craftingGrid) {
        if (recipeType == RecipeType.SHAPED) {
            return matchesShaped(craftingGrid);
        } else {
            return matchesShapeless(craftingGrid);
        }
    }
    
    /**
     * Zkontroluje shaped recept
     */
    private boolean matchesShaped(ItemStack[] craftingGrid) {
        if (craftingGrid.length != 9) return false;
        
        for (int i = 0; i < 9; i++) {
            RecipeIngredient required = ingredients.get(i);
            ItemStack provided = craftingGrid[i];
            
            if (required == null) {
                // Slot by měl být prázdný
                if (provided != null && provided.getType() != Material.AIR) {
                    return false;
                }
            } else {
                // Slot by měl obsahovat specifický ingredient
                if (!required.matches(provided)) {
                    return false;
                }
            }
        }
        
        return true;
    }
    
    /**
     * Zkontroluje shapeless recept
     */
    private boolean matchesShapeless(ItemStack[] craftingGrid) {
        List<ItemStack> providedItems = new ArrayList<>();
        for (ItemStack item : craftingGrid) {
            if (item != null && item.getType() != Material.AIR) {
                providedItems.add(item);
            }
        }
        
        List<RecipeIngredient> requiredIngredients = new ArrayList<>(shapelessIngredients);
        
        for (ItemStack provided : providedItems) {
            boolean found = false;
            
            for (Iterator<RecipeIngredient> it = requiredIngredients.iterator(); it.hasNext(); ) {
                RecipeIngredient required = it.next();
                if (required.matches(provided)) {
                    // Zkontroluj množství
                    if (provided.getAmount() >= required.getAmount()) {
                        it.remove();
                        found = true;
                        break;
                    }
                }
            }
            
            if (!found) {
                return false;
            }
        }
        
        return requiredIngredients.isEmpty();
    }
    
    /**
     * Zkontroluje, jestli hráč splňuje unlock requirements
     */
    public boolean isUnlockedFor(UUID playerUuid) {
        // TODO: Implementovat kontrolu unlock requirements
        return true;
    }
    
    /**
     * Vytvoří result ItemStack
     */
    public ItemStack createResult() {
        if (result != null) {
            return result.createItemStack(resultAmount);
        }
        return null;
    }
    
    /**
     * Spotřebuje ingredience z crafting gridu
     */
    public void consumeIngredients(ItemStack[] craftingGrid) {
        if (recipeType == RecipeType.SHAPED) {
            consumeShapedIngredients(craftingGrid);
        } else {
            consumeShapelessIngredients(craftingGrid);
        }
    }
    
    private void consumeShapedIngredients(ItemStack[] craftingGrid) {
        for (int i = 0; i < 9; i++) {
            RecipeIngredient required = ingredients.get(i);
            if (required != null && craftingGrid[i] != null) {
                int newAmount = craftingGrid[i].getAmount() - required.getAmount();
                if (newAmount <= 0) {
                    craftingGrid[i] = null;
                } else {
                    craftingGrid[i].setAmount(newAmount);
                }
            }
        }
    }
    
    private void consumeShapelessIngredients(ItemStack[] craftingGrid) {
        List<RecipeIngredient> requiredIngredients = new ArrayList<>(shapelessIngredients);
        
        for (ItemStack item : craftingGrid) {
            if (item != null && item.getType() != Material.AIR) {
                for (Iterator<RecipeIngredient> it = requiredIngredients.iterator(); it.hasNext(); ) {
                    RecipeIngredient required = it.next();
                    if (required.matches(item)) {
                        int consumeAmount = Math.min(item.getAmount(), required.getAmount());
                        item.setAmount(item.getAmount() - consumeAmount);
                        
                        if (item.getAmount() <= 0) {
                            item.setType(Material.AIR);
                        }
                        
                        it.remove();
                        break;
                    }
                }
            }
        }
    }
    
    @Override
    public String toString() {
        return "CustomRecipe{" +
                "id='" + id + '\'' +
                ", displayName='" + displayName + '\'' +
                ", recipeType=" + recipeType +
                ", result=" + (result != null ? result.getId() : "null") +
                '}';
    }
}
