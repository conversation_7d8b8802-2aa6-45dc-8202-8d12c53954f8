package cz.hypixel.skyblock.commands;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.gui.CollectionsGUI;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * Příkaz pro otevření Collections GUI
 */
public class CollectionsCommand implements CommandExecutor {

    private final HypixelSkyBlockCZ plugin;
    private final CollectionsGUI collectionsGUI;

    public CollectionsCommand(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.collectionsGUI = new CollectionsGUI(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cTento příkaz může použít pouze hráč!");
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            // Otev<PERSON><PERSON> hlavní Collections menu
            collectionsGUI.openCollectionsMenu(player);
            return true;
        }

        if (args.length == 1) {
            String category = args[0].toLowerCase();
            
            // Zkontroluj platné kategorie
            switch (category) {
                case "farming":
                case "mining":
                case "combat":
                case "foraging":
                case "fishing":
                case "boss":
                case "rift":
                    collectionsGUI.openCategoryMenu(player, category);
                    break;
                default:
                    player.sendMessage("§cNeplatná kategorie! Dostupné: farming, mining, combat, foraging, fishing, boss, rift");
                    break;
            }
            return true;
        }

        // Příliš mnoho argumentů
        player.sendMessage("§cPoužití: /collections [kategorie]");
        return true;
    }
}
