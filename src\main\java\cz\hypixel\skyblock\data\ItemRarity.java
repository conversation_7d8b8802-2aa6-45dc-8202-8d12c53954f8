package cz.hypixel.skyblock.data;

/**
 * Enum pro rarity custom př<PERSON><PERSON>ě<PERSON>ů podle Hypixel SkyBlock
 */
public enum ItemRarity {
    
    COMMON("Common", "§f", "§f§lCOMMON"),
    UNCOMMON("Uncommon", "§a", "§a§lUNCOMMON"),
    RARE("Rare", "§9", "§9§lRARE"),
    EPIC("Epic", "§5", "§5§lEPIC"),
    LEGENDARY("Legendary", "§6", "§6§lLEGENDARY"),
    MYTHIC("Mythic", "§d", "§d§lMYTHIC"),
    DIVINE("Divine", "§b", "§b§lDIVINE"),
    SPECIAL("Special", "§c", "§c§lSPECIAL"),
    VERY_SPECIAL("Very Special", "§c", "§c§lVERY SPECIAL");
    
    private final String displayName;
    private final String colorCode;
    private final String formattedName;
    
    ItemRarity(String displayName, String colorCode, String formattedName) {
        this.displayName = displayName;
        this.colorCode = colorCode;
        this.formattedName = formattedName;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getColorCode() {
        return colorCode;
    }
    
    public String getFormattedName() {
        return formattedName;
    }
    
    public String getColoredName() {
        return colorCode + displayName;
    }
    
    /**
     * Získá rarity podle názvu
     */
    public static ItemRarity fromString(String name) {
        for (ItemRarity rarity : values()) {
            if (rarity.name().equalsIgnoreCase(name) || 
                rarity.displayName.equalsIgnoreCase(name)) {
                return rarity;
            }
        }
        return COMMON; // Výchozí rarity
    }
    
    /**
     * Získá číselnou hodnotu pro porovnání
     */
    public int getValue() {
        return ordinal();
    }
    
    /**
     * Zkontroluje, jestli je rarity vyšší než jiná
     */
    public boolean isHigherThan(ItemRarity other) {
        return this.getValue() > other.getValue();
    }
    
    /**
     * Získá drop chance podle rarity
     */
    public double getDropChance() {
        return switch (this) {
            case COMMON -> 0.6;        // 60%
            case UNCOMMON -> 0.25;     // 25%
            case RARE -> 0.1;          // 10%
            case EPIC -> 0.04;         // 4%
            case LEGENDARY -> 0.01;    // 1%
            case MYTHIC -> 0.001;      // 0.1%
            case DIVINE -> 0.0005;     // 0.05%
            case SPECIAL -> 0.0001;    // 0.01%
            case VERY_SPECIAL -> 0.00001; // 0.001%
        };
    }
    
    /**
     * Získá base value podle rarity
     */
    public double getBaseValue() {
        return switch (this) {
            case COMMON -> 1.0;
            case UNCOMMON -> 2.5;
            case RARE -> 5.0;
            case EPIC -> 10.0;
            case LEGENDARY -> 25.0;
            case MYTHIC -> 50.0;
            case DIVINE -> 100.0;
            case SPECIAL -> 200.0;
            case VERY_SPECIAL -> 500.0;
        };
    }
}
