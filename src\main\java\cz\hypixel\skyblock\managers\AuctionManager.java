package cz.hypixel.skyblock.managers;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.AuctionData;
import cz.hypixel.skyblock.data.AuctionCategory;
import cz.hypixel.skyblock.data.AuctionStatus;
import cz.hypixel.skyblock.database.PlayerData;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;

/**
 * Manager pro Auction House systém podle Hypixel SkyBlock
 */
public class AuctionManager {

    private final HypixelSkyBlockCZ plugin;
    private final Logger logger;
    private final DataSource dataSource;
    private final Map<Integer, AuctionData> auctionCache;
    
    // Limity a poplatky
    private static final int MAX_AUCTIONS_PER_PLAYER = 5;
    private static final double AUCTION_FEE_PERCENTAGE = 0.01; // 1% poplatek
    private static final double MIN_AUCTION_FEE = 100; // Minimální poplatek 100 mincí
    private static final double MAX_AUCTION_FEE = 1_000_000; // Maximální poplatek 1M mincí

    public AuctionManager(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.dataSource = plugin.getPlayerDataManager().getDataSource();
        this.auctionCache = new HashMap<>();
        
        // Spusť pravidelné čištění vypršelých aukcí
        startAuctionCleanupTask();
        
        logger.info("AuctionManager inicializován");
    }

    /**
     * Vytvoří novou aukci
     */
    public CompletableFuture<Boolean> createAuction(Player seller, ItemStack item, double startingBid, 
                                                   boolean buyItNow, double binPrice, int durationHours) {
        return CompletableFuture.supplyAsync(() -> {
            UUID sellerUuid = seller.getUniqueId();
            
            // Zkontroluj limit aukcí na hráče
            if (getActiveAuctionCount(sellerUuid).join() >= MAX_AUCTIONS_PER_PLAYER) {
                seller.sendMessage("§cMáš příliš mnoho aktivních aukcí! (Max: " + MAX_AUCTIONS_PER_PLAYER + ")");
                return false;
            }
            
            // Zkontroluj platnost předmětu
            if (item == null || item.getType().isAir()) {
                seller.sendMessage("§cNeplatný předmět!");
                return false;
            }
            
            // Zkontroluj minimální cenu
            if (startingBid < 1) {
                seller.sendMessage("§cMinimální počáteční cena je 1 mince!");
                return false;
            }
            
            if (buyItNow && binPrice < startingBid) {
                seller.sendMessage("§cBIN cena musí být vyšší než počáteční cena!");
                return false;
            }
            
            // Spočítej poplatek
            double fee = calculateAuctionFee(buyItNow ? binPrice : startingBid);
            
            // Zkontroluj, jestli má hráč dostatek mincí na poplatek
            PlayerData playerData = plugin.getPlayerDataManager().loadPlayerData(sellerUuid).join();
            if (playerData == null || playerData.getCoins() < fee) {
                seller.sendMessage("§cNemáš dostatek mincí na poplatek! Potřebuješ: " + 
                                 plugin.getBankManager().formatCoins(fee));
                return false;
            }
            
            // Odeber poplatek
            playerData.removeCoins(fee);
            plugin.getPlayerDataManager().savePlayerData(playerData);
            
            // Vytvoř aukci
            AuctionCategory category = AuctionCategory.fromMaterial(item.getType());
            String itemName = item.hasItemMeta() && item.getItemMeta().hasDisplayName() 
                            ? item.getItemMeta().getDisplayName() 
                            : item.getType().name();
            
            AuctionData auction = new AuctionData(sellerUuid, item, itemName, category, 
                                                startingBid, buyItNow, binPrice, durationHours);
            
            // Serializuj ItemStack
            auction.setItemData(serializeItemStack(item));
            
            // Ulož do databáze
            String sql = """
                INSERT INTO auctions (seller_uuid, item_data, item_name, category, starting_bid, 
                                    current_bid, buy_it_now, bin_price, duration_hours, created_at, ends_at, status)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;
            
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
                
                stmt.setString(1, sellerUuid.toString());
                stmt.setString(2, auction.getItemData());
                stmt.setString(3, auction.getItemName());
                stmt.setString(4, auction.getCategory().name());
                stmt.setDouble(5, auction.getStartingBid());
                stmt.setDouble(6, auction.getCurrentBid());
                stmt.setBoolean(7, auction.isBuyItNow());
                stmt.setDouble(8, auction.getBinPrice());
                stmt.setInt(9, auction.getDurationHours());
                stmt.setLong(10, auction.getCreatedAt());
                stmt.setLong(11, auction.getEndsAt());
                stmt.setString(12, auction.getStatus().name());
                
                int result = stmt.executeUpdate();
                
                if (result > 0) {
                    // Získej vygenerované ID
                    try (ResultSet rs = stmt.getGeneratedKeys()) {
                        if (rs.next()) {
                            auction.setId(rs.getInt(1));
                        }
                    }
                    
                    // Přidej do cache
                    auctionCache.put(auction.getId(), auction);
                    
                    // Odeber předmět z inventáře
                    seller.getInventory().removeItem(item);
                    
                    seller.sendMessage("§aAukce úspěšně vytvořena!");
                    seller.sendMessage("§7ID: §e" + auction.getId());
                    seller.sendMessage("§7Poplatek: §c-" + plugin.getBankManager().formatCoins(fee));
                    seller.sendMessage("§7Doba trvání: §e" + durationHours + " hodin");
                    
                    logger.info("Vytvořena aukce ID " + auction.getId() + " od hráče " + seller.getName());
                    return true;
                }
                
            } catch (SQLException e) {
                logger.severe("Chyba při vytváření aukce: " + e.getMessage());
                // Vrať poplatek
                playerData.addCoins(fee);
                plugin.getPlayerDataManager().savePlayerData(playerData);
            }
            
            return false;
        });
    }

    /**
     * Podá bid na aukci
     */
    public CompletableFuture<Boolean> placeBid(Player bidder, int auctionId, double bidAmount) {
        return CompletableFuture.supplyAsync(() -> {
            UUID bidderUuid = bidder.getUniqueId();
            
            // Načti aukci
            AuctionData auction = getAuction(auctionId).join();
            if (auction == null) {
                bidder.sendMessage("§cAukce nenalezena!");
                return false;
            }
            
            // Zkontroluj, jestli může dražit
            if (!auction.canBid(bidderUuid, bidAmount)) {
                bidder.sendMessage("§cNemůžeš dražit na tuto aukci!");
                if (auction.getSellerUuid().equals(bidderUuid)) {
                    bidder.sendMessage("§cNemůžeš dražit na svou vlastní aukci!");
                } else if (!auction.isActive()) {
                    bidder.sendMessage("§cAukce již není aktivní!");
                } else {
                    double minBid = auction.getNextMinimumBid();
                    bidder.sendMessage("§cMinimální bid: " + plugin.getBankManager().formatCoins(minBid));
                }
                return false;
            }
            
            // Zkontroluj mince
            PlayerData bidderData = plugin.getPlayerDataManager().loadPlayerData(bidderUuid).join();
            if (bidderData == null || bidderData.getCoins() < bidAmount) {
                bidder.sendMessage("§cNemáš dostatek mincí!");
                return false;
            }
            
            // Vrať mince předchozímu nejvyššímu dražiteli
            if (auction.getHighestBidderUuid() != null) {
                PlayerData previousBidderData = plugin.getPlayerDataManager()
                    .loadPlayerData(auction.getHighestBidderUuid()).join();
                if (previousBidderData != null) {
                    previousBidderData.addCoins(auction.getCurrentBid());
                    plugin.getPlayerDataManager().savePlayerData(previousBidderData);
                    
                    // Pošli zprávu předchozímu dražiteli
                    Player previousBidder = Bukkit.getPlayer(auction.getHighestBidderUuid());
                    if (previousBidder != null) {
                        previousBidder.sendMessage("§cByl jsi přebit v aukci §e" + auction.getItemName() + "§c!");
                        previousBidder.sendMessage("§7Vráceno: §e" + plugin.getBankManager().formatCoins(auction.getCurrentBid()));
                    }
                }
            }
            
            // Odeber mince novému dražiteli
            bidderData.removeCoins(bidAmount);
            plugin.getPlayerDataManager().savePlayerData(bidderData);
            
            // Aktualizuj aukci
            auction.setCurrentBid(bidAmount);
            auction.setHighestBidderUuid(bidderUuid);
            
            // Ulož do databáze
            updateAuctionBid(auction);
            
            // Zaznamenej bid
            recordBid(auctionId, bidderUuid, bidAmount);
            
            bidder.sendMessage("§aBid úspěšně podán!");
            bidder.sendMessage("§7Tvůj bid: §e" + plugin.getBankManager().formatCoins(bidAmount));
            
            // Pošli zprávu prodávajícímu
            Player seller = Bukkit.getPlayer(auction.getSellerUuid());
            if (seller != null) {
                seller.sendMessage("§aNový bid na tvou aukci §e" + auction.getItemName() + "§a!");
                seller.sendMessage("§7Bid: §e" + plugin.getBankManager().formatCoins(bidAmount));
                seller.sendMessage("§7Od: §e" + bidder.getName());
            }
            
            return true;
        });
    }

    /**
     * Koupí předmět přes BIN
     */
    public CompletableFuture<Boolean> buyInstantly(Player buyer, int auctionId) {
        return CompletableFuture.supplyAsync(() -> {
            UUID buyerUuid = buyer.getUniqueId();
            
            // Načti aukci
            AuctionData auction = getAuction(auctionId).join();
            if (auction == null) {
                buyer.sendMessage("§cAukce nenalezena!");
                return false;
            }
            
            // Zkontroluj, jestli je to BIN aukce
            if (!auction.isBuyItNow()) {
                buyer.sendMessage("§cToto není BIN aukce!");
                return false;
            }
            
            // Zkontroluj, jestli je aktivní
            if (!auction.isActive()) {
                buyer.sendMessage("§cAukce již není aktivní!");
                return false;
            }
            
            // Zkontroluj, jestli to není vlastní aukce
            if (auction.getSellerUuid().equals(buyerUuid)) {
                buyer.sendMessage("§cNemůžeš koupit svou vlastní aukci!");
                return false;
            }
            
            double price = auction.getBinPrice();
            
            // Zkontroluj mince
            PlayerData buyerData = plugin.getPlayerDataManager().loadPlayerData(buyerUuid).join();
            if (buyerData == null || buyerData.getCoins() < price) {
                buyer.sendMessage("§cNemáš dostatek mincí!");
                buyer.sendMessage("§7Potřebuješ: §e" + plugin.getBankManager().formatCoins(price));
                return false;
            }
            
            // Proveď transakci
            buyerData.removeCoins(price);
            plugin.getPlayerDataManager().savePlayerData(buyerData);
            
            // Přidej mince prodávajícímu
            PlayerData sellerData = plugin.getPlayerDataManager().loadPlayerData(auction.getSellerUuid()).join();
            if (sellerData != null) {
                sellerData.addCoins(price);
                plugin.getPlayerDataManager().savePlayerData(sellerData);
            }
            
            // Dej předmět kupujícímu
            ItemStack item = deserializeItemStack(auction.getItemData());
            if (item != null) {
                buyer.getInventory().addItem(item);
            }
            
            // Označ aukci jako prodanou
            auction.setStatus(AuctionStatus.SOLD);
            updateAuctionStatus(auction);
            
            buyer.sendMessage("§aPředmět úspěšně koupen!");
            buyer.sendMessage("§7Zaplaceno: §c-" + plugin.getBankManager().formatCoins(price));
            
            // Pošli zprávu prodávajícímu
            Player seller = Bukkit.getPlayer(auction.getSellerUuid());
            if (seller != null) {
                seller.sendMessage("§aTvá aukce byla prodána!");
                seller.sendMessage("§7Předmět: §e" + auction.getItemName());
                seller.sendMessage("§7Cena: §e+" + plugin.getBankManager().formatCoins(price));
                seller.sendMessage("§7Kupující: §e" + buyer.getName());
            }
            
            return true;
        });
    }

    /**
     * Získá počet aktivních aukcí hráče
     */
    public CompletableFuture<Integer> getActiveAuctionCount(UUID playerUuid) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = "SELECT COUNT(*) FROM auctions WHERE seller_uuid = ? AND status = 'ACTIVE'";
            
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setString(1, playerUuid.toString());
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return rs.getInt(1);
                    }
                }
                
            } catch (SQLException e) {
                logger.warning("Chyba při počítání aukcí: " + e.getMessage());
            }
            
            return 0;
        });
    }

    /**
     * Získá aukci podle ID
     */
    public CompletableFuture<AuctionData> getAuction(int auctionId) {
        // Zkontroluj cache
        AuctionData cached = auctionCache.get(auctionId);
        if (cached != null) {
            return CompletableFuture.completedFuture(cached);
        }
        
        return loadAuctionFromDatabase(auctionId);
    }

    /**
     * Načte aukci z databáze
     */
    private CompletableFuture<AuctionData> loadAuctionFromDatabase(int auctionId) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = """
                SELECT id, seller_uuid, item_data, item_name, category, starting_bid, current_bid,
                       highest_bidder_uuid, buy_it_now, bin_price, duration_hours, created_at, ends_at, status
                FROM auctions WHERE id = ?
            """;
            
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setInt(1, auctionId);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        AuctionData auction = new AuctionData();
                        auction.setId(rs.getInt("id"));
                        auction.setSellerUuid(UUID.fromString(rs.getString("seller_uuid")));
                        auction.setItemData(rs.getString("item_data"));
                        auction.setItemName(rs.getString("item_name"));
                        auction.setCategory(AuctionCategory.fromString(rs.getString("category")));
                        auction.setStartingBid(rs.getDouble("starting_bid"));
                        auction.setCurrentBid(rs.getDouble("current_bid"));
                        
                        String bidderUuid = rs.getString("highest_bidder_uuid");
                        if (bidderUuid != null) {
                            auction.setHighestBidderUuid(UUID.fromString(bidderUuid));
                        }
                        
                        auction.setBuyItNow(rs.getBoolean("buy_it_now"));
                        auction.setBinPrice(rs.getDouble("bin_price"));
                        auction.setDurationHours(rs.getInt("duration_hours"));
                        auction.setCreatedAt(rs.getLong("created_at"));
                        auction.setEndsAt(rs.getLong("ends_at"));
                        auction.setStatus(AuctionStatus.fromString(rs.getString("status")));
                        
                        // Přidej do cache
                        auctionCache.put(auctionId, auction);
                        
                        return auction;
                    }
                }
                
            } catch (SQLException e) {
                logger.severe("Chyba při načítání aukce: " + e.getMessage());
            }
            
            return null;
        });
    }

    /**
     * Aktualizuje bid v databázi
     */
    private void updateAuctionBid(AuctionData auction) {
        String sql = "UPDATE auctions SET current_bid = ?, highest_bidder_uuid = ? WHERE id = ?";

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setDouble(1, auction.getCurrentBid());
            stmt.setString(2, auction.getHighestBidderUuid().toString());
            stmt.setInt(3, auction.getId());

            stmt.executeUpdate();

        } catch (SQLException e) {
            logger.warning("Chyba při aktualizaci bidu: " + e.getMessage());
        }
    }

    /**
     * Aktualizuje status aukce
     */
    private void updateAuctionStatus(AuctionData auction) {
        String sql = "UPDATE auctions SET status = ? WHERE id = ?";

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, auction.getStatus().name());
            stmt.setInt(2, auction.getId());

            stmt.executeUpdate();

        } catch (SQLException e) {
            logger.warning("Chyba při aktualizaci statusu aukce: " + e.getMessage());
        }
    }

    /**
     * Zaznamenává bid do databáze
     */
    private void recordBid(int auctionId, UUID bidderUuid, double bidAmount) {
        String sql = "INSERT INTO auction_bids (auction_id, bidder_uuid, bid_amount, created_at) VALUES (?, ?, ?, ?)";

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setInt(1, auctionId);
            stmt.setString(2, bidderUuid.toString());
            stmt.setDouble(3, bidAmount);
            stmt.setLong(4, System.currentTimeMillis());

            stmt.executeUpdate();

        } catch (SQLException e) {
            logger.warning("Chyba při zaznamenávání bidu: " + e.getMessage());
        }
    }

    /**
     * Spustí úlohu pro čištění vypršelých aukcí
     */
    private void startAuctionCleanupTask() {
        Bukkit.getScheduler().runTaskTimerAsynchronously(plugin, () -> {
            processExpiredAuctions();
        }, 20 * 60, 20 * 60); // Každou minutu
    }

    /**
     * Zpracuje vypršelé aukce
     */
    private void processExpiredAuctions() {
        String sql = "SELECT id FROM auctions WHERE status = 'ACTIVE' AND ends_at < ?";

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setLong(1, System.currentTimeMillis());

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    int auctionId = rs.getInt("id");
                    processExpiredAuction(auctionId);
                }
            }

        } catch (SQLException e) {
            logger.warning("Chyba při zpracování vypršelých aukcí: " + e.getMessage());
        }
    }

    /**
     * Zpracuje jednu vypršelou aukci
     */
    private void processExpiredAuction(int auctionId) {
        AuctionData auction = getAuction(auctionId).join();
        if (auction == null || !auction.isExpired()) {
            return;
        }

        if (auction.hasBids()) {
            // Aukce s bidy - prodej nejvyššímu dražiteli
            PlayerData sellerData = plugin.getPlayerDataManager().loadPlayerData(auction.getSellerUuid()).join();
            if (sellerData != null) {
                sellerData.addCoins(auction.getCurrentBid());
                plugin.getPlayerDataManager().savePlayerData(sellerData);
            }

            // Dej předmět nejvyššímu dražiteli
            Player winner = Bukkit.getPlayer(auction.getHighestBidderUuid());
            if (winner != null) {
                ItemStack item = deserializeItemStack(auction.getItemData());
                if (item != null) {
                    winner.getInventory().addItem(item);
                }
                winner.sendMessage("§aVyhrál jsi aukci: §e" + auction.getItemName());
                winner.sendMessage("§7Zaplaceno: §c-" + plugin.getBankManager().formatCoins(auction.getCurrentBid()));
            }

            // Pošli zprávu prodávajícímu
            Player seller = Bukkit.getPlayer(auction.getSellerUuid());
            if (seller != null) {
                seller.sendMessage("§aTvá aukce skončila!");
                seller.sendMessage("§7Předmět: §e" + auction.getItemName());
                seller.sendMessage("§7Finální cena: §e+" + plugin.getBankManager().formatCoins(auction.getCurrentBid()));
            }

            auction.setStatus(AuctionStatus.SOLD);
        } else {
            // Aukce bez bidů - vrať předmět prodávajícímu
            Player seller = Bukkit.getPlayer(auction.getSellerUuid());
            if (seller != null) {
                ItemStack item = deserializeItemStack(auction.getItemData());
                if (item != null) {
                    seller.getInventory().addItem(item);
                }
                seller.sendMessage("§cTvá aukce skončila bez bidů: §e" + auction.getItemName());
                seller.sendMessage("§7Předmět byl vrácen do inventáře.");
            }

            auction.setStatus(AuctionStatus.EXPIRED);
        }

        updateAuctionStatus(auction);
        auctionCache.remove(auctionId);
    }

    /**
     * Spočítá poplatek za aukci
     */
    private double calculateAuctionFee(double price) {
        double fee = price * AUCTION_FEE_PERCENTAGE;
        return Math.max(MIN_AUCTION_FEE, Math.min(fee, MAX_AUCTION_FEE));
    }

    /**
     * Serializuje ItemStack do stringu
     */
    private String serializeItemStack(ItemStack item) {
        // Zjednodušená serializace - v produkci by měla být robustnější
        if (item == null) return "";

        StringBuilder sb = new StringBuilder();
        sb.append(item.getType().name()).append(";");
        sb.append(item.getAmount()).append(";");

        if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
            sb.append(item.getItemMeta().getDisplayName());
        }

        return sb.toString();
    }

    /**
     * Deserializuje ItemStack ze stringu (veřejná metoda pro GUI)
     */
    public ItemStack deserializeItemStack(String data) {
        if (data == null || data.isEmpty()) return null;

        try {
            String[] parts = data.split(";");
            if (parts.length < 2) return null;

            org.bukkit.Material material = org.bukkit.Material.valueOf(parts[0]);
            int amount = Integer.parseInt(parts[1]);

            ItemStack item = new ItemStack(material, amount);

            if (parts.length > 2 && !parts[2].isEmpty()) {
                org.bukkit.inventory.meta.ItemMeta meta = item.getItemMeta();
                if (meta != null) {
                    meta.setDisplayName(parts[2]);
                    item.setItemMeta(meta);
                }
            }

            return item;
        } catch (Exception e) {
            logger.warning("Chyba při deserializaci ItemStack: " + e.getMessage());
            return null;
        }
    }

    /**
     * Získá aktivní aukce podle kategorie
     */
    public CompletableFuture<List<AuctionData>> getAuctionsByCategory(AuctionCategory category, int page, int pageSize) {
        return CompletableFuture.supplyAsync(() -> {
            List<AuctionData> auctions = new ArrayList<>();
            int offset = page * pageSize;

            String sql = """
                SELECT id, seller_uuid, item_data, item_name, category, starting_bid, current_bid,
                       highest_bidder_uuid, buy_it_now, bin_price, duration_hours, created_at, ends_at, status
                FROM auctions
                WHERE status = 'ACTIVE' AND category = ?
                ORDER BY created_at DESC
                LIMIT ? OFFSET ?
            """;

            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {

                stmt.setString(1, category.name());
                stmt.setInt(2, pageSize);
                stmt.setInt(3, offset);

                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        AuctionData auction = createAuctionFromResultSet(rs);
                        if (auction != null) {
                            auctions.add(auction);
                        }
                    }
                }

            } catch (SQLException e) {
                logger.severe("Chyba při načítání aukcí podle kategorie: " + e.getMessage());
            }

            return auctions;
        });
    }

    /**
     * Vytvoří AuctionData z ResultSet
     */
    private AuctionData createAuctionFromResultSet(ResultSet rs) throws SQLException {
        AuctionData auction = new AuctionData();
        auction.setId(rs.getInt("id"));
        auction.setSellerUuid(UUID.fromString(rs.getString("seller_uuid")));
        auction.setItemData(rs.getString("item_data"));
        auction.setItemName(rs.getString("item_name"));
        auction.setCategory(AuctionCategory.fromString(rs.getString("category")));
        auction.setStartingBid(rs.getDouble("starting_bid"));
        auction.setCurrentBid(rs.getDouble("current_bid"));

        String bidderUuid = rs.getString("highest_bidder_uuid");
        if (bidderUuid != null) {
            auction.setHighestBidderUuid(UUID.fromString(bidderUuid));
        }

        auction.setBuyItNow(rs.getBoolean("buy_it_now"));
        auction.setBinPrice(rs.getDouble("bin_price"));
        auction.setDurationHours(rs.getInt("duration_hours"));
        auction.setCreatedAt(rs.getLong("created_at"));
        auction.setEndsAt(rs.getLong("ends_at"));
        auction.setStatus(AuctionStatus.fromString(rs.getString("status")));

        return auction;
    }

    /**
     * Získá aukce hráče
     */
    public CompletableFuture<List<AuctionData>> getPlayerAuctions(UUID playerUuid) {
        return CompletableFuture.supplyAsync(() -> {
            List<AuctionData> auctions = new ArrayList<>();

            String sql = """
                SELECT id, seller_uuid, item_data, item_name, category, starting_bid, current_bid,
                       highest_bidder_uuid, buy_it_now, bin_price, duration_hours, created_at, ends_at, status
                FROM auctions
                WHERE seller_uuid = ?
                ORDER BY created_at DESC
            """;

            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {

                stmt.setString(1, playerUuid.toString());

                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        AuctionData auction = createAuctionFromResultSet(rs);
                        if (auction != null) {
                            auctions.add(auction);
                        }
                    }
                }

            } catch (SQLException e) {
                logger.severe("Chyba při načítání aukcí hráče: " + e.getMessage());
            }

            return auctions;
        });
    }
}
