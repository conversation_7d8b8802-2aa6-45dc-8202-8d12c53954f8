package cz.hypixel.skyblock.data;

import org.bukkit.Material;

import java.util.ArrayList;
import java.util.List;

/**
 * Data třída pro kolekce podle Hypixel SkyBlock
 */
public class CollectionData {
    
    private final String name;
    private final String category;
    private final Material displayMaterial;
    private final List<CollectionTier> tiers;
    
    public CollectionData(String name, String category, Material displayMaterial) {
        this.name = name;
        this.category = category;
        this.displayMaterial = displayMaterial;
        this.tiers = new ArrayList<>();
    }
    
    public void addTier(CollectionTier tier) {
        this.tiers.add(tier);
    }
    
    public String getName() {
        return name;
    }
    
    public String getCategory() {
        return category;
    }
    
    public Material getDisplayMaterial() {
        return displayMaterial;
    }
    
    public List<CollectionTier> getTiers() {
        return new ArrayList<>(tiers);
    }
    
    public CollectionTier getTier(int tierNumber) {
        for (CollectionTier tier : tiers) {
            if (tier.getTier() == tierNumber) {
                return tier;
            }
        }
        return null;
    }
    
    public int getMaxTier() {
        return tiers.size();
    }
    
    public int getCurrentTier(long amount) {
        int currentTier = 0;
        for (CollectionTier tier : tiers) {
            if (amount >= tier.getRequiredAmount()) {
                currentTier = tier.getTier();
            } else {
                break;
            }
        }
        return currentTier;
    }
    
    public CollectionTier getNextTier(long amount) {
        for (CollectionTier tier : tiers) {
            if (amount < tier.getRequiredAmount()) {
                return tier;
            }
        }
        return null; // Všechny tiery odemčeny
    }
    
    public double getProgress(long amount) {
        CollectionTier nextTier = getNextTier(amount);
        if (nextTier == null) {
            return 1.0; // 100% - všechny tiery odemčeny
        }
        
        long previousRequirement = 0;
        if (nextTier.getTier() > 1) {
            CollectionTier previousTier = getTier(nextTier.getTier() - 1);
            if (previousTier != null) {
                previousRequirement = previousTier.getRequiredAmount();
            }
        }
        
        long currentProgress = amount - previousRequirement;
        long totalNeeded = nextTier.getRequiredAmount() - previousRequirement;
        
        return Math.min(1.0, (double) currentProgress / totalNeeded);
    }
    
    public String getDisplayName() {
        // Převede CACTUS na "Cactus", ROTTEN_FLESH na "Rotten Flesh" atd.
        String[] parts = name.toLowerCase().split("_");
        StringBuilder result = new StringBuilder();
        
        for (int i = 0; i < parts.length; i++) {
            if (i > 0) {
                result.append(" ");
            }
            result.append(parts[i].substring(0, 1).toUpperCase())
                  .append(parts[i].substring(1));
        }
        
        return result.toString();
    }
    
    public String getCategoryDisplayName() {
        switch (category.toLowerCase()) {
            case "farming":
                return "§6Farming";
            case "mining":
                return "§8Mining";
            case "combat":
                return "§cCombat";
            case "foraging":
                return "§2Foraging";
            case "fishing":
                return "§3Fishing";
            case "boss":
                return "§5Boss";
            case "rift":
                return "§dRift";
            default:
                return "§7" + category;
        }
    }
    
    @Override
    public String toString() {
        return "CollectionData{" +
                "name='" + name + '\'' +
                ", category='" + category + '\'' +
                ", tiers=" + tiers.size() +
                '}';
    }
}
