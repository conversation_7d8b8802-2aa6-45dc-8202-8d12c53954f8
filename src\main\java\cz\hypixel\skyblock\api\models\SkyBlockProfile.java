package cz.hypixel.skyblock.api.models;

import com.google.gson.annotations.SerializedName;
import java.util.Map;

/**
 * Model pro SkyBlock profil z Hypixel API
 */
public class SkyBlockProfile {
    
    @SerializedName("profile_id")
    private String profileId;
    
    @SerializedName("cute_name")
    private String cuteName;
    
    private Map<String, ProfileMember> members;
    
    private boolean selected;
    
    // Gettery
    public String getProfileId() {
        return profileId;
    }
    
    public String getCuteName() {
        return cuteName;
    }
    
    public Map<String, ProfileMember> getMembers() {
        return members;
    }
    
    public boolean isSelected() {
        return selected;
    }
    
    /**
     * Získá data člena profilu
     */
    public ProfileMember getMember(String uuid) {
        return members != null ? members.get(uuid) : null;
    }
    
    /**
     * Model pro člena profilu
     */
    public static class ProfileMember {
        
        @SerializedName("last_save")
        private long lastSave;
        
        @SerializedName("first_join")
        private long firstJoin;
        
        private Map<String, Integer> experience;
        
        private Map<String, Long> collection;
        
        private Map<String, Object> stats;
        
        private Map<String, Object> objectives;
        
        private Map<String, Object> quests;
        
        private Map<String, Object> tutorial;
        
        private Inventory inv_armor;
        private Inventory inv_contents;
        private Inventory ender_chest_contents;
        private Inventory talisman_bag;
        private Inventory fishing_bag;
        private Inventory potion_bag;
        
        // Gettery
        public long getLastSave() {
            return lastSave;
        }
        
        public long getFirstJoin() {
            return firstJoin;
        }
        
        public Map<String, Integer> getExperience() {
            return experience;
        }
        
        public Map<String, Long> getCollection() {
            return collection;
        }
        
        public Map<String, Object> getStats() {
            return stats;
        }
        
        /**
         * Získá úroveň dovednosti
         */
        public int getSkillLevel(String skill) {
            if (experience == null) return 0;
            
            Integer exp = experience.get("SKILL_" + skill.toUpperCase());
            if (exp == null) return 0;
            
            return calculateSkillLevel(exp);
        }
        
        /**
         * Získá množství v kolekci
         */
        public long getCollectionAmount(String item) {
            if (collection == null) return 0;
            return collection.getOrDefault(item.toUpperCase(), 0L);
        }
        
        /**
         * Vypočítá úroveň dovednosti z experience
         */
        private int calculateSkillLevel(int experience) {
            // Zjednodušený výpočet - skutečné XP requirements jsou složitější
            int[] xpTable = {
                0, 50, 175, 375, 675, 1175, 1925, 2925, 4425, 6425, 9925, 14925, 22425, 32425, 47425, 67425, 97425, 147425, 222425, 322425, 522425, 822425, 1222425, 1722425, 2322425, 3022425, 3822425, 4722425, 5722425, 6822425, 8022425, 9322425, 10722425, 12222425, 13822425, 15522425, 17322425, 19222425, 21222425, 23322425, 25522425, 27822425, 30222425, 32722425, 35322425, 38072425, 40972425, 44072425, 47472425, 51172425, 55172425, 59472425, 64072425, 68972425, 74172425, 79672425, 85472425, 91572425, 97972425, 104672425
            };
            
            for (int i = xpTable.length - 1; i >= 0; i--) {
                if (experience >= xpTable[i]) {
                    return i;
                }
            }
            return 0;
        }
    }
    
    /**
     * Model pro inventář
     */
    public static class Inventory {
        private String type;
        private String data; // Base64 encoded NBT data
        
        public String getType() {
            return type;
        }
        
        public String getData() {
            return data;
        }
    }
}
