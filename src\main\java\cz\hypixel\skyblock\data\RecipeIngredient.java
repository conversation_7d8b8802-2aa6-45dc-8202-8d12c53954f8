package cz.hypixel.skyblock.data;

import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

/**
 * Třída pro ingredience v receptech podle Hypixel SkyBlock
 */
public class RecipeIngredient {
    
    private Material material;
    private String customItemId;
    private int amount;
    private boolean isCustomItem;
    
    // Konstruktor pro vanilla materiály
    public RecipeIngredient(Material material, int amount) {
        this.material = material;
        this.amount = amount;
        this.isCustomItem = false;
    }
    
    // Konstruktor pro custom předměty
    public RecipeIngredient(String customItemId, int amount) {
        this.customItemId = customItemId;
        this.amount = amount;
        this.isCustomItem = true;
    }
    
    // Gettery a settery
    public Material getMaterial() {
        return material;
    }
    
    public void setMaterial(Material material) {
        this.material = material;
        this.isCustomItem = false;
    }
    
    public String getCustomItemId() {
        return customItemId;
    }
    
    public void setCustomItemId(String customItemId) {
        this.customItemId = customItemId;
        this.isCustomItem = true;
    }
    
    public int getAmount() {
        return amount;
    }
    
    public void setAmount(int amount) {
        this.amount = amount;
    }
    
    public boolean isCustomItem() {
        return isCustomItem;
    }
    
    /**
     * Zkontroluje, jestli ItemStack odpovídá tomuto ingredientu
     */
    public boolean matches(ItemStack item) {
        if (item == null || item.getType() == Material.AIR) {
            return false;
        }
        
        if (isCustomItem) {
            // TODO: Implementovat kontrolu custom předmětů podle NBT
            // Pro teď jen kontrola podle display name
            if (item.hasItemMeta() && item.getItemMeta().hasDisplayName()) {
                String displayName = item.getItemMeta().getDisplayName();
                // Zjednodušená kontrola - v produkci by se kontrolovalo NBT
                return displayName.contains(customItemId);
            }
            return false;
        } else {
            return item.getType() == material && item.getAmount() >= amount;
        }
    }
    
    /**
     * Vytvoří ItemStack pro zobrazení v GUI
     */
    public ItemStack createDisplayItem() {
        if (isCustomItem) {
            // TODO: Získat custom předmět podle ID a vytvořit ItemStack
            // Pro teď vrátíme placeholder
            ItemStack item = new ItemStack(Material.BARRIER, amount);
            // Nastavit display name podle custom item ID
            return item;
        } else {
            return new ItemStack(material, amount);
        }
    }
    
    /**
     * Získá display název ingredientu
     */
    public String getDisplayName() {
        if (isCustomItem) {
            return customItemId;
        } else {
            return material.name().toLowerCase().replace("_", " ");
        }
    }
    
    /**
     * Zkopíruje ingredient
     */
    public RecipeIngredient copy() {
        if (isCustomItem) {
            return new RecipeIngredient(customItemId, amount);
        } else {
            return new RecipeIngredient(material, amount);
        }
    }
    
    @Override
    public String toString() {
        if (isCustomItem) {
            return "RecipeIngredient{customItemId='" + customItemId + "', amount=" + amount + "}";
        } else {
            return "RecipeIngredient{material=" + material + ", amount=" + amount + "}";
        }
    }
    
    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;
        
        RecipeIngredient that = (RecipeIngredient) obj;
        
        if (amount != that.amount) return false;
        if (isCustomItem != that.isCustomItem) return false;
        
        if (isCustomItem) {
            return customItemId != null ? customItemId.equals(that.customItemId) : that.customItemId == null;
        } else {
            return material == that.material;
        }
    }
    
    @Override
    public int hashCode() {
        int result = material != null ? material.hashCode() : 0;
        result = 31 * result + (customItemId != null ? customItemId.hashCode() : 0);
        result = 31 * result + amount;
        result = 31 * result + (isCustomItem ? 1 : 0);
        return result;
    }
}
