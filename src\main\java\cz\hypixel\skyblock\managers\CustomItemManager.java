package cz.hypixel.skyblock.managers;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.*;
import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.reflect.TypeToken;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

import java.io.*;
import java.lang.reflect.Type;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;

/**
 * Manager pro custom předměty a recepty podle Hypixel SkyBlock
 */
public class CustomItemManager {

    private final HypixelSkyBlockCZ plugin;
    private final Logger logger;
    private final Gson gson;
    private final File itemsFolder;
    private final File recipesFolder;
    
    private final Map<String, CustomItem> customItems;
    private final Map<String, CustomRecipe> customRecipes;
    private final Map<String, List<CustomRecipe>> recipesByCategory;

    public CustomItemManager(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.gson = new GsonBuilder()
            .setPrettyPrinting()
            .create();
        
        this.itemsFolder = new File(plugin.getDataFolder(), "items");
        this.recipesFolder = new File(plugin.getDataFolder(), "recipes");
        
        this.customItems = new HashMap<>();
        this.customRecipes = new HashMap<>();
        this.recipesByCategory = new HashMap<>();
        
        // Vytvoř složky pokud neexistují
        createDirectories();
        
        // Načti předměty a recepty
        loadAllItems();
        loadAllRecipes();
        
        logger.info("CustomItemManager inicializován s " + customItems.size() + 
                   " předměty a " + customRecipes.size() + " recepty");
    }

    /**
     * Vytvoří potřebné složky
     */
    private void createDirectories() {
        if (!itemsFolder.exists()) {
            itemsFolder.mkdirs();
        }
        if (!recipesFolder.exists()) {
            recipesFolder.mkdirs();
        }
    }

    /**
     * Načte všechny custom předměty ze souborů
     */
    private void loadAllItems() {
        File[] itemFiles = itemsFolder.listFiles((dir, name) -> name.endsWith(".json"));
        if (itemFiles == null) return;
        
        for (File file : itemFiles) {
            try {
                loadItemsFromFile(file);
            } catch (Exception e) {
                logger.warning("Chyba při načítání předmětů ze souboru " + file.getName() + ": " + e.getMessage());
            }
        }
        
        // Pokud nejsou žádné předměty, vytvoř základní
        if (customItems.isEmpty()) {
            createDefaultItems();
        }
    }

    /**
     * Načte předměty z konkrétního souboru
     */
    private void loadItemsFromFile(File file) throws IOException {
        try (FileReader reader = new FileReader(file)) {
            Type listType = new TypeToken<List<CustomItem>>(){}.getType();
            List<CustomItem> items = gson.fromJson(reader, listType);
            
            if (items != null) {
                for (CustomItem item : items) {
                    customItems.put(item.getId(), item);
                }
                logger.info("Načteno " + items.size() + " předmětů ze souboru " + file.getName());
            }
        }
    }

    /**
     * Načte všechny recepty ze souborů
     */
    private void loadAllRecipes() {
        File[] recipeFiles = recipesFolder.listFiles((dir, name) -> name.endsWith(".json"));
        if (recipeFiles == null) return;
        
        for (File file : recipeFiles) {
            try {
                loadRecipesFromFile(file);
            } catch (Exception e) {
                logger.warning("Chyba při načítání receptů ze souboru " + file.getName() + ": " + e.getMessage());
            }
        }
        
        // Pokud nejsou žádné recepty, vytvoř základní
        if (customRecipes.isEmpty()) {
            createDefaultRecipes();
        }
        
        // Organizuj recepty podle kategorií
        organizeRecipesByCategory();
    }

    /**
     * Načte recepty z konkrétního souboru
     */
    private void loadRecipesFromFile(File file) throws IOException {
        try (FileReader reader = new FileReader(file)) {
            Type listType = new TypeToken<List<CustomRecipe>>(){}.getType();
            List<CustomRecipe> recipes = gson.fromJson(reader, listType);
            
            if (recipes != null) {
                for (CustomRecipe recipe : recipes) {
                    customRecipes.put(recipe.getId(), recipe);
                }
                logger.info("Načteno " + recipes.size() + " receptů ze souboru " + file.getName());
            }
        }
    }

    /**
     * Organizuje recepty podle kategorií
     */
    private void organizeRecipesByCategory() {
        recipesByCategory.clear();
        
        for (CustomRecipe recipe : customRecipes.values()) {
            String category = recipe.getCategory() != null ? recipe.getCategory() : "misc";
            recipesByCategory.computeIfAbsent(category, k -> new ArrayList<>()).add(recipe);
        }
    }

    /**
     * Vytvoří základní předměty
     */
    private void createDefaultItems() {
        List<CustomItem> defaultItems = new ArrayList<>();
        
        // Enchanted Wheat
        CustomItem enchantedWheat = new CustomItem("ENCHANTED_WHEAT", "Enchanted Wheat", 
                                                  Material.WHEAT, ItemType.ENCHANTED_MATERIAL, ItemRarity.UNCOMMON);
        enchantedWheat.getDescription().add("A magical wheat that glows with");
        enchantedWheat.getDescription().add("enchanted energy.");
        defaultItems.add(enchantedWheat);
        
        // Enchanted Coal
        CustomItem enchantedCoal = new CustomItem("ENCHANTED_COAL", "Enchanted Coal", 
                                                 Material.COAL, ItemType.ENCHANTED_MATERIAL, ItemRarity.UNCOMMON);
        enchantedCoal.getDescription().add("Coal infused with magical energy.");
        defaultItems.add(enchantedCoal);
        
        // Aspect of the End
        CustomItem aspectOfTheEnd = new CustomItem("ASPECT_OF_THE_END", "Aspect of the End", 
                                                  Material.DIAMOND_SWORD, ItemType.SWORD, ItemRarity.RARE);
        aspectOfTheEnd.getStats().setDamage(100);
        aspectOfTheEnd.getStats().setStrength(100);
        aspectOfTheEnd.getDescription().add("A powerful sword from the End.");
        aspectOfTheEnd.getAbilities().add("Teleport: Right-click to teleport 8 blocks ahead!");
        defaultItems.add(aspectOfTheEnd);
        
        // Uložit do souboru
        saveItemsToFile(defaultItems, "default_items.json");
        
        // Přidat do mapy
        for (CustomItem item : defaultItems) {
            customItems.put(item.getId(), item);
        }
    }

    /**
     * Vytvoří základní recepty
     */
    private void createDefaultRecipes() {
        List<CustomRecipe> defaultRecipes = new ArrayList<>();
        
        // Enchanted Wheat recept (64 wheat -> 1 enchanted wheat)
        CustomRecipe enchantedWheatRecipe = new CustomRecipe("ENCHANTED_WHEAT_RECIPE", "Enchanted Wheat", 
                                                            RecipeType.SHAPELESS, getCustomItem("ENCHANTED_WHEAT"));
        enchantedWheatRecipe.addShapelessIngredient(Material.WHEAT, 64);
        enchantedWheatRecipe.setCategory("farming");
        enchantedWheatRecipe.getUnlockRequirements().add("WHEAT_COLLECTION_5");
        defaultRecipes.add(enchantedWheatRecipe);
        
        // Enchanted Coal recept (64 coal -> 1 enchanted coal)
        CustomRecipe enchantedCoalRecipe = new CustomRecipe("ENCHANTED_COAL_RECIPE", "Enchanted Coal", 
                                                           RecipeType.SHAPELESS, getCustomItem("ENCHANTED_COAL"));
        enchantedCoalRecipe.addShapelessIngredient(Material.COAL, 64);
        enchantedCoalRecipe.setCategory("mining");
        enchantedCoalRecipe.getUnlockRequirements().add("COAL_COLLECTION_5");
        defaultRecipes.add(enchantedCoalRecipe);
        
        // Uložit do souboru
        saveRecipesToFile(defaultRecipes, "default_recipes.json");
        
        // Přidat do mapy
        for (CustomRecipe recipe : defaultRecipes) {
            customRecipes.put(recipe.getId(), recipe);
        }
    }

    /**
     * Uloží předměty do souboru
     */
    public CompletableFuture<Boolean> saveItemsToFile(List<CustomItem> items, String fileName) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                File file = new File(itemsFolder, fileName);
                try (FileWriter writer = new FileWriter(file)) {
                    gson.toJson(items, writer);
                    return true;
                }
            } catch (IOException e) {
                logger.severe("Chyba při ukládání předmětů do souboru " + fileName + ": " + e.getMessage());
                return false;
            }
        });
    }

    /**
     * Uloží recepty do souboru
     */
    public CompletableFuture<Boolean> saveRecipesToFile(List<CustomRecipe> recipes, String fileName) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                File file = new File(recipesFolder, fileName);
                try (FileWriter writer = new FileWriter(file)) {
                    gson.toJson(recipes, writer);
                    return true;
                }
            } catch (IOException e) {
                logger.severe("Chyba při ukládání receptů do souboru " + fileName + ": " + e.getMessage());
                return false;
            }
        });
    }

    /**
     * Získá custom předmět podle ID
     */
    public CustomItem getCustomItem(String id) {
        return customItems.get(id);
    }

    /**
     * Získá custom recept podle ID
     */
    public CustomRecipe getCustomRecipe(String id) {
        return customRecipes.get(id);
    }

    /**
     * Získá všechny custom předměty
     */
    public Collection<CustomItem> getAllCustomItems() {
        return customItems.values();
    }

    /**
     * Získá všechny custom recepty
     */
    public Collection<CustomRecipe> getAllCustomRecipes() {
        return customRecipes.values();
    }

    /**
     * Získá recepty podle kategorie
     */
    public List<CustomRecipe> getRecipesByCategory(String category) {
        return recipesByCategory.getOrDefault(category, new ArrayList<>());
    }

    /**
     * Získá všechny kategorie receptů
     */
    public Set<String> getRecipeCategories() {
        return recipesByCategory.keySet();
    }

    /**
     * Zkontroluje, jestli ItemStack je custom předmět
     */
    public boolean isCustomItem(ItemStack item) {
        if (item == null || !item.hasItemMeta()) return false;
        
        // TODO: Implementovat kontrolu podle NBT dat
        // Pro teď kontrola podle display name
        return item.getItemMeta().hasDisplayName();
    }

    /**
     * Získá ID custom předmětu z ItemStack
     */
    public String getCustomItemId(ItemStack item) {
        if (!isCustomItem(item)) return null;
        
        // TODO: Implementovat získání ID z NBT dat
        // Pro teď zjednodušená implementace
        String displayName = item.getItemMeta().getDisplayName();
        for (CustomItem customItem : customItems.values()) {
            if (displayName.contains(customItem.getDisplayName())) {
                return customItem.getId();
            }
        }
        
        return null;
    }

    /**
     * Najde recept pro crafting grid
     */
    public CustomRecipe findMatchingRecipe(ItemStack[] craftingGrid) {
        for (CustomRecipe recipe : customRecipes.values()) {
            if (recipe.isEnabled() && recipe.matches(craftingGrid)) {
                return recipe;
            }
        }
        return null;
    }

    /**
     * Registruje nový custom předmět
     */
    public void registerCustomItem(CustomItem item) {
        customItems.put(item.getId(), item);
    }

    /**
     * Registruje nový custom recept
     */
    public void registerCustomRecipe(CustomRecipe recipe) {
        customRecipes.put(recipe.getId(), recipe);
        
        // Přidej do kategorie
        String category = recipe.getCategory() != null ? recipe.getCategory() : "misc";
        recipesByCategory.computeIfAbsent(category, k -> new ArrayList<>()).add(recipe);
    }

    /**
     * Odregistruje custom předmět
     */
    public void unregisterCustomItem(String id) {
        customItems.remove(id);
    }

    /**
     * Odregistruje custom recept
     */
    public void unregisterCustomRecipe(String id) {
        CustomRecipe recipe = customRecipes.remove(id);
        if (recipe != null) {
            String category = recipe.getCategory() != null ? recipe.getCategory() : "misc";
            List<CustomRecipe> categoryRecipes = recipesByCategory.get(category);
            if (categoryRecipes != null) {
                categoryRecipes.remove(recipe);
            }
        }
    }

    /**
     * Znovu načte všechny předměty a recepty
     */
    public void reload() {
        customItems.clear();
        customRecipes.clear();
        recipesByCategory.clear();
        
        loadAllItems();
        loadAllRecipes();
        
        logger.info("CustomItemManager znovu načten s " + customItems.size() +
                   " předměty a " + customRecipes.size() + " recepty");
    }

    /**
     * Vytvoří ItemStack z custom předmětu
     */
    public ItemStack createItemStack(String customItemId) {
        return createItemStack(customItemId, 1);
    }

    public ItemStack createItemStack(String customItemId, int amount) {
        CustomItem customItem = getCustomItem(customItemId);
        if (customItem != null) {
            return customItem.createItemStack(amount);
        }
        return null;
    }
}
