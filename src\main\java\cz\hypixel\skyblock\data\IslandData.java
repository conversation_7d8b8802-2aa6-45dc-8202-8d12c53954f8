package cz.hypixel.skyblock.data;

import org.bukkit.Location;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * Data třída pro ostrov
 */
public class IslandData {
    
    private int id;
    private UUID ownerUuid;
    private String islandName;
    private String worldName;
    private double spawnX;
    private double spawnY;
    private double spawnZ;
    private int size;
    private long createdAt;
    private long updatedAt;
    private List<IslandMember> members;
    
    public IslandData() {
        this.members = new ArrayList<>();
        this.size = 100; // Výchozí velikost ostrova
        this.spawnY = 100; // Výchozí výška spawnu
    }
    
    public IslandData(UUID ownerUuid, String worldName, Location spawnLocation) {
        this();
        this.ownerUuid = ownerUuid;
        this.worldName = worldName;
        this.spawnX = spawnLocation.getX();
        this.spawnY = spawnLocation.getY();
        this.spawnZ = spawnLocation.getZ();
        this.createdAt = System.currentTimeMillis();
        this.updatedAt = System.currentTimeMillis();
    }
    
    // Gettery a settery
    public int getId() {
        return id;
    }
    
    public void setId(int id) {
        this.id = id;
    }
    
    public UUID getOwnerUuid() {
        return ownerUuid;
    }
    
    public void setOwnerUuid(UUID ownerUuid) {
        this.ownerUuid = ownerUuid;
    }
    
    public String getIslandName() {
        return islandName;
    }
    
    public void setIslandName(String islandName) {
        this.islandName = islandName;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public String getWorldName() {
        return worldName;
    }
    
    public void setWorldName(String worldName) {
        this.worldName = worldName;
    }
    
    public double getSpawnX() {
        return spawnX;
    }
    
    public void setSpawnX(double spawnX) {
        this.spawnX = spawnX;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public double getSpawnY() {
        return spawnY;
    }
    
    public void setSpawnY(double spawnY) {
        this.spawnY = spawnY;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public double getSpawnZ() {
        return spawnZ;
    }
    
    public void setSpawnZ(double spawnZ) {
        this.spawnZ = spawnZ;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public Location getSpawnLocation() {
        return new Location(
            org.bukkit.Bukkit.getWorld(worldName),
            spawnX, spawnY, spawnZ
        );
    }
    
    public void setSpawnLocation(Location location) {
        this.spawnX = location.getX();
        this.spawnY = location.getY();
        this.spawnZ = location.getZ();
        this.worldName = location.getWorld().getName();
        this.updatedAt = System.currentTimeMillis();
    }
    
    public int getSize() {
        return size;
    }
    
    public void setSize(int size) {
        this.size = size;
        this.updatedAt = System.currentTimeMillis();
    }
    
    public long getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(long createdAt) {
        this.createdAt = createdAt;
    }
    
    public long getUpdatedAt() {
        return updatedAt;
    }
    
    public void setUpdatedAt(long updatedAt) {
        this.updatedAt = updatedAt;
    }
    
    public List<IslandMember> getMembers() {
        return new ArrayList<>(members);
    }
    
    public void setMembers(List<IslandMember> members) {
        this.members = new ArrayList<>(members);
        this.updatedAt = System.currentTimeMillis();
    }
    
    // Utility metody pro členy
    public void addMember(UUID playerUuid, IslandRole role) {
        members.add(new IslandMember(playerUuid, role, System.currentTimeMillis()));
        this.updatedAt = System.currentTimeMillis();
    }
    
    public boolean removeMember(UUID playerUuid) {
        boolean removed = members.removeIf(member -> member.getPlayerUuid().equals(playerUuid));
        if (removed) {
            this.updatedAt = System.currentTimeMillis();
        }
        return removed;
    }
    
    public IslandMember getMember(UUID playerUuid) {
        return members.stream()
                .filter(member -> member.getPlayerUuid().equals(playerUuid))
                .findFirst()
                .orElse(null);
    }
    
    public boolean isMember(UUID playerUuid) {
        return ownerUuid.equals(playerUuid) || getMember(playerUuid) != null;
    }
    
    public boolean isOwner(UUID playerUuid) {
        return ownerUuid.equals(playerUuid);
    }
    
    public boolean hasPermission(UUID playerUuid, String permission) {
        if (isOwner(playerUuid)) {
            return true; // Vlastník má všechna oprávnění
        }
        
        IslandMember member = getMember(playerUuid);
        if (member == null) {
            return false; // Není člen ostrova
        }
        
        return member.getRole().hasPermission(permission);
    }
    
    public int getMemberCount() {
        return members.size() + 1; // +1 za vlastníka
    }
    
    public String getDisplayName() {
        return islandName != null ? islandName : "Ostrov hráče";
    }
    
    @Override
    public String toString() {
        return "IslandData{" +
                "id=" + id +
                ", ownerUuid=" + ownerUuid +
                ", islandName='" + islandName + '\'' +
                ", worldName='" + worldName + '\'' +
                ", members=" + members.size() +
                '}';
    }
    
    /**
     * Třída pro člena ostrova
     */
    public static class IslandMember {
        private UUID playerUuid;
        private IslandRole role;
        private long joinedAt;
        
        public IslandMember(UUID playerUuid, IslandRole role, long joinedAt) {
            this.playerUuid = playerUuid;
            this.role = role;
            this.joinedAt = joinedAt;
        }
        
        public UUID getPlayerUuid() {
            return playerUuid;
        }
        
        public void setPlayerUuid(UUID playerUuid) {
            this.playerUuid = playerUuid;
        }
        
        public IslandRole getRole() {
            return role;
        }
        
        public void setRole(IslandRole role) {
            this.role = role;
        }
        
        public long getJoinedAt() {
            return joinedAt;
        }
        
        public void setJoinedAt(long joinedAt) {
            this.joinedAt = joinedAt;
        }
        
        @Override
        public String toString() {
            return "IslandMember{" +
                    "playerUuid=" + playerUuid +
                    ", role=" + role +
                    ", joinedAt=" + joinedAt +
                    '}';
        }
    }
}
