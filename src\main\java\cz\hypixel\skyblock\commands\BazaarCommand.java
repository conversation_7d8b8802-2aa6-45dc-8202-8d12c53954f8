package cz.hypixel.skyblock.commands;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.BazaarOrderType;
import cz.hypixel.skyblock.gui.BazaarGUI;
import cz.hypixel.skyblock.managers.BankManager;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

/**
 * Příkaz pro Bazaar systém
 */
public class BazaarCommand implements CommandExecutor {

    private final HypixelSkyBlockCZ plugin;
    private final BazaarGUI bazaarGUI;

    public BazaarCommand(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.bazaarGUI = new BazaarGUI(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cTento příkaz může použít pouze hráč!");
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            // Otevři Bazaar GUI
            bazaarGUI.openBazaar(player);
            return true;
        }

        String action = args[0].toLowerCase();

        switch (action) {
            case "buy":
                handleBuyOrder(player, args);
                break;
                
            case "sell":
                handleSellOrder(player, args);
                break;
                
            case "instant":
                handleInstantAction(player, args);
                break;
                
            case "orders":
            case "my":
                handleMyOrders(player);
                break;
                
            case "help":
                showHelp(player);
                break;
                
            default:
                player.sendMessage("§cNeznámý příkaz! Použij §e/bazaar help §cpro nápovědu.");
                break;
        }

        return true;
    }

    /**
     * Zpracuje vytvoření buy orderu
     */
    private void handleBuyOrder(Player player, String[] args) {
        if (args.length < 4) {
            player.sendMessage("§cPoužití: /bazaar buy <předmět> <množství> <cena_za_kus>");
            player.sendMessage("§7Příklad: /bazaar buy WHEAT 64 10");
            return;
        }

        String itemName = args[1].toUpperCase();
        
        if (!plugin.getBazaarManager().isBazaarItem(itemName)) {
            player.sendMessage("§cTento předmět není dostupný v Bazaaru!");
            return;
        }

        int amount;
        try {
            amount = Integer.parseInt(args[2]);
        } catch (NumberFormatException e) {
            player.sendMessage("§cNeplatné množství!");
            return;
        }

        double pricePerUnit = BankManager.parseCoins(args[3]);
        if (pricePerUnit <= 0) {
            player.sendMessage("§cNeplatná cena!");
            return;
        }

        plugin.getBazaarManager().createOrder(player, itemName, BazaarOrderType.BUY, amount, pricePerUnit)
            .thenAccept(success -> {
                if (!success) {
                    player.sendMessage("§cNepodařilo se vytvořit buy order!");
                }
            });
    }

    /**
     * Zpracuje vytvoření sell orderu
     */
    private void handleSellOrder(Player player, String[] args) {
        if (args.length < 4) {
            player.sendMessage("§cPoužití: /bazaar sell <předmět> <množství> <cena_za_kus>");
            player.sendMessage("§7Příklad: /bazaar sell WHEAT 64 15");
            return;
        }

        String itemName = args[1].toUpperCase();
        
        if (!plugin.getBazaarManager().isBazaarItem(itemName)) {
            player.sendMessage("§cTento předmět není dostupný v Bazaaru!");
            return;
        }

        int amount;
        try {
            amount = Integer.parseInt(args[2]);
        } catch (NumberFormatException e) {
            player.sendMessage("§cNeplatné množství!");
            return;
        }

        double pricePerUnit = BankManager.parseCoins(args[3]);
        if (pricePerUnit <= 0) {
            player.sendMessage("§cNeplatná cena!");
            return;
        }

        plugin.getBazaarManager().createOrder(player, itemName, BazaarOrderType.SELL, amount, pricePerUnit)
            .thenAccept(success -> {
                if (!success) {
                    player.sendMessage("§cNepodařilo se vytvořit sell order!");
                }
            });
    }

    /**
     * Zpracuje instant akce
     */
    private void handleInstantAction(Player player, String[] args) {
        if (args.length < 4) {
            player.sendMessage("§cPoužití: /bazaar instant <buy/sell> <předmět> <množství>");
            player.sendMessage("§7Příklad: /bazaar instant buy WHEAT 64");
            player.sendMessage("§7Příklad: /bazaar instant sell WHEAT 64");
            return;
        }

        String type = args[1].toLowerCase();
        String itemName = args[2].toUpperCase();
        
        if (!plugin.getBazaarManager().isBazaarItem(itemName)) {
            player.sendMessage("§cTento předmět není dostupný v Bazaaru!");
            return;
        }

        int amount;
        try {
            amount = Integer.parseInt(args[3]);
        } catch (NumberFormatException e) {
            player.sendMessage("§cNeplatné množství!");
            return;
        }

        if (type.equals("buy")) {
            plugin.getBazaarManager().instantBuy(player, itemName, amount)
                .thenAccept(success -> {
                    if (!success) {
                        player.sendMessage("§cNepodařilo se koupit předměty!");
                    }
                });
        } else if (type.equals("sell")) {
            plugin.getBazaarManager().instantSell(player, itemName, amount)
                .thenAccept(success -> {
                    if (!success) {
                        player.sendMessage("§cNepodařilo se prodat předměty!");
                    }
                });
        } else {
            player.sendMessage("§cNeplatný typ! Použij 'buy' nebo 'sell'.");
        }
    }

    /**
     * Zobrazí ordery hráče
     */
    private void handleMyOrders(Player player) {
        plugin.getBazaarManager().getPlayerOrders(player.getUniqueId())
            .thenAccept(orders -> {
                player.sendMessage("§6§l=== MOJE BAZAAR ORDERY ===");
                
                if (orders.isEmpty()) {
                    player.sendMessage("§7Nemáš žádné ordery.");
                    return;
                }

                for (int i = 0; i < Math.min(orders.size(), 10); i++) {
                    var order = orders.get(i);
                    String type = order.getOrderType().getColoredName();
                    String status = order.getStatus().getColoredName();
                    
                    player.sendMessage("§7[§e" + order.getId() + "§7] " + 
                                     type + " §e" + order.getItemName() + " §7- " + status);
                    
                    if (order.isActive()) {
                        player.sendMessage("  §7Množství: §e" + order.getRemainingAmount() + "§7/§e" + order.getAmount());
                        player.sendMessage("  §7Cena za kus: §e" + BankManager.formatCoins(order.getPricePerUnit()));
                        player.sendMessage("  §7Progress: " + order.getProgressFormatted());
                    }
                }
                
                if (orders.size() > 10) {
                    player.sendMessage("§7... a " + (orders.size() - 10) + " dalších");
                }
            });
    }

    /**
     * Zobrazí nápovědu
     */
    private void showHelp(Player player) {
        player.sendMessage("§6§l=== BAZAAR - NÁPOVĚDA ===");
        player.sendMessage("§e/bazaar §7- Otevře Bazaar GUI");
        player.sendMessage("§e/bazaar buy <předmět> <množství> <cena> §7- Vytvoří buy order");
        player.sendMessage("§e/bazaar sell <předmět> <množství> <cena> §7- Vytvoří sell order");
        player.sendMessage("§e/bazaar instant buy <předmět> <množství> §7- Instant buy");
        player.sendMessage("§e/bazaar instant sell <předmět> <množství> §7- Instant sell");
        player.sendMessage("§e/bazaar orders §7- Zobrazí tvoje ordery");
        player.sendMessage("");
        player.sendMessage("§7Příklady:");
        player.sendMessage("§7- §e/bazaar buy WHEAT 64 10 §7- Buy order na 64 wheat za 10 mincí/kus");
        player.sendMessage("§7- §e/bazaar sell COAL 128 5 §7- Sell order na 128 coal za 5 mincí/kus");
        player.sendMessage("§7- §e/bazaar instant buy WHEAT 64 §7- Koupí 64 wheat okamžitě");
        player.sendMessage("§7- §e/bazaar instant sell COAL 128 §7- Prodá 128 coal okamžitě");
        player.sendMessage("");
        player.sendMessage("§7Dostupné předměty: §eWheat, Coal, Iron, Gold, Diamond, atd.");
        player.sendMessage("§7Použij GUI pro snadnější ovládání!");
    }
}
