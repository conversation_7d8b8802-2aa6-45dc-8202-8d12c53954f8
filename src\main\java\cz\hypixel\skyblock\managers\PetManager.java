package cz.hypixel.skyblock.managers;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.Pet;
import cz.hypixel.skyblock.data.PetType;
import cz.hypixel.skyblock.data.PetRarity;
import cz.hypixel.skyblock.database.PlayerData;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

import javax.sql.DataSource;
import java.sql.*;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;

/**
 * Manager pro Pet systém podle Hypixel SkyBlock
 */
public class PetManager {

    private final HypixelSkyBlockCZ plugin;
    private final Logger logger;
    private final DataSource dataSource;
    private final Map<UUID, List<Pet>> playerPetsCache;
    private final Map<UUID, Pet> activePetsCache;

    public PetManager(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.dataSource = plugin.getPlayerDataManager().getDataSource();
        this.playerPetsCache = new HashMap<>();
        this.activePetsCache = new HashMap<>();
        
        logger.info("PetManager inicializován");
    }

    /**
     * Dá hráči nového mazlíčka
     */
    public CompletableFuture<Boolean> givePet(Player player, PetType petType, PetRarity rarity) {
        return CompletableFuture.supplyAsync(() -> {
            UUID playerUuid = player.getUniqueId();
            
            // Vytvoř nového mazlíčka
            Pet pet = new Pet(playerUuid, petType, rarity);
            
            // Ulož do databáze
            String sql = """
                INSERT INTO pets (owner_uuid, pet_type, rarity, custom_name, experience, level, 
                                active, obtained_at, metadata)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;
            
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql, Statement.RETURN_GENERATED_KEYS)) {
                
                stmt.setString(1, playerUuid.toString());
                stmt.setString(2, pet.getPetType().name());
                stmt.setString(3, pet.getRarity().name());
                stmt.setString(4, pet.getCustomName());
                stmt.setDouble(5, pet.getExperience());
                stmt.setInt(6, pet.getLevel());
                stmt.setBoolean(7, pet.isActive());
                stmt.setLong(8, pet.getObtainedAt());
                stmt.setString(9, serializeMetadata(pet.getMetadata()));
                
                int result = stmt.executeUpdate();
                
                if (result > 0) {
                    // Získej vygenerované ID
                    try (ResultSet rs = stmt.getGeneratedKeys()) {
                        if (rs.next()) {
                            pet.setId(rs.getInt(1));
                        }
                    }
                    
                    // Přidej do cache
                    playerPetsCache.computeIfAbsent(playerUuid, k -> new ArrayList<>()).add(pet);
                    
                    player.sendMessage("§aZískal jsi nového mazlíčka!");
                    player.sendMessage("§7Typ: " + pet.getPetType().getColoredName());
                    player.sendMessage("§7Rarity: " + pet.getRarity().getColoredName());
                    player.sendMessage("§7Level: §e" + pet.getLevel());
                    
                    return true;
                }
                
            } catch (SQLException e) {
                logger.severe("Chyba při přidávání mazlíčka: " + e.getMessage());
            }
            
            return false;
        });
    }

    /**
     * Aktivuje mazlíčka
     */
    public CompletableFuture<Boolean> activatePet(Player player, int petId) {
        return CompletableFuture.supplyAsync(() -> {
            UUID playerUuid = player.getUniqueId();
            
            // Načti mazlíčka
            Pet pet = getPetById(petId).join();
            if (pet == null || !pet.getOwnerUuid().equals(playerUuid)) {
                player.sendMessage("§cMazlíček nenalezen!");
                return false;
            }
            
            // Deaktivuj současného mazlíčka
            Pet currentPet = getActivePet(playerUuid).join();
            if (currentPet != null) {
                currentPet.setActive(false);
                updatePetInDatabase(currentPet);
            }
            
            // Aktivuj nového mazlíčka
            pet.setActive(true);
            updatePetInDatabase(pet);
            
            // Aktualizuj cache
            activePetsCache.put(playerUuid, pet);
            
            player.sendMessage("§aAktivoval jsi mazlíčka: " + pet.getDisplayName());
            
            // Aplikuj stat bonusy
            applyPetBonuses(player, pet);
            
            return true;
        });
    }

    /**
     * Deaktivuje mazlíčka
     */
    public CompletableFuture<Boolean> deactivatePet(Player player) {
        return CompletableFuture.supplyAsync(() -> {
            UUID playerUuid = player.getUniqueId();
            
            Pet currentPet = getActivePet(playerUuid).join();
            if (currentPet == null) {
                player.sendMessage("§cNemáš aktivního mazlíčka!");
                return false;
            }
            
            // Deaktivuj mazlíčka
            currentPet.setActive(false);
            updatePetInDatabase(currentPet);
            
            // Odeber z cache
            activePetsCache.remove(playerUuid);
            
            player.sendMessage("§cDeaktivoval jsi mazlíčka: " + currentPet.getDisplayName());
            
            // Odeber stat bonusy
            removePetBonuses(player, currentPet);
            
            return true;
        });
    }

    /**
     * Přidá experience mazlíčkovi
     */
    public CompletableFuture<Void> addPetExperience(UUID playerUuid, double experience, String source) {
        return CompletableFuture.runAsync(() -> {
            Pet activePet = getActivePet(playerUuid).join();
            if (activePet == null || activePet.isMaxLevel()) {
                return;
            }
            
            int oldLevel = activePet.getLevel();
            activePet.addExperience(experience);
            int newLevel = activePet.getLevel();
            
            // Aktualizuj v databázi
            updatePetInDatabase(activePet);
            
            Player player = Bukkit.getPlayer(playerUuid);
            if (player != null) {
                // Zobraz experience gain
                if (experience > 0) {
                    player.sendMessage("§b+" + String.format("%.1f", experience) + " Pet XP §7(" + source + ")");
                }
                
                // Zkontroluj level up
                if (newLevel > oldLevel) {
                    player.sendMessage("§6§l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
                    player.sendMessage("§6§l                    PET LEVEL UP!");
                    player.sendMessage("");
                    player.sendMessage("§7Tvůj " + activePet.getPetType().getColoredName() + " §7dosáhl levelu §e" + newLevel + "§7!");
                    player.sendMessage("");
                    player.sendMessage("§6§l▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬▬");
                    
                    // Znovu aplikuj bonusy s novým levelem
                    applyPetBonuses(player, activePet);
                }
            }
        });
    }

    /**
     * Získá aktivního mazlíčka hráče
     */
    public CompletableFuture<Pet> getActivePet(UUID playerUuid) {
        // Zkontroluj cache
        Pet cached = activePetsCache.get(playerUuid);
        if (cached != null) {
            return CompletableFuture.completedFuture(cached);
        }
        
        return CompletableFuture.supplyAsync(() -> {
            String sql = """
                SELECT id, owner_uuid, pet_type, rarity, custom_name, experience, level, 
                       active, obtained_at, metadata
                FROM pets 
                WHERE owner_uuid = ? AND active = true
            """;
            
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setString(1, playerUuid.toString());
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        Pet pet = createPetFromResultSet(rs);
                        activePetsCache.put(playerUuid, pet);
                        return pet;
                    }
                }
                
            } catch (SQLException e) {
                logger.severe("Chyba při načítání aktivního mazlíčka: " + e.getMessage());
            }
            
            return null;
        });
    }

    /**
     * Získá všechny mazlíčky hráče
     */
    public CompletableFuture<List<Pet>> getPlayerPets(UUID playerUuid) {
        // Zkontroluj cache
        List<Pet> cached = playerPetsCache.get(playerUuid);
        if (cached != null) {
            return CompletableFuture.completedFuture(new ArrayList<>(cached));
        }
        
        return CompletableFuture.supplyAsync(() -> {
            List<Pet> pets = new ArrayList<>();
            
            String sql = """
                SELECT id, owner_uuid, pet_type, rarity, custom_name, experience, level, 
                       active, obtained_at, metadata
                FROM pets 
                WHERE owner_uuid = ? 
                ORDER BY active DESC, level DESC, obtained_at DESC
            """;
            
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setString(1, playerUuid.toString());
                
                try (ResultSet rs = stmt.executeQuery()) {
                    while (rs.next()) {
                        Pet pet = createPetFromResultSet(rs);
                        pets.add(pet);
                        
                        if (pet.isActive()) {
                            activePetsCache.put(playerUuid, pet);
                        }
                    }
                }
                
            } catch (SQLException e) {
                logger.severe("Chyba při načítání mazlíčků hráče: " + e.getMessage());
            }
            
            playerPetsCache.put(playerUuid, pets);
            return pets;
        });
    }

    /**
     * Získá mazlíčka podle ID
     */
    public CompletableFuture<Pet> getPetById(int petId) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = """
                SELECT id, owner_uuid, pet_type, rarity, custom_name, experience, level, 
                       active, obtained_at, metadata
                FROM pets 
                WHERE id = ?
            """;
            
            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {
                
                stmt.setInt(1, petId);
                
                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        return createPetFromResultSet(rs);
                    }
                }
                
            } catch (SQLException e) {
                logger.severe("Chyba při načítání mazlíčka: " + e.getMessage());
            }
            
            return null;
        });
    }

    /**
     * Vytvoří Pet z ResultSet
     */
    private Pet createPetFromResultSet(ResultSet rs) throws SQLException {
        Pet pet = new Pet();
        pet.setId(rs.getInt("id"));
        pet.setOwnerUuid(UUID.fromString(rs.getString("owner_uuid")));
        pet.setPetType(PetType.fromString(rs.getString("pet_type")));
        pet.setRarity(PetRarity.fromString(rs.getString("rarity")));
        pet.setCustomName(rs.getString("custom_name"));
        pet.setExperience(rs.getDouble("experience"));
        pet.setLevel(rs.getInt("level"));
        pet.setActive(rs.getBoolean("active"));
        pet.setObtainedAt(rs.getLong("obtained_at"));
        pet.setMetadata(deserializeMetadata(rs.getString("metadata")));
        
        return pet;
    }

    /**
     * Aktualizuje mazlíčka v databázi
     */
    private void updatePetInDatabase(Pet pet) {
        String sql = """
            UPDATE pets 
            SET custom_name = ?, experience = ?, level = ?, active = ?, metadata = ?
            WHERE id = ?
        """;
        
        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {
            
            stmt.setString(1, pet.getCustomName());
            stmt.setDouble(2, pet.getExperience());
            stmt.setInt(3, pet.getLevel());
            stmt.setBoolean(4, pet.isActive());
            stmt.setString(5, serializeMetadata(pet.getMetadata()));
            stmt.setInt(6, pet.getId());
            
            stmt.executeUpdate();
            
        } catch (SQLException e) {
            logger.warning("Chyba při aktualizaci mazlíčka: " + e.getMessage());
        }
    }

    /**
     * Aplikuje stat bonusy od mazlíčka
     */
    private void applyPetBonuses(Player player, Pet pet) {
        // TODO: Implementovat aplikaci stat bonusů
        // Toto by mělo být integrováno se stat systémem
        Map<String, Double> bonuses = pet.getStatBonuses();
        
        // Pro teď jen zobraz bonusy
        player.sendMessage("§7Aplikovány pet bonusy:");
        for (Map.Entry<String, Double> entry : bonuses.entrySet()) {
            player.sendMessage("§7- " + entry.getKey() + ": §a+" + String.format("%.1f", entry.getValue()));
        }
    }

    /**
     * Odebere stat bonusy od mazlíčka
     */
    private void removePetBonuses(Player player, Pet pet) {
        // TODO: Implementovat odebrání stat bonusů
        player.sendMessage("§7Odebrány pet bonusy.");
    }

    /**
     * Serializuje metadata do stringu
     */
    private String serializeMetadata(Map<String, Object> metadata) {
        if (metadata == null || metadata.isEmpty()) {
            return "{}";
        }
        
        // Zjednodušená serializace - v produkci by měla být robustnější
        StringBuilder sb = new StringBuilder("{");
        boolean first = true;
        for (Map.Entry<String, Object> entry : metadata.entrySet()) {
            if (!first) sb.append(",");
            sb.append("\"").append(entry.getKey()).append("\":\"").append(entry.getValue()).append("\"");
            first = false;
        }
        sb.append("}");
        
        return sb.toString();
    }

    /**
     * Deserializuje metadata ze stringu
     */
    private Map<String, Object> deserializeMetadata(String data) {
        Map<String, Object> metadata = new HashMap<>();
        
        if (data == null || data.equals("{}")) {
            return metadata;
        }
        
        // Zjednodušená deserializace - v produkci by měla být robustnější
        try {
            data = data.substring(1, data.length() - 1); // Odeber {}
            String[] pairs = data.split(",");
            
            for (String pair : pairs) {
                String[] keyValue = pair.split(":");
                if (keyValue.length == 2) {
                    String key = keyValue[0].replace("\"", "").trim();
                    String value = keyValue[1].replace("\"", "").trim();
                    metadata.put(key, value);
                }
            }
        } catch (Exception e) {
            logger.warning("Chyba při deserializaci pet metadata: " + e.getMessage());
        }
        
        return metadata;
    }
}
