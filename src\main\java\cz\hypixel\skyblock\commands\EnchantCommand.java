package cz.hypixel.skyblock.commands;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.SkyBlockEnchantment;
import cz.hypixel.skyblock.gui.EnchantingTableGUI;
import cz.hypixel.skyblock.managers.BankManager;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.Map;

/**
 * Příkaz pro Enchanting systém
 */
public class EnchantCommand implements CommandExecutor {

    private final HypixelSkyBlockCZ plugin;
    private final EnchantingTableGUI enchantingGUI;

    public EnchantCommand(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.enchantingGUI = new EnchantingTableGUI(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cTento příkaz může použít pouze hráč!");
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            // Otevři Enchanting Table GUI
            enchantingGUI.openEnchantingTable(player);
            return true;
        }

        String action = args[0].toLowerCase();

        switch (action) {
            case "add":
            case "apply":
                handleAddEnchant(player, args);
                break;
                
            case "remove":
                handleRemoveEnchant(player, args);
                break;
                
            case "list":
            case "show":
                handleShowEnchants(player);
                break;
                
            case "info":
                handleEnchantInfo(player, args);
                break;
                
            case "help":
                showHelp(player);
                break;
                
            default:
                player.sendMessage("§cNeznámý příkaz! Použij §e/enchant help §cpro nápovědu.");
                break;
        }

        return true;
    }

    /**
     * Zpracuje přidání enchantmentu
     */
    private void handleAddEnchant(Player player, String[] args) {
        if (args.length < 3) {
            player.sendMessage("§cPoužití: /enchant add <enchantment> <level>");
            player.sendMessage("§7Příklad: /enchant add sharpness 5");
            return;
        }

        ItemStack item = player.getInventory().getItemInMainHand();
        if (item.getType().isAir()) {
            player.sendMessage("§cMusíš držet předmět v ruce!");
            return;
        }

        String enchantName = args[1];
        SkyBlockEnchantment enchantment = SkyBlockEnchantment.fromString(enchantName);
        
        if (enchantment == null) {
            player.sendMessage("§cNeznámý enchantment: " + enchantName);
            player.sendMessage("§7Použij §e/enchant list §7pro seznam enchantmentů.");
            return;
        }

        int level;
        try {
            level = Integer.parseInt(args[2]);
        } catch (NumberFormatException e) {
            player.sendMessage("§cNeplatný level!");
            return;
        }

        plugin.getEnchantingManager().enchantItem(player, item, enchantment, level)
            .thenAccept(success -> {
                if (!success) {
                    player.sendMessage("§cNepodařilo se enchantovat předmět!");
                }
            });
    }

    /**
     * Zpracuje odebrání enchantmentu
     */
    private void handleRemoveEnchant(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cPoužití: /enchant remove <enchantment>");
            player.sendMessage("§7Příklad: /enchant remove sharpness");
            return;
        }

        ItemStack item = player.getInventory().getItemInMainHand();
        if (item.getType().isAir()) {
            player.sendMessage("§cMusíš držet předmět v ruce!");
            return;
        }

        String enchantName = args[1];
        SkyBlockEnchantment enchantment = SkyBlockEnchantment.fromString(enchantName);
        
        if (enchantment == null) {
            player.sendMessage("§cNeznámý enchantment: " + enchantName);
            return;
        }

        plugin.getEnchantingManager().removeEnchantment(player, item, enchantment)
            .thenAccept(success -> {
                if (!success) {
                    player.sendMessage("§cNepodařilo se odebrat enchantment!");
                }
            });
    }

    /**
     * Zobrazí enchantmenty na předmětu
     */
    private void handleShowEnchants(Player player) {
        ItemStack item = player.getInventory().getItemInMainHand();
        if (item.getType().isAir()) {
            player.sendMessage("§cMusíš držet předmět v ruce!");
            return;
        }

        Map<SkyBlockEnchantment, Integer> enchants = plugin.getEnchantingManager().getEnchantments(item);
        
        player.sendMessage("§5§l=== ENCHANTMENTY ===");
        player.sendMessage("§7Předmět: §e" + item.getType().name());
        
        if (enchants.isEmpty()) {
            player.sendMessage("§7Žádné enchantmenty");
        } else {
            for (Map.Entry<SkyBlockEnchantment, Integer> entry : enchants.entrySet()) {
                SkyBlockEnchantment enchant = entry.getKey();
                int level = entry.getValue();
                player.sendMessage("§7- " + enchant.getFormattedName(level) + " §7- " + enchant.getDescription(level));
            }
        }
    }

    /**
     * Zobrazí informace o enchantmentu
     */
    private void handleEnchantInfo(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cPoužití: /enchant info <enchantment>");
            player.sendMessage("§7Příklad: /enchant info sharpness");
            return;
        }

        String enchantName = args[1];
        SkyBlockEnchantment enchantment = SkyBlockEnchantment.fromString(enchantName);
        
        if (enchantment == null) {
            player.sendMessage("§cNeznámý enchantment: " + enchantName);
            return;
        }

        player.sendMessage("§5§l=== " + enchantment.getColoredName().toUpperCase() + " ===");
        player.sendMessage("§7Max Level: §e" + enchantment.getMaxLevel());
        player.sendMessage("");
        
        player.sendMessage("§7Levely:");
        for (int i = 1; i <= Math.min(enchantment.getMaxLevel(), 5); i++) {
            double cost = enchantment.getEnchantCost(i);
            player.sendMessage("§7- Level " + i + ": " + enchantment.getDescription(i) + 
                             " §7(Cena: §6" + BankManager.formatCoins(cost) + "§7)");
        }
        
        if (enchantment.getMaxLevel() > 5) {
            player.sendMessage("§7... a " + (enchantment.getMaxLevel() - 5) + " dalších levelů");
        }
        
        player.sendMessage("");
        player.sendMessage("§7Aplikovatelné na:");
        for (int i = 0; i < Math.min(enchantment.getApplicableItems().size(), 5); i++) {
            player.sendMessage("§7- " + enchantment.getApplicableItems().get(i).name());
        }
        
        if (enchantment.getApplicableItems().size() > 5) {
            player.sendMessage("§7... a " + (enchantment.getApplicableItems().size() - 5) + " dalších");
        }
    }

    /**
     * Zobrazí nápovědu
     */
    private void showHelp(Player player) {
        player.sendMessage("§5§l=== ENCHANTING - NÁPOVĚDA ===");
        player.sendMessage("§e/enchant §7- Otevře Enchanting Table GUI");
        player.sendMessage("§e/enchant add <enchantment> <level> §7- Přidá enchantment");
        player.sendMessage("§e/enchant remove <enchantment> §7- Odebere enchantment");
        player.sendMessage("§e/enchant list §7- Zobrazí enchantmenty na předmětu");
        player.sendMessage("§e/enchant info <enchantment> §7- Informace o enchantmentu");
        player.sendMessage("");
        player.sendMessage("§7Dostupné enchantmenty:");
        player.sendMessage("§7Combat: §cSharpness, Critical, Looting");
        player.sendMessage("§7Mining: §eEfficiency, Fortune, Smelting Touch");
        player.sendMessage("§7Farming: §aHarvesting, Turbo-Crop");
        player.sendMessage("§7Fishing: §bLuck of the Sea, Lure");
        player.sendMessage("§7Armor: §7Protection, Growth");
        player.sendMessage("§7Speciální: §dTelekinesis, §bUltimate Wise");
        player.sendMessage("");
        player.sendMessage("§7Použij GUI pro snadnější enchantování!");
    }
}
