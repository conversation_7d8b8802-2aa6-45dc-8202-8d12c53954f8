package cz.hypixel.skyblock.data;

/**
 * Enum pro stavy Bazaar orderů
 */
public enum BazaarOrderStatus {
    
    ACTIVE("Aktivní", "§a"),
    COMPLETED("Dokončen", "§e"),
    CANCELLED("Zrušen", "§c"),
    EXPIRED("<PERSON><PERSON><PERSON>r<PERSON><PERSON>", "§8");
    
    private final String displayName;
    private final String colorCode;
    
    BazaarOrderStatus(String displayName, String colorCode) {
        this.displayName = displayName;
        this.colorCode = colorCode;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getColorCode() {
        return colorCode;
    }
    
    public String getColoredName() {
        return colorCode + displayName;
    }
    
    /**
     * Získá status podle názvu
     */
    public static BazaarOrderStatus fromString(String name) {
        for (BazaarOrderStatus status : values()) {
            if (status.name().equalsIgnoreCase(name) || 
                status.displayName.equalsIgnoreCase(name)) {
                return status;
            }
        }
        return ACTIVE; // Výchozí status
    }
}
