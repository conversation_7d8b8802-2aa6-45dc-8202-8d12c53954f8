package cz.hypixel.skyblock.commands;

import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.data.CustomItem;
import cz.hypixel.skyblock.data.CustomRecipe;
import cz.hypixel.skyblock.gui.CraftingTableGUI;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.Collection;
import java.util.Set;

/**
 * Příkaz pro custom předměty a crafting
 */
public class CustomItemCommand implements CommandExecutor {

    private final HypixelSkyBlockCZ plugin;
    private final CraftingTableGUI craftingGUI;

    public CustomItemCommand(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.craftingGUI = new CraftingTableGUI(plugin);
    }

    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!(sender instanceof Player)) {
            sender.sendMessage("§cTento příkaz může použít pouze hráč!");
            return true;
        }

        Player player = (Player) sender;

        if (args.length == 0) {
            showHelp(player);
            return true;
        }

        String action = args[0].toLowerCase();

        switch (action) {
            case "give":
                handleGiveItem(player, args);
                break;
                
            case "list":
                handleListItems(player, args);
                break;
                
            case "info":
                handleItemInfo(player, args);
                break;
                
            case "craft":
            case "crafting":
                handleOpenCrafting(player);
                break;
                
            case "recipes":
                handleListRecipes(player, args);
                break;
                
            case "recipe":
                handleRecipeInfo(player, args);
                break;
                
            case "reload":
                handleReload(player);
                break;
                
            case "help":
                showHelp(player);
                break;
                
            default:
                player.sendMessage("§cNeznámý příkaz! Použij §e/customitem help §cpro nápovědu.");
                break;
        }

        return true;
    }

    /**
     * Zpracuje dávání custom předmětů
     */
    private void handleGiveItem(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cPoužití: /customitem give <id> [množství]");
            player.sendMessage("§7Příklad: /customitem give ENCHANTED_WHEAT 10");
            return;
        }

        String itemId = args[1].toUpperCase();
        int amount = 1;
        
        if (args.length >= 3) {
            try {
                amount = Integer.parseInt(args[2]);
            } catch (NumberFormatException e) {
                player.sendMessage("§cNeplatné množství!");
                return;
            }
        }

        CustomItem customItem = plugin.getCustomItemManager().getCustomItem(itemId);
        if (customItem == null) {
            player.sendMessage("§cCustom předmět s ID '" + itemId + "' neexistuje!");
            player.sendMessage("§7Použij §e/customitem list §7pro seznam předmětů.");
            return;
        }

        ItemStack item = customItem.createItemStack(amount);
        player.getInventory().addItem(item);
        
        player.sendMessage("§aZískal jsi §e" + amount + "x " + customItem.getDisplayName() + "§a!");
    }

    /**
     * Zobrazí seznam custom předmětů
     */
    private void handleListItems(Player player, String[] args) {
        Collection<CustomItem> items = plugin.getCustomItemManager().getAllCustomItems();
        
        player.sendMessage("§6§l=== CUSTOM PŘEDMĚTY ===");
        player.sendMessage("§7Celkem: §e" + items.size() + " předmětů");
        
        if (items.isEmpty()) {
            player.sendMessage("§7Žádné custom předměty nejsou registrovány.");
            return;
        }

        // Filtruj podle kategorie pokud je zadána
        String category = args.length > 1 ? args[1].toLowerCase() : null;
        
        int count = 0;
        for (CustomItem item : items) {
            if (category != null && !item.getItemType().getCategory().toLowerCase().contains(category)) {
                continue;
            }
            
            if (count >= 15) {
                player.sendMessage("§7... a " + (items.size() - count) + " dalších");
                break;
            }
            
            player.sendMessage("§7- §e" + item.getId() + " §7(" + 
                             item.getRarity().getColoredName() + " " + 
                             item.getItemType().getColoredName() + "§7)");
            count++;
        }
        
        if (category != null) {
            player.sendMessage("§7Filtrováno podle kategorie: §e" + category);
        }
        
        player.sendMessage("§7Použij §e/customitem info <id> §7pro detaily.");
    }

    /**
     * Zobrazí informace o custom předmětu
     */
    private void handleItemInfo(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cPoužití: /customitem info <id>");
            player.sendMessage("§7Příklad: /customitem info ENCHANTED_WHEAT");
            return;
        }

        String itemId = args[1].toUpperCase();
        CustomItem customItem = plugin.getCustomItemManager().getCustomItem(itemId);
        
        if (customItem == null) {
            player.sendMessage("§cCustom předmět s ID '" + itemId + "' neexistuje!");
            return;
        }

        player.sendMessage("§6§l=== " + customItem.getDisplayName().toUpperCase() + " ===");
        player.sendMessage("§7ID: §e" + customItem.getId());
        player.sendMessage("§7Materiál: §e" + customItem.getMaterial().name());
        player.sendMessage("§7Typ: " + customItem.getItemType().getColoredName());
        player.sendMessage("§7Rarity: " + customItem.getRarity().getFormattedName());
        player.sendMessage("§7Stackable: " + (customItem.isStackable() ? "§aAno" : "§cNe"));
        
        if (!customItem.getDescription().isEmpty()) {
            player.sendMessage("");
            player.sendMessage("§7Popis:");
            for (String line : customItem.getDescription()) {
                player.sendMessage("§7- " + line);
            }
        }
        
        if (!customItem.getStats().isEmpty()) {
            player.sendMessage("");
            player.sendMessage("§7Statistiky:");
            customItem.getStats().getAllStats().forEach((stat, value) -> {
                if (value > 0) {
                    player.sendMessage("§7- " + stat + ": §e+" + 
                                     (value % 1 == 0 ? String.valueOf(value.intValue()) : String.format("%.1f", value)));
                }
            });
        }
        
        if (!customItem.getAbilities().isEmpty()) {
            player.sendMessage("");
            player.sendMessage("§7Schopnosti:");
            for (String ability : customItem.getAbilities()) {
                player.sendMessage("§6- " + ability);
            }
        }
        
        player.sendMessage("");
        player.sendMessage("§7Hodnota: §6" + String.format("%.0f", customItem.getValue()) + " mincí");
    }

    /**
     * Otevře crafting table
     */
    private void handleOpenCrafting(Player player) {
        craftingGUI.openCraftingTable(player);
    }

    /**
     * Zobrazí seznam receptů
     */
    private void handleListRecipes(Player player, String[] args) {
        Collection<CustomRecipe> recipes = plugin.getCustomItemManager().getAllCustomRecipes();
        
        player.sendMessage("§6§l=== CUSTOM RECEPTY ===");
        player.sendMessage("§7Celkem: §e" + recipes.size() + " receptů");
        
        if (recipes.isEmpty()) {
            player.sendMessage("§7Žádné custom recepty nejsou registrovány.");
            return;
        }

        // Filtruj podle kategorie pokud je zadána
        String category = args.length > 1 ? args[1].toLowerCase() : null;
        
        if (category != null) {
            recipes = plugin.getCustomItemManager().getRecipesByCategory(category);
            player.sendMessage("§7Kategorie: §e" + category);
        } else {
            // Zobraz dostupné kategorie
            Set<String> categories = plugin.getCustomItemManager().getRecipeCategories();
            player.sendMessage("§7Dostupné kategorie: §e" + String.join(", ", categories));
        }
        
        int count = 0;
        for (CustomRecipe recipe : recipes) {
            if (count >= 15) {
                player.sendMessage("§7... a " + (recipes.size() - count) + " dalších");
                break;
            }
            
            String resultName = recipe.getResult() != null ? recipe.getResult().getDisplayName() : "Unknown";
            player.sendMessage("§7- §e" + recipe.getId() + " §7-> " + resultName + 
                             " §7(" + recipe.getRecipeType().getColoredName() + "§7)");
            count++;
        }
        
        player.sendMessage("§7Použij §e/customitem recipe <id> §7pro detaily.");
    }

    /**
     * Zobrazí informace o receptu
     */
    private void handleRecipeInfo(Player player, String[] args) {
        if (args.length < 2) {
            player.sendMessage("§cPoužití: /customitem recipe <id>");
            player.sendMessage("§7Příklad: /customitem recipe ENCHANTED_WHEAT_RECIPE");
            return;
        }

        String recipeId = args[1].toUpperCase();
        CustomRecipe recipe = plugin.getCustomItemManager().getCustomRecipe(recipeId);
        
        if (recipe == null) {
            player.sendMessage("§cRecept s ID '" + recipeId + "' neexistuje!");
            return;
        }

        player.sendMessage("§6§l=== " + recipe.getDisplayName().toUpperCase() + " ===");
        player.sendMessage("§7ID: §e" + recipe.getId());
        player.sendMessage("§7Typ: " + recipe.getRecipeType().getColoredName());
        player.sendMessage("§7Kategorie: §e" + (recipe.getCategory() != null ? recipe.getCategory() : "misc"));
        player.sendMessage("§7Enabled: " + (recipe.isEnabled() ? "§aAno" : "§cNe"));
        
        if (recipe.getResult() != null) {
            player.sendMessage("§7Výsledek: §e" + recipe.getResultAmount() + "x " + recipe.getResult().getDisplayName());
        }
        
        if (!recipe.getUnlockRequirements().isEmpty()) {
            player.sendMessage("");
            player.sendMessage("§7Unlock Requirements:");
            for (String requirement : recipe.getUnlockRequirements()) {
                player.sendMessage("§c- " + requirement);
            }
        }
        
        player.sendMessage("");
        player.sendMessage("§7Použij crafting table pro vytvoření tohoto předmětu!");
    }

    /**
     * Znovu načte custom předměty a recepty
     */
    private void handleReload(Player player) {
        if (!player.hasPermission("skyblock.admin")) {
            player.sendMessage("§cNemáš oprávnění k tomuto příkazu!");
            return;
        }
        
        plugin.getCustomItemManager().reload();
        player.sendMessage("§aCustom předměty a recepty byly znovu načteny!");
    }

    /**
     * Zobrazí nápovědu
     */
    private void showHelp(Player player) {
        player.sendMessage("§6§l=== CUSTOM PŘEDMĚTY - NÁPOVĚDA ===");
        player.sendMessage("§e/customitem give <id> [množství] §7- Dá custom předmět");
        player.sendMessage("§e/customitem list [kategorie] §7- Seznam custom předmětů");
        player.sendMessage("§e/customitem info <id> §7- Informace o předmětu");
        player.sendMessage("§e/customitem craft §7- Otevře crafting table");
        player.sendMessage("§e/customitem recipes [kategorie] §7- Seznam receptů");
        player.sendMessage("§e/customitem recipe <id> §7- Informace o receptu");
        
        if (player.hasPermission("skyblock.admin")) {
            player.sendMessage("§e/customitem reload §7- Znovu načte předměty a recepty");
        }
        
        player.sendMessage("");
        player.sendMessage("§7Příklady:");
        player.sendMessage("§7- §e/customitem give ENCHANTED_WHEAT 10");
        player.sendMessage("§7- §e/customitem list farming");
        player.sendMessage("§7- §e/customitem info ASPECT_OF_THE_END");
        player.sendMessage("§7- §e/customitem recipes mining");
        player.sendMessage("");
        player.sendMessage("§7Custom předměty mají speciální statistiky");
        player.sendMessage("§7a schopnosti podle Hypixel SkyBlock!");
    }
}
