package cz.hypixel.skyblock.data;

import java.util.HashMap;
import java.util.Map;

/**
 * Třída pro statistiky custom předmětů podle Hypixel SkyBlock
 */
public class ItemStats {
    
    private final Map<String, Double> stats;
    
    public ItemStats() {
        this.stats = new HashMap<>();
    }
    
    public ItemStats(Map<String, Double> stats) {
        this.stats = new HashMap<>(stats);
    }
    
    // Základní combat statistiky
    public double getDamage() {
        return stats.getOrDefault("damage", 0.0);
    }
    
    public void setDamage(double damage) {
        stats.put("damage", damage);
    }
    
    public double getStrength() {
        return stats.getOrDefault("strength", 0.0);
    }
    
    public void setStrength(double strength) {
        stats.put("strength", strength);
    }
    
    public double getCritChance() {
        return stats.getOrDefault("crit_chance", 0.0);
    }
    
    public void setCritChance(double critChance) {
        stats.put("crit_chance", critChance);
    }
    
    public double getCritDamage() {
        return stats.getOrDefault("crit_damage", 0.0);
    }
    
    public void setCritDamage(double critDamage) {
        stats.put("crit_damage", critDamage);
    }
    
    public double getAttackSpeed() {
        return stats.getOrDefault("attack_speed", 0.0);
    }
    
    public void setAttackSpeed(double attackSpeed) {
        stats.put("attack_speed", attackSpeed);
    }
    
    // Defensive statistiky
    public double getDefense() {
        return stats.getOrDefault("defense", 0.0);
    }
    
    public void setDefense(double defense) {
        stats.put("defense", defense);
    }
    
    public double getHealth() {
        return stats.getOrDefault("health", 0.0);
    }
    
    public void setHealth(double health) {
        stats.put("health", health);
    }
    
    public double getSpeed() {
        return stats.getOrDefault("speed", 0.0);
    }
    
    public void setSpeed(double speed) {
        stats.put("speed", speed);
    }
    
    // Magic statistiky
    public double getIntelligence() {
        return stats.getOrDefault("intelligence", 0.0);
    }
    
    public void setIntelligence(double intelligence) {
        stats.put("intelligence", intelligence);
    }
    
    public double getMana() {
        return stats.getOrDefault("mana", 0.0);
    }
    
    public void setMana(double mana) {
        stats.put("mana", mana);
    }
    
    // Skill-specific statistiky
    public double getMiningSpeed() {
        return stats.getOrDefault("mining_speed", 0.0);
    }
    
    public void setMiningSpeed(double miningSpeed) {
        stats.put("mining_speed", miningSpeed);
    }
    
    public double getMiningFortune() {
        return stats.getOrDefault("mining_fortune", 0.0);
    }
    
    public void setMiningFortune(double miningFortune) {
        stats.put("mining_fortune", miningFortune);
    }
    
    public double getFarmingFortune() {
        return stats.getOrDefault("farming_fortune", 0.0);
    }
    
    public void setFarmingFortune(double farmingFortune) {
        stats.put("farming_fortune", farmingFortune);
    }
    
    public double getForagingFortune() {
        return stats.getOrDefault("foraging_fortune", 0.0);
    }
    
    public void setForagingFortune(double foragingFortune) {
        stats.put("foraging_fortune", foragingFortune);
    }
    
    public double getFishingSpeed() {
        return stats.getOrDefault("fishing_speed", 0.0);
    }
    
    public void setFishingSpeed(double fishingSpeed) {
        stats.put("fishing_speed", fishingSpeed);
    }
    
    public double getSeaCreatureChance() {
        return stats.getOrDefault("sea_creature_chance", 0.0);
    }
    
    public void setSeaCreatureChance(double seaCreatureChance) {
        stats.put("sea_creature_chance", seaCreatureChance);
    }
    
    // Utility metody
    public double getStat(String statName) {
        return stats.getOrDefault(statName, 0.0);
    }
    
    public void setStat(String statName, double value) {
        stats.put(statName, value);
    }
    
    public boolean hasStat(String statName) {
        return stats.containsKey(statName) && stats.get(statName) > 0;
    }
    
    public Map<String, Double> getAllStats() {
        return new HashMap<>(stats);
    }
    
    public void addStats(ItemStats other) {
        for (Map.Entry<String, Double> entry : other.stats.entrySet()) {
            double currentValue = stats.getOrDefault(entry.getKey(), 0.0);
            stats.put(entry.getKey(), currentValue + entry.getValue());
        }
    }
    
    public void multiplyStats(double multiplier) {
        for (Map.Entry<String, Double> entry : stats.entrySet()) {
            stats.put(entry.getKey(), entry.getValue() * multiplier);
        }
    }
    
    public ItemStats copy() {
        return new ItemStats(this.stats);
    }
    
    public boolean isEmpty() {
        return stats.isEmpty() || stats.values().stream().allMatch(value -> value == 0.0);
    }
    
    public void clear() {
        stats.clear();
    }
    
    @Override
    public String toString() {
        return "ItemStats{" + stats + "}";
    }
}
