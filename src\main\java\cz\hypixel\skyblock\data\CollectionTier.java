package cz.hypixel.skyblock.data;

import java.util.ArrayList;
import java.util.List;

/**
 * Data třída pro tier kolekce
 */
public class CollectionTier {
    
    private final int tier;
    private final long requiredAmount;
    private final List<String> rewards;
    
    public CollectionTier(int tier, long requiredAmount, List<String> rewards) {
        this.tier = tier;
        this.requiredAmount = requiredAmount;
        this.rewards = new ArrayList<>(rewards);
    }
    
    public int getTier() {
        return tier;
    }
    
    public long getRequiredAmount() {
        return requiredAmount;
    }
    
    public List<String> getRewards() {
        return new ArrayList<>(rewards);
    }
    
    public String getFormattedAmount() {
        if (requiredAmount >= 1_000_000) {
            return String.format("%.1fM", requiredAmount / 1_000_000.0);
        } else if (requiredAmount >= 1_000) {
            return String.format("%.1fk", requiredAmount / 1_000.0);
        } else {
            return String.valueOf(requiredAmount);
        }
    }
    
    public String getRomanNumeral() {
        switch (tier) {
            case 1: return "I";
            case 2: return "II";
            case 3: return "III";
            case 4: return "IV";
            case 5: return "V";
            case 6: return "VI";
            case 7: return "VII";
            case 8: return "VIII";
            case 9: return "IX";
            case 10: return "X";
            case 11: return "XI";
            case 12: return "XII";
            default: return String.valueOf(tier);
        }
    }
    
    @Override
    public String toString() {
        return "CollectionTier{" +
                "tier=" + tier +
                ", requiredAmount=" + requiredAmount +
                ", rewards=" + rewards.size() +
                '}';
    }
}
