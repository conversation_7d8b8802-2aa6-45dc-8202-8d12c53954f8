package cz.hypixel.skyblock.managers;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import cz.hypixel.skyblock.HypixelSkyBlockCZ;
import cz.hypixel.skyblock.database.PlayerData;

import javax.sql.DataSource;

import java.io.File;
import java.sql.*;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.logging.Logger;

/**
 * Manager pro správu dat hráčů s SQLite databází
 */
public class PlayerDataManager {

    private final HypixelSkyBlockCZ plugin;
    private final Logger logger;
    private final HikariDataSource dataSource;

    public PlayerDataManager(HypixelSkyBlockCZ plugin) {
        this.plugin = plugin;
        this.logger = plugin.getLogger();
        this.dataSource = initializeDatabase();

        createTables();
        logger.info("PlayerDataManager inicializován s SQLite databází");
    }

    /**
     * Inicializuje SQLite databázi
     */
    private HikariDataSource initializeDatabase() {
        File dataFolder = plugin.getDataFolder();
        if (!dataFolder.exists()) {
            dataFolder.mkdirs();
        }

        File dbFile = new File(dataFolder, "skyblock.db");
        String jdbcUrl = "jdbc:sqlite:" + dbFile.getAbsolutePath();

        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(jdbcUrl);
        config.setMaximumPoolSize(10);
        config.setMinimumIdle(2);
        config.setConnectionTimeout(30000);
        config.setIdleTimeout(600000);
        config.setMaxLifetime(1800000);

        return new HikariDataSource(config);
    }

    /**
     * Vytvoří databázové tabulky
     */
    private void createTables() {
        // Základní tabulky
        createBasicTables();

        // Tabulky pro ostrovy
        createIslandTables();

        // Tabulky pro ekonomiku
        createEconomyTables();

        // Tabulky pro auction house a bazaar
        createMarketTables();

        logger.info("Všechny databázové tabulky vytvořeny/ověřeny");
    }

    /**
     * Vytvoří základní tabulky pro hráče, skills a collections
     */
    private void createBasicTables() {
        String createPlayersTable = """
            CREATE TABLE IF NOT EXISTS players (
                uuid TEXT PRIMARY KEY,
                username TEXT NOT NULL,
                hypixel_uuid TEXT,
                coins REAL DEFAULT 100.0,
                bank_coins REAL DEFAULT 0.0,
                skyblock_level INTEGER DEFAULT 1,
                skyblock_experience REAL DEFAULT 0,
                island_created BOOLEAN DEFAULT FALSE,
                last_seen INTEGER,
                first_join INTEGER,
                created_at INTEGER DEFAULT (strftime('%s', 'now')),
                updated_at INTEGER DEFAULT (strftime('%s', 'now'))
            )
        """;

        String createSkillsTable = """
            CREATE TABLE IF NOT EXISTS player_skills (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_uuid TEXT NOT NULL,
                skill_name TEXT NOT NULL,
                level INTEGER DEFAULT 0,
                experience REAL DEFAULT 0,
                updated_at INTEGER DEFAULT (strftime('%s', 'now')),
                FOREIGN KEY (player_uuid) REFERENCES players(uuid),
                UNIQUE(player_uuid, skill_name)
            )
        """;

        String createCollectionsTable = """
            CREATE TABLE IF NOT EXISTS player_collections (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_uuid TEXT NOT NULL,
                collection_category TEXT NOT NULL,
                item_name TEXT NOT NULL,
                amount INTEGER DEFAULT 0,
                tier_unlocked INTEGER DEFAULT 0,
                updated_at INTEGER DEFAULT (strftime('%s', 'now')),
                FOREIGN KEY (player_uuid) REFERENCES players(uuid),
                UNIQUE(player_uuid, item_name)
            )
        """;

        try (Connection conn = dataSource.getConnection()) {
            try (Statement stmt = conn.createStatement()) {
                stmt.execute(createPlayersTable);
                stmt.execute(createSkillsTable);
                stmt.execute(createCollectionsTable);
            }
        } catch (SQLException e) {
            logger.severe("Chyba při vytváření základních tabulek: " + e.getMessage());
        }
    }

    /**
     * Vytvoří tabulky pro správu ostrovů
     */
    private void createIslandTables() {
        String createIslandsTable = """
            CREATE TABLE IF NOT EXISTS islands (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                owner_uuid TEXT NOT NULL,
                island_name TEXT,
                world_name TEXT NOT NULL,
                spawn_x REAL DEFAULT 0,
                spawn_y REAL DEFAULT 100,
                spawn_z REAL DEFAULT 0,
                size INTEGER DEFAULT 100,
                created_at INTEGER DEFAULT (strftime('%s', 'now')),
                updated_at INTEGER DEFAULT (strftime('%s', 'now')),
                FOREIGN KEY (owner_uuid) REFERENCES players(uuid)
            )
        """;

        String createIslandMembersTable = """
            CREATE TABLE IF NOT EXISTS island_members (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                island_id INTEGER NOT NULL,
                player_uuid TEXT NOT NULL,
                role TEXT DEFAULT 'MEMBER',
                joined_at INTEGER DEFAULT (strftime('%s', 'now')),
                FOREIGN KEY (island_id) REFERENCES islands(id),
                FOREIGN KEY (player_uuid) REFERENCES players(uuid),
                UNIQUE(island_id, player_uuid)
            )
        """;

        try (Connection conn = dataSource.getConnection()) {
            try (Statement stmt = conn.createStatement()) {
                stmt.execute(createIslandsTable);
                stmt.execute(createIslandMembersTable);
            }
        } catch (SQLException e) {
            logger.severe("Chyba při vytváření tabulek ostrovů: " + e.getMessage());
        }
    }

    /**
     * Vytvoří tabulky pro ekonomický systém
     */
    private void createEconomyTables() {
        String createTransactionsTable = """
            CREATE TABLE IF NOT EXISTS transactions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_uuid TEXT NOT NULL,
                transaction_type TEXT NOT NULL,
                amount REAL NOT NULL,
                description TEXT,
                created_at INTEGER DEFAULT (strftime('%s', 'now')),
                FOREIGN KEY (player_uuid) REFERENCES players(uuid)
            )
        """;

        String createBankTable = """
            CREATE TABLE IF NOT EXISTS bank_accounts (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_uuid TEXT NOT NULL,
                account_type TEXT DEFAULT 'PERSONAL',
                balance REAL DEFAULT 0.0,
                interest_rate REAL DEFAULT 0.02,
                last_interest INTEGER DEFAULT (strftime('%s', 'now')),
                created_at INTEGER DEFAULT (strftime('%s', 'now')),
                FOREIGN KEY (player_uuid) REFERENCES players(uuid)
            )
        """;

        try (Connection conn = dataSource.getConnection()) {
            try (Statement stmt = conn.createStatement()) {
                stmt.execute(createTransactionsTable);
                stmt.execute(createBankTable);
            }
        } catch (SQLException e) {
            logger.severe("Chyba při vytváření ekonomických tabulek: " + e.getMessage());
        }
    }

    /**
     * Vytvoří tabulky pro Auction House a Bazaar
     */
    private void createMarketTables() {
        String createAuctionsTable = """
            CREATE TABLE IF NOT EXISTS auctions (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                seller_uuid TEXT NOT NULL,
                item_data TEXT NOT NULL,
                item_name TEXT NOT NULL,
                category TEXT NOT NULL,
                starting_bid REAL NOT NULL,
                current_bid REAL DEFAULT 0,
                highest_bidder_uuid TEXT,
                buy_it_now BOOLEAN DEFAULT FALSE,
                bin_price REAL,
                duration_hours INTEGER NOT NULL,
                created_at INTEGER DEFAULT (strftime('%s', 'now')),
                ends_at INTEGER NOT NULL,
                status TEXT DEFAULT 'ACTIVE',
                FOREIGN KEY (seller_uuid) REFERENCES players(uuid)
            )
        """;

        String createBidsTable = """
            CREATE TABLE IF NOT EXISTS auction_bids (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                auction_id INTEGER NOT NULL,
                bidder_uuid TEXT NOT NULL,
                bid_amount REAL NOT NULL,
                created_at INTEGER DEFAULT (strftime('%s', 'now')),
                FOREIGN KEY (auction_id) REFERENCES auctions(id),
                FOREIGN KEY (bidder_uuid) REFERENCES players(uuid)
            )
        """;

        String createBazaarTable = """
            CREATE TABLE IF NOT EXISTS bazaar_orders (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                player_uuid TEXT NOT NULL,
                item_name TEXT NOT NULL,
                order_type TEXT NOT NULL,
                amount INTEGER NOT NULL,
                price_per_unit REAL NOT NULL,
                filled_amount INTEGER DEFAULT 0,
                status TEXT DEFAULT 'ACTIVE',
                created_at INTEGER DEFAULT (strftime('%s', 'now')),
                updated_at INTEGER DEFAULT (strftime('%s', 'now')),
                FOREIGN KEY (player_uuid) REFERENCES players(uuid)
            )
        """;

        try (Connection conn = dataSource.getConnection()) {
            try (Statement stmt = conn.createStatement()) {
                stmt.execute(createAuctionsTable);
                stmt.execute(createBidsTable);
                stmt.execute(createBazaarTable);
            }
        } catch (SQLException e) {
            logger.severe("Chyba při vytváření tržních tabulek: " + e.getMessage());
        }
    }

    /**
     * Načte data hráče z databáze
     */
    public CompletableFuture<PlayerData> loadPlayerData(UUID playerUuid) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = "SELECT * FROM players WHERE uuid = ?";

            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {

                stmt.setString(1, playerUuid.toString());

                try (ResultSet rs = stmt.executeQuery()) {
                    if (rs.next()) {
                        PlayerData data = new PlayerData();
                        data.setUuid(rs.getString("uuid"));
                        data.setUsername(rs.getString("username"));
                        data.setHypixelUuid(rs.getString("hypixel_uuid"));
                        data.setCoins(rs.getDouble("coins"));
                        data.setLevel(rs.getInt("level"));
                        data.setExperience(rs.getDouble("experience"));
                        data.setIslandCreated(rs.getBoolean("island_created"));
                        data.setLastSeen(rs.getLong("last_seen"));
                        data.setFirstJoin(rs.getLong("first_join"));
                        data.setCreatedAt(rs.getLong("created_at"));
                        data.setUpdatedAt(rs.getLong("updated_at"));

                        // Načti skills a collections
                        loadPlayerSkills(data);
                        loadPlayerCollections(data);

                        return data;
                    }
                }
            } catch (SQLException e) {
                logger.severe("Chyba při načítání dat hráče: " + e.getMessage());
            }

            return null;
        });
    }

    /**
     * Uloží data hráče do databáze
     */
    public CompletableFuture<Boolean> savePlayerData(PlayerData playerData) {
        return CompletableFuture.supplyAsync(() -> {
            String sql = """
                INSERT OR REPLACE INTO players
                (uuid, username, hypixel_uuid, coins, level, experience, island_created,
                 last_seen, first_join, created_at, updated_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """;

            try (Connection conn = dataSource.getConnection();
                 PreparedStatement stmt = conn.prepareStatement(sql)) {

                stmt.setString(1, playerData.getUuid());
                stmt.setString(2, playerData.getUsername());
                stmt.setString(3, playerData.getHypixelUuid());
                stmt.setDouble(4, playerData.getCoins());
                stmt.setInt(5, playerData.getLevel());
                stmt.setDouble(6, playerData.getExperience());
                stmt.setBoolean(7, playerData.isIslandCreated());
                stmt.setLong(8, playerData.getLastSeen());
                stmt.setLong(9, playerData.getFirstJoin());
                stmt.setLong(10, playerData.getCreatedAt());
                stmt.setLong(11, System.currentTimeMillis());

                int result = stmt.executeUpdate();

                if (result > 0) {
                    // Ulož skills a collections
                    savePlayerSkills(playerData);
                    savePlayerCollections(playerData);
                    return true;
                }

            } catch (SQLException e) {
                logger.severe("Chyba při ukládání dat hráče: " + e.getMessage());
            }

            return false;
        });
    }

    /**
     * Načte skills hráče
     */
    private void loadPlayerSkills(PlayerData playerData) {
        String sql = "SELECT skill_name, level, experience FROM player_skills WHERE player_uuid = ?";

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, playerData.getUuid());

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String skillName = rs.getString("skill_name");
                    int level = rs.getInt("level");
                    double experience = rs.getDouble("experience");

                    playerData.setSkill(skillName, level, experience);
                }
            }
        } catch (SQLException e) {
            logger.warning("Chyba při načítání skills: " + e.getMessage());
        }
    }

    /**
     * Načte collections hráče
     */
    private void loadPlayerCollections(PlayerData playerData) {
        String sql = "SELECT item_name, amount FROM player_collections WHERE player_uuid = ?";

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            stmt.setString(1, playerData.getUuid());

            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    String itemName = rs.getString("item_name");
                    long amount = rs.getLong("amount");

                    playerData.getCollections().put(itemName, amount);
                }
            }
        } catch (SQLException e) {
            logger.warning("Chyba při načítání collections: " + e.getMessage());
        }
    }

    /**
     * Uloží skills hráče
     */
    private void savePlayerSkills(PlayerData playerData) {
        String sql = """
            INSERT OR REPLACE INTO player_skills
            (player_uuid, skill_name, level, experience, updated_at)
            VALUES (?, ?, ?, ?, ?)
        """;

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            for (PlayerData.SkillData skill : playerData.getSkills().values()) {
                stmt.setString(1, playerData.getUuid());
                stmt.setString(2, skill.getName());
                stmt.setInt(3, skill.getLevel());
                stmt.setDouble(4, skill.getExperience());
                stmt.setLong(5, System.currentTimeMillis());
                stmt.addBatch();
            }

            stmt.executeBatch();
        } catch (SQLException e) {
            logger.warning("Chyba při ukládání skills: " + e.getMessage());
        }
    }

    /**
     * Uloží collections hráče
     */
    private void savePlayerCollections(PlayerData playerData) {
        String sql = """
            INSERT OR REPLACE INTO player_collections
            (player_uuid, item_name, amount, updated_at)
            VALUES (?, ?, ?, ?)
        """;

        try (Connection conn = dataSource.getConnection();
             PreparedStatement stmt = conn.prepareStatement(sql)) {

            for (Map.Entry<String, Long> entry : playerData.getCollections().entrySet()) {
                stmt.setString(1, playerData.getUuid());
                stmt.setString(2, entry.getKey());
                stmt.setLong(3, entry.getValue());
                stmt.setLong(4, System.currentTimeMillis());
                stmt.addBatch();
            }

            stmt.executeBatch();
        } catch (SQLException e) {
            logger.warning("Chyba při ukládání collections: " + e.getMessage());
        }
    }

    /**
     * Vytvoří nového hráče v databázi
     */
    public CompletableFuture<PlayerData> createPlayer(UUID playerUuid, String username) {
        return CompletableFuture.supplyAsync(() -> {
            PlayerData playerData = new PlayerData(playerUuid.toString(), username);

            if (savePlayerData(playerData).join()) {
                logger.info("Vytvořen nový hráč: " + username + " (" + playerUuid + ")");
                return playerData;
            }

            return null;
        });
    }

    /**
     * Uloží všechna data (pro shutdown)
     */
    public void saveAllData() {
        logger.info("Ukládání všech dat hráčů...");
        // V této implementaci se data ukládají průběžně
        // Tato metoda může být použita pro flush cache apod.
    }

    /**
     * Ukončí databázové připojení
     */
    public void shutdown() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
            logger.info("Databázové připojení ukončeno");
        }
    }

    /**
     * Získá DataSource pro ostatní managery
     */
    public DataSource getDataSource() {
        return dataSource;
    }
}
