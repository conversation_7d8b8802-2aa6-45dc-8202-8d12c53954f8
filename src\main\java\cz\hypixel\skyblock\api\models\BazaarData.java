package cz.hypixel.skyblock.api.models;

import com.google.gson.annotations.SerializedName;
import java.util.List;
import java.util.Map;

/**
 * Model pro bazaar data z Hypixel API
 */
public class BazaarData {
    
    private boolean success;
    
    @SerializedName("lastUpdated")
    private long lastUpdated;
    
    private Map<String, Product> products;
    
    // Gettery
    public boolean isSuccess() {
        return success;
    }
    
    public long getLastUpdated() {
        return lastUpdated;
    }
    
    public Map<String, Product> getProducts() {
        return products;
    }
    
    /**
     * Získá produkt podle ID
     */
    public Product getProduct(String productId) {
        return products != null ? products.get(productId.toUpperCase()) : null;
    }
    
    /**
     * Model pro produkt v bazaaru
     */
    public static class Product {
        
        @SerializedName("product_id")
        private String productId;
        
        @SerializedName("sell_summary")
        private List<Order> sellSummary;
        
        @SerializedName("buy_summary")
        private List<Order> buySummary;
        
        @SerializedName("quick_status")
        private QuickStatus quickStatus;
        
        // Gettery
        public String getProductId() {
            return productId;
        }
        
        public List<Order> getSellSummary() {
            return sellSummary;
        }
        
        public List<Order> getBuySummary() {
            return buySummary;
        }
        
        public QuickStatus getQuickStatus() {
            return quickStatus;
        }
        
        /**
         * Získá nejlepší sell cenu (instant sell)
         */
        public double getBestSellPrice() {
            if (buySummary == null || buySummary.isEmpty()) {
                return 0.0;
            }
            return buySummary.get(0).getPricePerUnit();
        }
        
        /**
         * Získá nejlepší buy cenu (instant buy)
         */
        public double getBestBuyPrice() {
            if (sellSummary == null || sellSummary.isEmpty()) {
                return 0.0;
            }
            return sellSummary.get(0).getPricePerUnit();
        }
    }
    
    /**
     * Model pro order v bazaaru
     */
    public static class Order {
        
        private int amount;
        
        @SerializedName("pricePerUnit")
        private double pricePerUnit;
        
        private int orders;
        
        // Gettery
        public int getAmount() {
            return amount;
        }
        
        public double getPricePerUnit() {
            return pricePerUnit;
        }
        
        public int getOrders() {
            return orders;
        }
        
        /**
         * Získá celkovou cenu
         */
        public double getTotalPrice() {
            return amount * pricePerUnit;
        }
    }
    
    /**
     * Model pro quick status
     */
    public static class QuickStatus {
        
        @SerializedName("productId")
        private String productId;
        
        @SerializedName("sellPrice")
        private double sellPrice;
        
        @SerializedName("sellVolume")
        private long sellVolume;
        
        @SerializedName("sellMovingWeek")
        private long sellMovingWeek;
        
        @SerializedName("sellOrders")
        private int sellOrders;
        
        @SerializedName("buyPrice")
        private double buyPrice;
        
        @SerializedName("buyVolume")
        private long buyVolume;
        
        @SerializedName("buyMovingWeek")
        private long buyMovingWeek;
        
        @SerializedName("buyOrders")
        private int buyOrders;
        
        // Gettery
        public String getProductId() {
            return productId;
        }
        
        public double getSellPrice() {
            return sellPrice;
        }
        
        public long getSellVolume() {
            return sellVolume;
        }
        
        public long getSellMovingWeek() {
            return sellMovingWeek;
        }
        
        public int getSellOrders() {
            return sellOrders;
        }
        
        public double getBuyPrice() {
            return buyPrice;
        }
        
        public long getBuyVolume() {
            return buyVolume;
        }
        
        public long getBuyMovingWeek() {
            return buyMovingWeek;
        }
        
        public int getBuyOrders() {
            return buyOrders;
        }
        
        /**
         * Získá profit margin (rozdíl mezi buy a sell cenou)
         */
        public double getProfitMargin() {
            return sellPrice - buyPrice;
        }
        
        /**
         * Získá profit margin v procentech
         */
        public double getProfitMarginPercent() {
            if (buyPrice == 0) return 0;
            return (getProfitMargin() / buyPrice) * 100;
        }
    }
}
