package cz.hypixel.skyblock.data;

import org.bukkit.Material;

import java.util.HashMap;
import java.util.Map;

/**
 * Enum pro typy ma<PERSON>l<PERSON>ů podle Hypixel SkyBlock
 */
public enum PetType {
    
    // Combat pets
    WOLF("Wolf", "§6", Material.BONE, "combat") {
        @Override
        public Map<String, Double> getBaseBonuses() {
            Map<String, Double> bonuses = new HashMap<>();
            bonuses.put("strength", 5.0);
            bonuses.put("crit_damage", 10.0);
            return bonuses;
        }
        
        @Override
        public String[] getAbilities(int level, PetRarity rarity) {
            return new String[]{
                "§6Alpha Dog §7- Zvyšuje damage o §c+15%",
                "§6Pack Leader §7- Zvyšuje damage party o §c+10%"
            };
        }
    },
    
    TIGER("Tiger", "§6", Material.ORANGE_DYE, "combat") {
        @Override
        public Map<String, Double> getBaseBonuses() {
            Map<String, Double> bonuses = new HashMap<>();
            bonuses.put("strength", 3.0);
            bonuses.put("crit_chance", 5.0);
            bonuses.put("ferocity", 2.0);
            return bonuses;
        }
        
        @Override
        public String[] getAbilities(int level, PetRarity rarity) {
            return new String[]{
                "§6Merciless Swipe §7- Zvyšuje crit chance",
                "§6Apex Predator §7- Zvyšuje ferocity"
            };
        }
    },
    
    // Mining pets
    MITHRIL_GOLEM("Mithril Golem", "§b", Material.PRISMARINE_CRYSTALS, "mining") {
        @Override
        public Map<String, Double> getBaseBonuses() {
            Map<String, Double> bonuses = new HashMap<>();
            bonuses.put("mining_speed", 10.0);
            bonuses.put("mining_fortune", 5.0);
            return bonuses;
        }
        
        @Override
        public String[] getAbilities(int level, PetRarity rarity) {
            return new String[]{
                "§6Mithril Affinity §7- Zvyšuje mining speed",
                "§6Pickaxe Upgrade §7- Zvyšuje mining fortune"
            };
        }
    },
    
    SILVERFISH("Silverfish", "§7", Material.IRON_NUGGET, "mining") {
        @Override
        public Map<String, Double> getBaseBonuses() {
            Map<String, Double> bonuses = new HashMap<>();
            bonuses.put("mining_speed", 8.0);
            bonuses.put("defense", 5.0);
            return bonuses;
        }
        
        @Override
        public String[] getAbilities(int level, PetRarity rarity) {
            return new String[]{
                "§6True Defense §7- Ignoruje část damage",
                "§6Mining Expertise §7- Zvyšuje mining XP"
            };
        }
    },
    
    // Farming pets
    RABBIT("Rabbit", "§f", Material.RABBIT_FOOT, "farming") {
        @Override
        public Map<String, Double> getBaseBonuses() {
            Map<String, Double> bonuses = new HashMap<>();
            bonuses.put("speed", 10.0);
            bonuses.put("farming_fortune", 5.0);
            return bonuses;
        }
        
        @Override
        public String[] getAbilities(int level, PetRarity rarity) {
            return new String[]{
                "§6Happy Feet §7- Zvyšuje speed",
                "§6Farming Wisdom §7- Zvyšuje farming XP"
            };
        }
    },
    
    BEE("Bee", "§e", Material.HONEYCOMB, "farming") {
        @Override
        public Map<String, Double> getBaseBonuses() {
            Map<String, Double> bonuses = new HashMap<>();
            bonuses.put("farming_fortune", 8.0);
            bonuses.put("speed", 5.0);
            return bonuses;
        }
        
        @Override
        public String[] getAbilities(int level, PetRarity rarity) {
            return new String[]{
                "§6Hive Mind §7- Zvyšuje farming fortune",
                "§6Busy Bee §7- Šance na extra drops"
            };
        }
    },
    
    // Fishing pets
    DOLPHIN("Dolphin", "§b", Material.PRISMARINE_SHARD, "fishing") {
        @Override
        public Map<String, Double> getBaseBonuses() {
            Map<String, Double> bonuses = new HashMap<>();
            bonuses.put("sea_creature_chance", 5.0);
            bonuses.put("fishing_speed", 10.0);
            return bonuses;
        }
        
        @Override
        public String[] getAbilities(int level, PetRarity rarity) {
            return new String[]{
                "§6Pod Tactics §7- Zvyšuje sea creature chance",
                "§6Echolocation §7- Zvyšuje fishing speed"
            };
        }
    },
    
    FLYING_FISH("Flying Fish", "§9", Material.TROPICAL_FISH, "fishing") {
        @Override
        public Map<String, Double> getBaseBonuses() {
            Map<String, Double> bonuses = new HashMap<>();
            bonuses.put("fishing_speed", 15.0);
            bonuses.put("fishing_fortune", 3.0);
            return bonuses;
        }
        
        @Override
        public String[] getAbilities(int level, PetRarity rarity) {
            return new String[]{
                "§6Quick Reel §7- Rychlejší fishing",
                "§6Water Bending §7- Šance na extra fish"
            };
        }
    },
    
    // Foraging pets
    MONKEY("Monkey", "§6", Material.JUNGLE_LOG, "foraging") {
        @Override
        public Map<String, Double> getBaseBonuses() {
            Map<String, Double> bonuses = new HashMap<>();
            bonuses.put("foraging_fortune", 8.0);
            bonuses.put("speed", 5.0);
            return bonuses;
        }
        
        @Override
        public String[] getAbilities(int level, PetRarity rarity) {
            return new String[]{
                "§6Vine Swing §7- Zvyšuje speed v jungle",
                "§6Banana Finder §7- Šance na extra logs"
            };
        }
    },
    
    // Utility pets
    PARROT("Parrot", "§a", Material.FEATHER, "utility") {
        @Override
        public Map<String, Double> getBaseBonuses() {
            Map<String, Double> bonuses = new HashMap<>();
            bonuses.put("intelligence", 10.0);
            bonuses.put("crit_damage", 5.0);
            return bonuses;
        }
        
        @Override
        public String[] getAbilities(int level, PetRarity rarity) {
            return new String[]{
                "§6Imitator §7- Kopíruje schopnosti jiných pets",
                "§6Flamboyant §7- Zvyšuje všechny stats"
            };
        }
    };
    
    private final String displayName;
    private final String colorCode;
    private final Material displayMaterial;
    private final String category;
    
    PetType(String displayName, String colorCode, Material displayMaterial, String category) {
        this.displayName = displayName;
        this.colorCode = colorCode;
        this.displayMaterial = displayMaterial;
        this.category = category;
    }
    
    public String getDisplayName() {
        return displayName;
    }
    
    public String getColorCode() {
        return colorCode;
    }
    
    public String getColoredName() {
        return colorCode + displayName;
    }
    
    public Material getDisplayMaterial() {
        return displayMaterial;
    }
    
    public String getCategory() {
        return category;
    }
    
    /**
     * Získá základní stat bonusy (implementováno v každém typu)
     */
    public abstract Map<String, Double> getBaseBonuses();
    
    /**
     * Získá schopnosti mazlíčka (implementováno v každém typu)
     */
    public abstract String[] getAbilities(int level, PetRarity rarity);
    
    /**
     * Získá typ podle názvu
     */
    public static PetType fromString(String name) {
        for (PetType type : values()) {
            if (type.name().equalsIgnoreCase(name) || 
                type.displayName.equalsIgnoreCase(name)) {
                return type;
            }
        }
        return null;
    }
    
    /**
     * Získá všechny typy podle kategorie
     */
    public static PetType[] getByCategory(String category) {
        return java.util.Arrays.stream(values())
            .filter(type -> type.category.equalsIgnoreCase(category))
            .toArray(PetType[]::new);
    }
}
